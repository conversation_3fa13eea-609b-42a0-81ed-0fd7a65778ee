import { expect, test } from "@playwright/test";

import { TEST_EMAIL_DOMAIN } from "./utils/constants";
import { createTestUser, deleteTestUserByEmail } from "./utils/db.helper";
import {
  clickLoginLink,
  logout,
  verifyLoggedOut,
} from "./utils/navigation.helper";

test.describe("Client Signup", () => {
  let testEmail: string | null = null;

  test.afterEach(async () => {
    // Clean up the user if it was created
    if (testEmail) {
      await deleteTestUserByEmail(testEmail);
      testEmail = null;
    }
  });

  test("client can sign up via UI", async ({ page }) => {
    const uniqueId = Date.now();
    const email = `test-client-${uniqueId}${TEST_EMAIL_DOMAIN}`;
    const password = `Password${uniqueId}!`;
    const fullName = `Test Client ${uniqueId}`;
    const phoneNumber = `+65-1234-${uniqueId.toString().slice(-4)}`;

    await page.goto("/signup-client");
    await page.waitForLoadState("networkidle");

    // Fill signup form
    await page.getByLabel("Full Name").fill(fullName);
    await page.getByLabel("Email").fill(email);
    await page.getByLabel("Password", { exact: true }).fill(password);
    await page.getByLabel("Confirm Password").fill(password);
    await page.getByLabel("Company Name").fill(`Company ${uniqueId}`);
    await page.getByLabel("Location").fill(`Location ${uniqueId}`);

    // Submit
    await page.getByRole("button", { name: "Create Client Account" }).click();

    // Should redirect to profile page
    await page.waitForURL("/profile");

    // Verify initial profile shows "Not provided" for phone
    await expect(page.getByText("Not provided")).toBeVisible();

    // Navigate to edit profile page
    await page.getByRole("link", { name: "Edit Profile" }).click();
    await page.waitForURL("/profile/edit");

    // Fill phone number field
    await page.getByLabel("Phone Number").fill(phoneNumber);

    // Submit the form
    await page.getByRole("button", { name: "Save Changes" }).click();

    // Should redirect back to profile page
    await page.waitForURL("/profile");

    // Verify phone number is displayed correctly
    await expect(page.getByText(phoneNumber)).toBeVisible();

    // Test logout functionality
    await logout(page);

    // Should redirect to home page
    await page.waitForURL("/");

    // Verify we're logged out
    await verifyLoggedOut(page);

    // Test logging back in
    await clickLoginLink(page);
    await page.waitForURL("/login?type=client");

    // Login with the same credentials
    await page.getByLabel("Email").fill(email);
    await page.getByLabel("Password", { exact: true }).fill(password);
    await page.getByRole("button", { name: "Sign in" }).click();

    // Should redirect to profile page
    await page.waitForURL("/profile");

    // Verify we're logged in again
    await expect(page.getByText(fullName)).toBeVisible();

    // Track email for cleanup
    testEmail = email;
  });

  test("duplicate email shows error", async ({ page }) => {
    const uniqueId = Date.now();
    const email = `test-client-${uniqueId}${TEST_EMAIL_DOMAIN}`;
    const password = `Password${uniqueId}!`;
    const fullName = `Test Client ${uniqueId}`;

    // Create the first user directly in the database
    const existingUser = await createTestUser({
      email,
      password,
      fullName,
      userType: "client",
      companyName: `Company ${uniqueId}`,
      location: `Location ${uniqueId}`,
    });

    // Track the existing user for cleanup
    testEmail = existingUser.email;

    // Try to sign up again with same email
    await page.goto("/signup-client");

    // Clear and fill fields to ensure they're properly set
    const nameField = page.getByLabel("Full Name");
    await nameField.clear();
    await nameField.fill(`Another ${fullName}`);

    const emailField = page.getByLabel("Email");
    await emailField.clear();
    await emailField.fill(email);

    const passwordField = page.getByLabel("Password", { exact: true });
    await passwordField.clear();
    await passwordField.fill(`Another${password}`);

    const confirmPasswordField = page.getByLabel("Confirm Password");
    await confirmPasswordField.clear();
    await confirmPasswordField.fill(`Another${password}`);

    const companyField = page.getByLabel("Company Name");
    await companyField.clear();
    await companyField.fill(`Another Company ${uniqueId}`);

    const locationField = page.getByLabel("Location");
    await locationField.clear();
    await locationField.fill(`Another Location ${uniqueId}`);

    await page.getByRole("button", { name: "Create Client Account" }).click();

    await page.getByText(/User already registered/).waitFor();
  });

  test("password mismatch shows error", async ({ page }) => {
    const uniqueId = Date.now();
    const email = `test-client-${uniqueId}${TEST_EMAIL_DOMAIN}`;
    const password = `Password${uniqueId}!`;
    const fullName = `Test Client ${uniqueId}`;

    await page.goto("/signup-client");
    await page.waitForLoadState("networkidle");

    // Fill signup form with mismatched passwords
    await page.getByLabel("Full Name").fill(fullName);
    await page.getByLabel("Email").fill(email);
    await page.getByLabel("Password", { exact: true }).fill(password);
    await page.getByLabel("Confirm Password").fill(`Different${password}`);
    await page.getByLabel("Company Name").fill(`Company ${uniqueId}`);
    await page.getByLabel("Location").fill(`Location ${uniqueId}`);

    // Submit should show validation error
    await page.getByRole("button", { name: "Create Client Account" }).click();

    // Should show password mismatch error
    await expect(page.getByText("Passwords don't match")).toBeVisible();
  });
});
