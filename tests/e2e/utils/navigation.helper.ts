import type { Page } from "@playwright/test";
import { expect } from "@playwright/test";

/**
 * Helper to check if we're in mobile viewport
 */
export function isMobileViewport(page: Page): boolean {
  const viewport = page.viewportSize();
  return viewport ? viewport.width < 768 : false;
}

/**
 * Navigates to a page by clicking a navbar link, handling both mobile and desktop viewports.
 *
 * On mobile, opens the menu before clicking the specified link. Waits for the page to finish loading after navigation.
 *
 * @param linkName - The exact text of the navbar link to click
 */
export async function navigateViaNavbar(page: Page, linkName: string) {
  if (isMobileViewport(page)) {
    // On mobile, open the menu first, then click the link
    await page.getByRole("button", { name: "Toggle menu" }).click();
    await page.getByRole("link", { name: linkName, exact: true }).click();
  } else {
    // On desktop, click the link directly in the navbar
    await page.getByRole("link", { name: linkName, exact: true }).click();
  }

  // Wait for navigation to complete
  await page.waitForLoadState("networkidle");
}

/**
 * Helper to access user menu (for logout) that works for both desktop and mobile
 */
export async function openUserMenu(page: Page) {
  if (isMobileViewport(page)) {
    // On mobile, open the menu first
    await page.getByRole("button", { name: "Toggle menu" }).click();
    // The user info and logout button are directly in the sheet
  } else {
    // On desktop, click the user menu button
    await page.getByTestId("user-menu-button").click();
  }
}

/**
 * Helper to logout that works for both desktop and mobile
 */
export async function logout(page: Page) {
  await openUserMenu(page);

  if (isMobileViewport(page)) {
    // On mobile, click the Log Out button directly
    await page.getByRole("button", { name: "Log Out" }).click();
  } else {
    // On desktop, click the Log Out menu item
    await page.getByRole("menuitem", { name: "Log Out" }).click();
  }
}

/**
 * Helper to click login link that works for both desktop and mobile (handles case differences)
 */
export async function clickLoginLink(page: Page) {
  if (isMobileViewport(page)) {
    // On mobile, open the menu first
    await page.getByRole("button", { name: "Toggle menu" }).click();
    // Wait for the login link to be visible
    await page.getByRole("link", { name: "Log in" }).waitFor();
    // Mobile uses lowercase "Log in"
    await page.getByRole("link", { name: "Log in" }).click();
  } else {
    // Desktop uses title case "Log In"
    await page.getByRole("link", { name: "Log In" }).click();
  }
}

/**
 * Helper to get a handle to navbar links so their DOM can be checked
 * Returns an object with the link locator and a close navbar function
 *
 * @param page - the page context
 * @param linkName - The exact text of the navbar link
 * @returns Object with link locator and close function
 */
export async function viewNavbarLink(page: Page, linkName: string) {
  if (isMobileViewport(page)) {
    // On mobile, open the menu first to access the nav links
    await page.getByRole("button", { name: "Toggle menu" }).click();
  }

  const linkLocator = page.getByRole("link", { name: linkName, exact: true });

  return {
    link: linkLocator,
    close: async () => {
      if (isMobileViewport(page)) {
        // Close the mobile menu
        await page.getByRole("button", { name: "Close" }).click();
      }
    },
  };
}

/**
 * Helper to verify we're logged out - looks for login link/button with proper casing
 */
export async function verifyLoggedOut(page: Page) {
  if (isMobileViewport(page)) {
    // On mobile, we need to open the menu to see the login link
    await page.getByRole("button", { name: "Toggle menu" }).click();
    // Wait for the login link to be visible
    const loginLink = page.getByRole("link", { name: "Log in" });
    await loginLink.waitFor();
    await expect(loginLink).toBeVisible();
    // Close the menu
    await page.getByRole("button", { name: "Close" }).click();
  } else {
    // On desktop, the login link should be visible in the navbar
    await expect(page.getByRole("link", { name: "Log In" })).toBeVisible();
  }
}
