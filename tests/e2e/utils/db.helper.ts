import { createClient } from "@supabase/supabase-js";
import * as dotenv from "dotenv";
import * as path from "path";

import { type Database } from "@/types/database.types";
import type { PricingModel, UserType, WorkshopFormat } from "@/types/types";

// Load environment variables from .env.local
dotenv.config({ path: path.join(__dirname, "../../../.env.local") });

// Initialize Supabase client with service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error(
    "Missing required environment variables: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY",
  );
}

export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  supabaseServiceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  },
);

export interface TestUser {
  id: string;
  email: string;
  password: string;
  fullName: string;
  userType: UserType;
  organizationName?: string;
  companyName?: string;
  location?: string;
  providerId?: string;
}

export async function createTestUser(
  userData: Omit<TestUser, "id" | "providerId">,
): Promise<TestUser> {
  // Create auth user
  const { data: authData, error: authError } =
    await supabaseAdmin.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true,
      user_metadata: {
        full_name: userData.fullName,
      },
    });

  if (authError || !authData.user) {
    throw new Error(`Failed to create auth user: ${authError?.message}`);
  }

  // Create profile
  const profileData = {
    id: authData.user.id,
    email: userData.email,
    full_name: userData.fullName,
    user_type: userData.userType,
    phone_number:
      userData.userType === "provider" ? "**********" : "**********",
  } as const;

  const { error: profileError } = await supabaseAdmin
    .from("profiles")
    .insert(profileData);

  if (profileError) {
    // Clean up auth user if profile creation fails
    await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
    throw new Error(`Failed to create profile: ${profileError.message}`);
  }

  // If client, create client record
  if (userData.userType === "client") {
    const clientData = {
      id: authData.user.id,
      company_name: userData.companyName || "Test Company",
      location: userData.location || "Singapore",
    };

    const { error: clientError } = await supabaseAdmin
      .from("clients")
      .insert(clientData);

    if (clientError) {
      // Clean up profile and auth user if client creation fails
      await supabaseAdmin.from("profiles").delete().eq("id", authData.user.id);
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
      throw new Error(`Failed to create client: ${clientError.message}`);
    }
  }

  let providerId: string | undefined;

  // If provider, create provider organization and link via providers table
  if (userData.userType === "provider" && userData.organizationName) {
    // First create the provider organization
    const { data: orgData, error: orgError } = await supabaseAdmin
      .from("provider_organizations")
      .insert({
        name: userData.organizationName,
        description: `E2E test organization for ${userData.fullName}`,
        city: "Singapore",
        country: "Singapore",
      })
      .select("id")
      .single();

    if (orgError) {
      // Clean up
      await supabaseAdmin.from("profiles").delete().eq("id", authData.user.id);
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
      throw new Error(
        `Failed to create provider organization: ${orgError.message}`,
      );
    }

    // Then create the provider link
    const { error: providerError } = await supabaseAdmin
      .from("providers")
      .insert({
        id: authData.user.id,
        organization_id: orgData.id,
        role: "admin",
      });

    if (providerError) {
      // Clean up organization, profile, and auth user
      await supabaseAdmin
        .from("provider_organizations")
        .delete()
        .eq("id", orgData.id);
      await supabaseAdmin.from("profiles").delete().eq("id", authData.user.id);
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id);
      throw new Error(
        `Failed to create provider link: ${providerError.message}`,
      );
    }

    providerId = orgData.id;
  }

  return {
    id: authData.user.id,
    email: userData.email,
    password: userData.password,
    fullName: userData.fullName,
    userType: userData.userType,
    organizationName: userData.organizationName,
    companyName: userData.companyName,
    location: userData.location,
    providerId,
  };
}

export async function deleteTestUser(userId: string): Promise<void> {
  // Delete in reverse order of creation to handle foreign key constraints

  // Get provider organization if exists
  const { data: providerData } = await supabaseAdmin
    .from("providers")
    .select("organization_id")
    .eq("id", userId)
    .single();

  // Delete any workshops created by this provider's organization
  if (providerData?.organization_id) {
    await supabaseAdmin
      .from("workshops")
      .delete()
      .eq("provider_id", providerData.organization_id);
  }

  // Delete any bookings by this user
  await supabaseAdmin.from("bookings").delete().eq("client_id", userId);

  // Delete provider link if exists
  await supabaseAdmin.from("providers").delete().eq("id", userId);

  // Delete provider organization if exists
  if (providerData?.organization_id) {
    await supabaseAdmin
      .from("provider_organizations")
      .delete()
      .eq("id", providerData.organization_id);
  }

  // Delete client record if exists
  await supabaseAdmin.from("clients").delete().eq("id", userId);

  // Delete profile
  await supabaseAdmin.from("profiles").delete().eq("id", userId);

  // Delete auth user
  const { error } = await supabaseAdmin.auth.admin.deleteUser(userId);
  if (error) {
    console.error(`Failed to delete auth user ${userId}: ${error.message}`);
  }
}

export async function createTestWorkshop(
  providerId: string,
  workshopData: {
    name: string;
    description: string;
    price: number;
    format: WorkshopFormat;
    duration: string;
    min_capacity: number;
    max_capacity: number;
    pricing_model?: PricingModel;
    published?: boolean;
  },
): Promise<string> {
  const { data, error } = await supabaseAdmin
    .from("workshops")
    .insert({
      provider_id: providerId,
      name: workshopData.name,
      description: workshopData.description,
      price: workshopData.price,
      format: workshopData.format,
      duration: workshopData.duration,
      min_capacity: workshopData.min_capacity,
      max_capacity: workshopData.max_capacity,
      pricing_model: workshopData.pricing_model ?? "per_person",
      currency: "SGD",
      lead_time: "7",
      published: workshopData.published ?? true,
    })
    .select("id")
    .single();

  if (error || !data) {
    throw new Error(`Failed to create workshop: ${error?.message}`);
  }

  return data.id;
}

export async function deleteTestWorkshop(workshopId: string): Promise<void> {
  await supabaseAdmin.from("workshops").delete().eq("id", workshopId);
}

export async function deleteTestUserByEmail(email: string): Promise<void> {
  // Find user by email
  const { data: profile } = await supabaseAdmin
    .from("profiles")
    .select("id")
    .eq("email", email)
    .single();

  if (profile?.id) {
    await deleteTestUser(profile.id);
  } else {
    console.warn(`No user found with email: ${email}`);
  }
}
