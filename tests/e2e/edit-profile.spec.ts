import { test } from "@playwright/test";

import { TEST_EMAIL_DOMAIN, TEST_ORGANIZATION_PREFIX } from "./utils/constants";
import {
  createTestUser,
  deleteTestUser,
  type TestUser,
} from "./utils/db.helper";

test.describe("Profile Editing", () => {
  let testProvider: TestUser;
  let testClient: TestUser;

  test.beforeAll(async () => {
    // Create test users for the suite
    const timestamp = Date.now();
    testProvider = await createTestUser({
      email: `test-provider-edit-${timestamp}${TEST_EMAIL_DOMAIN}`,
      password: "Password123!",
      fullName: "Test Provider Edit",
      userType: "provider",
      organizationName: `${TEST_ORGANIZATION_PREFIX} Edit ${timestamp}`,
    });

    testClient = await createTestUser({
      email: `test-client-edit-${timestamp}${TEST_EMAIL_DOMAIN}`,
      password: "Password123!",
      fullName: "Test Client Edit",
      userType: "client",
      companyName: "Test Company",
      location: "Singapore",
    });
  });

  test.afterAll(async () => {
    // Clean up test users
    if (testProvider) {
      await deleteTestUser(testProvider.id);
    }
    if (testClient) {
      await deleteTestUser(testClient.id);
    }
  });

  test("provider can edit profile", async ({ page }) => {
    // Login
    await page.goto("/login");
    await page.waitForLoadState("networkidle");
    await page.getByLabel("Email").fill(testProvider.email);
    await page
      .getByLabel("Password", { exact: true })
      .fill(testProvider.password);
    await page.getByRole("button", { name: "Sign in" }).click();

    // Wait for redirect
    await page.waitForURL("/dashboard");

    // Navigate to profile edit via UI
    await page.getByRole("link", { name: "Edit Profile" }).click();
    await page.waitForURL("/dashboard/edit");
    await page.getByRole("heading", { name: "Edit Profile" }).waitFor();

    // Update values
    const newName = `Updated ${testProvider.fullName}`;
    const newPhone = "**********";

    await page.getByLabel("Full Name").clear();
    await page.getByLabel("Full Name").fill(newName);
    await page.getByLabel("Phone Number").clear();
    await page.getByLabel("Phone Number").fill(newPhone);

    // Save
    await page.getByRole("button", { name: "Save changes" }).click();

    // Should be redirected to profile
    await page.waitForURL("/profile");

    // Verify the change
    await page.getByText(newName).waitFor();
  });

  test("client can edit profile", async ({ page }) => {
    // Login
    await page.goto("/login");
    await page.goto("/login");
    await page.waitForLoadState("networkidle");
    await page.getByLabel("Email").fill(testClient.email);
    await page
      .getByLabel("Password", { exact: true })
      .fill(testClient.password);
    await page.getByRole("button", { name: "Sign in" }).click();

    // Should redirect to profile for clients
    await page.waitForURL("/profile");

    // Navigate to profile edit via UI
    await page.getByRole("link", { name: "Edit Profile" }).click();
    await page.waitForURL("/profile/edit");
    await page.getByRole("heading", { name: "Edit Profile" }).waitFor();

    // Update values
    const newName = `Updated ${testClient.fullName}`;
    const newPhone = "0123456789";

    await page.getByLabel("Full Name").clear();
    await page.getByLabel("Full Name").fill(newName);
    await page.getByLabel("Phone Number").clear();
    await page.getByLabel("Phone Number").fill(newPhone);

    // Save
    await page.getByRole("button", { name: "Save changes" }).click();

    // Should be redirected to profile
    await page.waitForURL("/profile");

    // Verify the change
    await page.getByText(newName).waitFor();
  });
});
