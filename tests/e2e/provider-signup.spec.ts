import { expect, test } from "@playwright/test";

import { TEST_EMAIL_DOMAIN, TEST_ORGANIZATION_PREFIX } from "./utils/constants";
import { deleteTestUserByEmail } from "./utils/db.helper";
import {
  clickLoginLink,
  logout,
  verifyLoggedOut,
} from "./utils/navigation.helper";

test.describe("Provider Signup", () => {
  let testEmail: string | null = null;

  test.afterEach(async () => {
    // Clean up the user if it was created
    if (testEmail) {
      await deleteTestUserByEmail(testEmail);
      testEmail = null;
    }
  });

  test("provider can sign up with valid invite code", async ({ page }) => {
    const uniqueId = Date.now();
    const email = `test-provider-${uniqueId}${TEST_EMAIL_DOMAIN}`;
    const password = `Password${uniqueId}!`;
    const fullName = `Test Provider ${uniqueId}`;
    const organizationName = `${TEST_ORGANIZATION_PREFIX} ${uniqueId}`;
    const phoneNumber = `******-${String(uniqueId).slice(-7)}`;
    const organizationDescription = `This is a test wellness organization providing premium services. Created on ${new Date().toISOString()}`;

    await page.goto("/signup");
    await page.waitForLoadState("networkidle");

    // Step 1: Enter invite code
    await page.getByLabel("Invite Code").fill("SIMPLEVIBE25");
    await page.getByRole("button", { name: "Continue" }).click();

    // Wait for form to appear - check for Full Name field
    await page.getByLabel("Full Name").waitFor();

    // Step 2: Fill personal details
    await page.getByLabel("Full Name").fill(fullName);
    await page.getByLabel("Email").fill(email);
    await page.getByLabel("Password", { exact: true }).fill(password);
    await page.getByLabel("Confirm Password").fill(password);

    // Click Next - be more specific to avoid conflict with dev tools button
    await page
      .getByRole("button", { name: "Next", exact: true })
      .first()
      .click();

    // Wait for organization form - note: UK spelling "Organisation"
    await page.getByLabel("Organisation / Provider Name").waitFor();

    // Step 3: Fill organization details
    await page
      .getByLabel("Organisation / Provider Name")
      .fill(organizationName);

    // Select location (city and country combined)
    await page.getByRole("combobox").click();
    await page.getByRole("option", { name: "Singapore" }).click();

    // Agree to terms and conditions
    await page.getByRole("checkbox").check();

    // Submit
    await page.getByRole("button", { name: "Sign up" }).click();

    // Should redirect to profile (changed from dashboard)
    await page.waitForURL("/dashboard");

    // Track email for cleanup
    testEmail = email;

    // Verify profile access - the provider should see their profile page
    await expect(page.getByText(fullName)).toBeVisible();
    await expect(page.getByText("Not provided").first()).toBeVisible();

    // Step 4: Add phone number to provider profile
    await page.getByRole("link", { name: "Edit Profile" }).click();
    await page.waitForURL("/dashboard/edit");

    // Wait for the phone number field to be visible
    await page.getByLabel("Phone Number").waitFor();

    // Fill in the phone number
    await page.getByLabel("Phone Number").fill(phoneNumber);

    // Save the profile changes
    await page.getByRole("button", { name: "Save Changes" }).click();

    // Wait for redirect back to dashboard
    await page.waitForURL("/dashboard");

    // Verify phone number is displayed
    await expect(page.getByText(phoneNumber)).toBeVisible();

    // Step 5: Add organization description and location
    await page.getByRole("link", { name: "Edit Organisation" }).click();
    await page.waitForURL("/dashboard/organization");

    // Wait for the organization description field to be visible
    await page.getByLabel("Organisation Description").waitFor();

    // Fill in the organization description
    await page
      .getByLabel("Organisation Description")
      .fill(organizationDescription);

    // Set the address field
    await page.getByLabel("Address").fill("123 Test Street, Singapore 123456");

    // Save the organization changes
    await page.getByRole("button", { name: "Save Changes" }).click();

    // Wait for redirect back to dashboard
    await page.waitForURL("/dashboard");

    // Verify organization description is displayed - check for part of the description
    await expect(
      page.getByText(/This is a test wellness organization/),
    ).toBeVisible();
    await expect(page.getByText("Not provided")).not.toBeVisible();

    // Test logout functionality
    await logout(page);

    // Should redirect to providers landing page
    await page.waitForURL("/providers");

    // Verify we're logged out
    await verifyLoggedOut(page);

    // Test logging back in
    await clickLoginLink(page);
    await page.waitForURL("/login?type=provider");

    // Login with the same credentials
    await page.getByLabel("Email").fill(email);
    await page.getByLabel("Password", { exact: true }).fill(password);
    await page.getByRole("button", { name: "Sign in" }).click();

    // Should redirect to dashboard
    await page.waitForURL("/dashboard");

    // Verify we're logged in again - check for key elements
    await expect(page.getByText(fullName)).toBeVisible();
  });

  test("provider signup requires all fields", async ({ page }) => {
    await page.goto("/signup");
    await page.waitForLoadState("networkidle");

    // Enter valid invite code
    await page.getByLabel("Invite Code").fill("SIMPLEVIBE25");
    await page.getByRole("button", { name: "Continue" }).click();

    // Wait for the form to load - look for Full Name label
    await page.getByLabel("Full Name").waitFor();

    // Try to submit without filling fields
    await page
      .getByRole("button", { name: "Next", exact: true })
      .first()
      .click();

    // Should show validation errors - check for form error messages
    const errorMessage = page.locator(".text-destructive").first();
    await errorMessage.waitFor();
  });

  test("provider can sign up with Kuala Lumpur and city/country are set correctly", async ({
    page,
  }) => {
    const uniqueId = Date.now();
    const email = `test-provider-kl-${uniqueId}${TEST_EMAIL_DOMAIN}`;
    const password = `Password${uniqueId}!`;
    const fullName = `Test Provider KL ${uniqueId}`;
    const organizationName = `${TEST_ORGANIZATION_PREFIX} KL ${uniqueId}`;

    await page.goto("/signup");
    await page.waitForLoadState("networkidle");

    // Step 1: Enter invite code
    await page.getByLabel("Invite Code").fill("SIMPLEVIBE25");
    await page.getByRole("button", { name: "Continue" }).click();

    // Wait for form to appear
    await page.getByLabel("Full Name").waitFor();

    // Step 2: Fill personal details
    await page.getByLabel("Full Name").fill(fullName);
    await page.getByLabel("Email").fill(email);
    await page.getByLabel("Password", { exact: true }).fill(password);
    await page.getByLabel("Confirm Password").fill(password);

    // Click Next
    await page
      .getByRole("button", { name: "Next", exact: true })
      .first()
      .click();

    // Wait for organization form
    await page.getByLabel("Organisation / Provider Name").waitFor();

    // Step 3: Fill organization details
    await page
      .getByLabel("Organisation / Provider Name")
      .fill(organizationName);

    // Select location - Kuala Lumpur, Malaysia
    await page.getByRole("combobox").click();
    await page.getByRole("option", { name: "Kuala Lumpur, Malaysia" }).click();

    // Agree to terms and conditions
    await page.getByRole("checkbox").check();

    // Submit
    await page.getByRole("button", { name: "Sign up" }).click();

    // Should redirect to profile (changed from dashboard)
    await page.waitForURL("/dashboard");

    // Track email for cleanup
    testEmail = email;

    // Look for the Edit Organisation button to ensure page is loaded
    await page.getByRole("link", { name: "Edit Organisation" }).waitFor();

    // Verify city and country are displayed correctly
    await expect(page.getByText("Kuala Lumpur, Malaysia")).toBeVisible();
  });

  test("password mismatch shows error", async ({ page }) => {
    const uniqueId = Date.now();
    const email = `test-provider-${uniqueId}${TEST_EMAIL_DOMAIN}`;
    const password = `Password${uniqueId}!`;
    const fullName = `Test Provider ${uniqueId}`;

    await page.goto("/signup");
    await page.waitForLoadState("networkidle");

    // Step 1: Enter invite code
    await page.getByLabel("Invite Code").fill("SIMPLEVIBE25");
    await page.getByRole("button", { name: "Continue" }).click();

    // Wait for form to appear
    await page.getByLabel("Full Name").waitFor();

    // Step 2: Fill personal details with mismatched passwords
    await page.getByLabel("Full Name").fill(fullName);
    await page.getByLabel("Email").fill(email);
    await page.getByLabel("Password", { exact: true }).fill(password);
    await page.getByLabel("Confirm Password").fill(`Different${password}`);

    // Try to proceed to next step - should show validation error
    await page
      .getByRole("button", { name: "Next", exact: true })
      .first()
      .click();

    // Should show password mismatch error
    await expect(page.getByText("Passwords don't match")).toBeVisible();
  });
});
