import { expect, test } from "@playwright/test";

import { TEST_EMAIL_DOMAIN } from "./utils/constants";
import { createTestUser, deleteTestUserByEmail } from "./utils/db.helper";

test.describe("Signup Page - Logged in Client", () => {
  let testEmail: string | null = null;

  test.afterEach(async () => {
    // Clean up the user if it was created
    if (testEmail) {
      await deleteTestUserByEmail(testEmail);
      testEmail = null;
    }
  });

  test("logged-in client sees message when accessing /signup?type=provider", async ({
    page,
  }) => {
    const uniqueId = Date.now();
    const email = `test-client-signup-${uniqueId}${TEST_EMAIL_DOMAIN}`;
    const password = `Password${uniqueId}!`;
    const fullName = `Test Client ${uniqueId}`;
    const companyName = `Test Company ${uniqueId}`;

    // Step 1: Create a client user first
    await createTestUser({
      email,
      password,
      fullName,
      userType: "client",
      companyName,
    });

    testEmail = email;

    // Step 2: Login as the client
    await page.goto("/login");
    await page.waitForLoadState("networkidle");

    await page.getByLabel("Email").fill(email);
    await page.getByLabel("Password", { exact: true }).fill(password);
    await page.getByRole("button", { name: "Sign in" }).click();

    // Should redirect to profile page for clients
    await page.waitForURL("/profile");

    // Step 3: Navigate to providers landing page first, then click "List your event"
    await page.goto("/providers");
    await page.waitForLoadState("networkidle");

    // Click the "List your event" button which goes to /signup?type=provider
    await page.getByRole("link", { name: "List your event" }).click();
    await page.waitForLoadState("networkidle");

    // Step 4: Verify the message is shown
    await expect(page.getByText("Already Logged In")).toBeVisible();
    await expect(
      page.getByText(
        "You're currently logged in as a client. To create a provider account, please log out first.",
      ),
    ).toBeVisible();

    // Step 5: Verify the Log Out button is present
    await expect(page.getByRole("button", { name: "Log Out" })).toBeVisible();

    // Step 6: Verify the "Go to your profile" link is present
    await expect(page.getByText("Go to your profile")).toBeVisible();

    // Step 7: Test the Log Out button functionality
    await page.getByRole("button", { name: "Log Out" }).click();

    // Should redirect to home page after logout
    await page.waitForURL("/");

    // Step 8: Verify we can now access the signup page normally by clicking the link again
    await page.goto("/providers");
    await page.waitForLoadState("networkidle");

    await page.getByRole("link", { name: "List your event" }).click();
    await page.waitForLoadState("networkidle");

    // Should now show the normal signup form
    await expect(page.getByText("Create a provider account")).toBeVisible();
    await expect(page.getByText("Enter your invite code")).toBeVisible();
    await expect(page.getByLabel("Invite Code")).toBeVisible();
  });
});
