import { expect, test } from "@playwright/test";

import { TEST_EMAIL_DOMAIN } from "./utils/constants";
import { createTestUser, deleteTestUser } from "./utils/db.helper";
import {
  clickLoginLink,
  logout,
  navigateViaNavbar,
} from "./utils/navigation.helper";

test.describe("Change Password", () => {
  test("client can change password", async ({ page }) => {
    // Create a test user
    const testUser = await createTestUser({
      email: `test-pwd-${Date.now()}${TEST_EMAIL_DOMAIN}`,
      password: "Password123!",
      fullName: "Test Password User",
      userType: "client",
      companyName: "Test Company",
      location: "Singapore",
    });

    const currentPassword = testUser.password;
    const newPassword = "NewPassword456!";

    try {
      // Login
      await page.goto("/login");
      await page.waitForLoadState("networkidle");
      await page.getByLabel("Email").fill(testUser.email);
      await page.getByLabel("Password", { exact: true }).fill(currentPassword);
      await page.getByRole("button", { name: "Sign in" }).click();

      // Should redirect to profile
      await page.waitForURL("/profile");

      // Navigate to change password via Edit Profile
      await page.getByRole("link", { name: "Edit Profile" }).click();
      await page.waitForURL("/profile/edit");
      await page.getByRole("link", { name: "Change Password" }).click();
      await page.waitForURL("/profile/password");
      await expect(page.getByText("Change Password").first()).toBeVisible();

      // Change password
      await page.getByLabel("Current Password").fill(currentPassword);
      await page.getByLabel("New Password", { exact: true }).fill(newPassword);
      await page.getByLabel("Confirm New Password").fill(newPassword);
      await page.getByRole("button", { name: "Change Password" }).click();

      // Should show success message
      await page.getByText(/Password changed successfully/i).waitFor();

      // Should still be on the password page
      await page.waitForURL("/profile/password");

      // Navigate to profile via navbar to verify we're still logged in
      await navigateViaNavbar(page, "Profile");

      // Test logout and login with new password
      await logout(page);

      // Should redirect to home page
      await page.waitForURL("/");

      // Navigate to login page
      await clickLoginLink(page);
      await page.waitForURL("/login?type=client");

      // Try to login with old password - should fail
      await page.getByLabel("Email").fill(testUser.email);
      await page.getByLabel("Password", { exact: true }).fill(currentPassword);
      await page.getByRole("button", { name: "Sign in" }).click();

      // Should show error
      await page.getByText("Invalid login credentials").waitFor();

      // Login with new password - should succeed
      await page.getByLabel("Password", { exact: true }).clear();
      await page.getByLabel("Password", { exact: true }).fill(newPassword);
      await page.getByRole("button", { name: "Sign in" }).click();

      // Should redirect to profile
      await page.waitForURL("/profile");

      // Verify we're logged in
      await expect(page.getByText(testUser.fullName)).toBeVisible();
    } finally {
      // Clean up
      await deleteTestUser(testUser.id);
    }
  });

  test("provider can change password", async ({ page }) => {
    // Create a test provider
    const testProvider = await createTestUser({
      email: `test-provider-pwd-${Date.now()}${TEST_EMAIL_DOMAIN}`,
      password: "Password123!",
      fullName: "Test Provider User",
      userType: "provider",
      organizationName: "Test Wellness Co",
      location: "Singapore",
    });

    const currentPassword = testProvider.password;
    const newPassword = "NewPassword456!";

    try {
      // Login
      await page.goto("/login");
      await page.waitForLoadState("networkidle");
      await page.getByLabel("Email").fill(testProvider.email);
      await page.getByLabel("Password", { exact: true }).fill(currentPassword);
      await page.getByRole("button", { name: "Sign in" }).click();

      // Should redirect to dashboard (via /profile redirect)
      await page.waitForURL("/dashboard");

      // Navigate to change password via Edit Profile
      await page.getByRole("link", { name: "Edit Profile" }).click();
      await page.waitForURL("/dashboard/edit");
      await page.getByRole("link", { name: "Change Password" }).click();
      await page.waitForURL("/dashboard/password");
      await expect(page.getByText("Change Password").first()).toBeVisible();

      // Change password
      await page.getByLabel("Current Password").fill(currentPassword);
      await page.getByLabel("New Password", { exact: true }).fill(newPassword);
      await page.getByLabel("Confirm New Password").fill(newPassword);
      await page.getByRole("button", { name: "Change Password" }).click();

      // Should show success message
      await page.getByText(/Password changed successfully/i).waitFor();

      // Should still be on the password page
      await page.waitForURL("/dashboard/password");

      // Navigate to dashboard via navbar to verify we're still logged in
      await navigateViaNavbar(page, "Dashboard");

      // Test logout and login with new password
      await logout(page);

      // Should redirect to providers landing page
      await page.waitForURL("/providers");

      // Navigate to login page
      await clickLoginLink(page);
      await page.waitForURL("/login?type=provider");

      // Try to login with old password - should fail
      await page.getByLabel("Email").fill(testProvider.email);
      await page.getByLabel("Password", { exact: true }).fill(currentPassword);
      await page.getByRole("button", { name: "Sign in" }).click();

      // Should show error
      await page.getByText("Invalid login credentials").waitFor();

      // Login with new password - should succeed
      await page.getByLabel("Password", { exact: true }).clear();
      await page.getByLabel("Password", { exact: true }).fill(newPassword);
      await page.getByRole("button", { name: "Sign in" }).click();

      // Should redirect to dashboard
      await page.waitForURL("/dashboard");

      // Verify we're logged in - check that we're on the dashboard
      await expect(
        page.getByRole("heading", { name: "My Workshops" }),
      ).toBeVisible();
    } finally {
      // Clean up
      await deleteTestUser(testProvider.id);
    }
  });
});
