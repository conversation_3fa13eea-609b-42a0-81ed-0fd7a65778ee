# E2E Test Helpers

## Available Helper Files

### `./utils/navigation.helper.ts`

Navigation helpers that handle mobile/desktop differences (hamburger menu, etc).
Look here for `navigateViaNavbar(page, linkName, expectedUrl)`, `logout()`, `clickLoginLink()`, etc.

### `./utils/db.helper.ts`

Database helpers for creating/deleting test data.
Look here for `createTestUser()`, `deleteTestUser()`, `createTestWorkshop()`, etc.

### `./utils/constants.ts`

Test constants like `TEST_EMAIL_DOMAIN` and `TEST_ORGANIZATION_PREFIX`.

## Key Patterns

- Use `goto()` only at the start of tests - after that, navigate by clicking links/buttons
- Use `waitForLoadState("networkidle")` after `goto()`
- Use `waitForURL()` after navigation actions (clicks that change routes) unless it's hard or unimportant to test for
  the exact URL. waitForLoadState("networkIdle") is also OK.
- Always use navigation helpers instead of custom mobile/desktop handling
- Clean up test data in `afterEach` hooks
- Tests run on both "Desktop Chrome" and "Mobile Safari"
