# E2E Tests

This directory contains end-to-end tests for the Pulse Space application.

## Test Files

All tests use database helpers for reliable setup and cleanup:

- `workshop.spec.ts` - Workshop creation and management
- `client-signup.spec.ts` - Client signup flow
- `provider-signup.spec.ts` - Provider signup flow
- `profile-delete.spec.ts` - Profile deletion

## Running Tests

```bash
# Run all tests
pnpm test:e2e

# Run tests with UI
pnpm test:e2e:ui

# Run specific test file
pnpm test:e2e workshop-improved.spec.ts

# View test report
pnpm test:e2e:report
```

## Test Helpers

### Database Helpers (`utils/db.helper.ts`)

- `createTestUser()` - Creates a user directly in the database
- `deleteTestUser()` - Deletes a user and all related data
- `createTestWorkshop()` - Creates a workshop for a provider
- `deleteTestWorkshop()` - Deletes a workshop
- `cleanupTestUsers()` - Bulk cleanup of test users

### Auth Helpers (`utils/auth.helper.ts`)

- `signUpProvider()` - Signs up a provider through the UI
- `signUpClient()` - Signs up a client through the UI

## Environment Setup

Tests require the following environment variables:

- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY` - Service role key for database operations

## Global Setup/Teardown

The test suite uses Playwright's setup project pattern for global setup/teardown:

- `global.setup.ts` - Runs before all tests to clean up old test data and verify app is running
- `global.teardown.ts` - Runs after all tests to clean up test data created during the run

These are configured as separate projects in `playwright.config.ts` with all test projects depending on the setup project.

## Best Practices

1. **Use improved tests** - They have better cleanup and are more reliable
2. **Unique test data** - Always use timestamps in test data to avoid conflicts
3. **Clean up after tests** - Use afterEach hooks to clean up created data
4. **Direct DB operations** - For setup, prefer creating data directly in DB
5. **Test isolation** - Each test should be independent and not rely on others

## Test Data Patterns

Test users follow these email patterns:

- Clients: `test-client-{timestamp}@example.com`
- Providers: `test-provider-{timestamp}@example.com`

This allows the cleanup scripts to identify and remove test data.
