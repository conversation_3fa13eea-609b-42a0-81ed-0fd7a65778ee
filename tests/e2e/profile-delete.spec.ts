import { test } from "@playwright/test";

import { TEST_EMAIL_DOMAIN } from "./utils/constants";
import {
  createTestUser,
  deleteTestUser,
  type TestUser,
} from "./utils/db.helper";
import { verifyLoggedOut } from "./utils/navigation.helper";

test.describe("Profile Deletion", () => {
  let testUsers: TestUser[] = [];

  test.afterEach(async () => {
    // Clean up any users that were created during tests
    for (const user of testUsers) {
      try {
        await deleteTestUser(user.id);
      } catch {
        // User already deleted through UI, which is expected
      }
    }
    testUsers = [];
  });

  test("client can delete their profile", async ({ page }) => {
    // Create a client user directly in database
    const uniqueId = Date.now();
    const client: TestUser = await createTestUser({
      email: `test-client-${uniqueId}${TEST_EMAIL_DOMAIN}`,
      password: `Password${uniqueId}!`,
      fullName: `Test Client ${uniqueId}`,
      userType: "client",
      companyName: `Company ${uniqueId}`,
      location: `Location ${uniqueId}`,
    });

    // Track for cleanup
    testUsers.push(client);

    // Log in
    await page.goto("/login");
    await page.waitForLoadState("networkidle");
    await page.getByLabel("Email").fill(client.email);
    await page.getByLabel("Password", { exact: true }).fill(client.password);
    await page.getByRole("button", { name: "Sign in" }).click();
    await page.waitForURL("/profile");

    // Navigate to profile edit via UI
    await page.getByRole("link", { name: "Edit Profile" }).click();
    await page.waitForURL("/profile/edit");
    await page.getByRole("heading", { name: "Edit Profile" }).waitFor();

    // Delete profile
    await page.getByRole("button", { name: "Delete Profile" }).click();
    await page.getByRole("button", { name: "Continue" }).click();

    // Should redirect to home page
    await page.waitForURL("/");

    // Verify logged out
    await verifyLoggedOut(page);

    // Verify can't log in with deleted credentials
    await page.goto("/login");
    await page.getByLabel("Email").fill(client.email);
    await page.getByLabel("Password", { exact: true }).fill(client.password);
    await page.getByRole("button", { name: "Sign in" }).click();

    await page.getByText("Invalid login credentials").waitFor();

    // Remove it from the array so the cleanup doesn't try to delete it again
    testUsers = testUsers.filter((user) => user.email !== client.email);
  });

  test("provider can delete their profile", async ({ page }) => {
    // Create a provider user directly in database
    const uniqueId = Date.now();
    const provider: TestUser = await createTestUser({
      email: `test-provider-${uniqueId}${TEST_EMAIL_DOMAIN}`,
      password: `Password${uniqueId}!`,
      fullName: `Test Provider ${uniqueId}`,
      userType: "provider",
      organizationName: `Organization ${uniqueId}`,
    });

    // Track for cleanup
    testUsers.push(provider);

    // Log in
    await page.goto("/login");
    await page.waitForLoadState("networkidle");
    await page.getByLabel("Email").fill(provider.email);
    await page.getByLabel("Password", { exact: true }).fill(provider.password);
    await page.getByRole("button", { name: "Sign in" }).click();
    await page.waitForURL("/dashboard");

    // Navigate to profile edit via UI
    await page.getByRole("link", { name: "Edit Profile" }).click();
    await page.waitForURL("/dashboard/edit");
    await page.getByRole("heading", { name: "Edit Profile" }).waitFor();

    // Delete profile
    await page.getByRole("button", { name: "Delete Profile" }).click();
    await page.getByRole("button", { name: "Continue" }).click();

    // Wait for deletion and redirect
    await page.waitForURL("/");

    // Verify logged out
    await verifyLoggedOut(page);

    // Verify can't log in with deleted credentials
    await page.goto("/login");
    await page.getByLabel("Email").fill(provider.email);
    await page.getByLabel("Password", { exact: true }).fill(provider.password);
    await page.getByRole("button", { name: "Sign in" }).click();

    await page.getByText("Invalid login credentials").waitFor();

    // Remove it from the array so the cleanup doesn't try to delete it again
    testUsers = testUsers.filter((user) => user.email !== provider.email);
  });
});
