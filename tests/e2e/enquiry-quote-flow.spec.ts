import { expect, test } from "@playwright/test";

import { TEST_EMAIL_DOMAIN, TEST_ORGANIZATION_PREFIX } from "./utils/constants";
import {
  createTestUser,
  createTestWorkshop,
  deleteTestUser,
  deleteTestWorkshop,
  type TestUser,
} from "./utils/db.helper";
import { navigateViaNavbar, viewNavbarLink } from "./utils/navigation.helper";

test.describe("Enquiry and Quote Flow", () => {
  let client: TestUser;
  let provider: TestUser;
  let workshopId: string;

  test.beforeEach(async () => {
    const uniqueId = Date.now();

    client = await createTestUser({
      email: `test-client-${uniqueId}${TEST_EMAIL_DOMAIN}`,
      password: `Password${uniqueId}!`,
      fullName: `Test Client ${uniqueId}`,
      userType: "client",
      companyName: "Test Company",
      location: "Singapore",
    });

    provider = await createTestUser({
      email: `test-provider-${uniqueId}${TEST_EMAIL_DOMAIN}`,
      password: `Password${uniqueId}!`,
      fullName: `Test Provider ${uniqueId}`,
      userType: "provider",
      organizationName: `${TEST_ORGANIZATION_PREFIX} ${uniqueId}`,
    });

    if (!provider.providerId) {
      throw new Error("Provider organization ID is missing");
    }

    workshopId = await createTestWorkshop(provider.providerId, {
      name: `E2E Test Workshop ${uniqueId}`,
      description: "Workshop for testing enquiry and quote flow",
      price: 500,
      pricing_model: "total",
      format: "hybrid",
      duration: "2 hours",
      min_capacity: 5,
      max_capacity: 20,
      published: true,
    });
  });

  test.afterEach(async () => {
    if (workshopId) {
      await deleteTestWorkshop(workshopId);
    }
    if (client?.id) {
      await deleteTestUser(client.id);
    }
    if (provider?.id) {
      await deleteTestUser(provider.id);
    }
  });

  test("complete enquiry and quote flow with two browser contexts", async ({
    browser,
  }) => {
    // Create two separate browser contexts for client and provider
    const clientContext = await browser.newContext();
    const providerContext = await browser.newContext();

    const clientPage = await clientContext.newPage();
    const providerPage = await providerContext.newPage();

    try {
      // === CLIENT FLOW: Login and send enquiry ===

      // Login as client
      await clientPage.goto("/login");
      await clientPage.waitForLoadState("networkidle");
      await clientPage.getByLabel("Email").fill(client.email);
      await clientPage
        .getByLabel("Password", { exact: true })
        .fill(client.password);
      await clientPage.getByRole("button", { name: "Sign in" }).click();

      // Check if we're on client profile page (clients redirect to /profile)
      await clientPage.waitForURL("/profile");

      // Navigate to workshops listing
      await navigateViaNavbar(clientPage, "Workshops");

      // Find and click on our test workshop
      const workshopCard = clientPage.locator("a").filter({
        hasText: `E2E Test Workshop`,
      });
      await expect(workshopCard.first()).toBeVisible();
      await workshopCard.first().click();

      // Wait for workshop detail page
      await clientPage.waitForURL(/\/workshops\/[0-9a-f-]+/);

      // Click "Enquire to Book" button
      await expect(
        clientPage.getByRole("button", { name: "Enquire to Book" }),
      ).toBeVisible();
      await clientPage.getByRole("button", { name: "Enquire to Book" }).click();

      // Should navigate to chat room
      await clientPage.waitForURL(/\/enquiries\/[^/]+/);

      // Verify we're in the chat interface
      await expect(
        clientPage.getByPlaceholder("Type a message..."),
      ).toBeVisible();

      // Client sends a message
      const testMessage =
        "Hi, I'm interested in booking this workshop for my team.";
      await clientPage.getByPlaceholder("Type a message...").fill(testMessage);
      await clientPage.getByRole("button", { name: "Send" }).click();

      // Verify the message appears in the chat
      await expect(clientPage.getByText(testMessage)).toBeVisible();

      // Verify that the client (message sender) does NOT see unread indicators for their own message
      await navigateViaNavbar(clientPage, "Enquiries");

      // Check that the "Enquiries" nav link does NOT show a red dot for the client
      {
        const { link, close } = await viewNavbarLink(clientPage, "Enquiries");
        const redDot = link.locator(".bg-red-600.rounded-full");
        await expect(redDot).not.toBeVisible();
        await close();
      }

      // === PROVIDER FLOW: Login and respond with quote ===

      // Login as provider
      await providerPage.goto("/login");
      await providerPage.waitForLoadState("networkidle");
      await providerPage.getByLabel("Email").fill(provider.email);
      await providerPage
        .getByLabel("Password", { exact: true })
        .fill(provider.password);
      await providerPage.getByRole("button", { name: "Sign in" }).click();
      await providerPage.waitForURL("/dashboard");

      // Check that the "Enquiries" nav link shows a red dot indicator for unread messages
      {
        const { link, close } = await viewNavbarLink(providerPage, "Enquiries");
        await expect(link).toBeVisible();

        // Look for the red dot indicator (StatusIndicator with missing status)
        const redDot = link.locator(".bg-red-600.rounded-full");
        await expect(redDot).toBeVisible();
        await close();
      }

      // Navigate to enquiries
      await navigateViaNavbar(providerPage, "Enquiries");

      // Check that the message sidebar shows unread indicators
      const clientMessageRow = providerPage
        .locator("a")
        .filter({ hasText: client.fullName });
      await expect(clientMessageRow).toBeVisible();

      // Check for red dot indicator in the sidebar
      const sidebarRedDot = clientMessageRow.locator(
        ".bg-red-600.rounded-full",
      );
      await expect(sidebarRedDot).toBeVisible();

      // Check that the client name appears bold (font-bold class)
      const clientNameElement = clientMessageRow
        .locator("span")
        .filter({ hasText: client.fullName });
      await expect(clientNameElement).toHaveClass(/font-bold/);

      // Find and click on conversation with client
      await providerPage.getByText(client.fullName).click();

      // Wait for chat to load
      await providerPage.waitForURL(/\/enquiries\/[^/]+/);

      // Verify provider can see the client's message
      await expect(providerPage.getByText(testMessage)).toBeVisible();

      // Navigate back to enquiries list to verify red dot indicators are gone
      await navigateViaNavbar(providerPage, "Enquiries");

      // Verify the "Enquiries" nav link no longer shows a red dot (room was marked as read)
      {
        const { link, close } = await viewNavbarLink(providerPage, "Enquiries");
        const redDot = link.locator(".bg-red-600.rounded-full");
        await expect(redDot).not.toBeVisible();
        await close();
      }

      // Verify the sidebar no longer shows red dot for this conversation
      const updatedClientMessageRow = providerPage
        .locator("a")
        .filter({ hasText: client.fullName });
      const updatedSidebarRedDot = updatedClientMessageRow.locator(
        ".bg-red-600.rounded-full",
      );
      await expect(updatedSidebarRedDot).not.toBeVisible();

      // Verify the client name is no longer bold (should be font-medium now)
      const updatedClientNameElement = updatedClientMessageRow
        .locator("span")
        .filter({ hasText: client.fullName });
      await expect(updatedClientNameElement).toHaveClass(/font-medium/);

      // Go back to the chat to continue the test
      await providerPage.getByText(client.fullName).click();
      await providerPage.waitForURL(/\/enquiries\/[^/]+/);

      // Switch to quote mode by clicking the switch
      await expect(providerPage.getByRole("switch")).toBeVisible();
      await providerPage.getByRole("switch").click();

      // Fill out quote form
      await providerPage.getByLabel("Date & Time").fill("2024-12-20T14:00");
      await providerPage
        .getByLabel("Location")
        .fill("Client's office - 123 Business Street, Singapore");

      // Clear and fill price field
      const priceField = providerPage.getByRole("spinbutton");
      await priceField.click();
      await priceField.fill("");
      await priceField.fill("1200");

      await providerPage
        .getByLabel("Notes (Optional)")
        .fill(
          "This quote includes all materials and a 2-hour interactive session for up to 20 participants. Travel within Singapore CBD is included.",
        );

      // Click Preview button
      await expect(
        providerPage.getByRole("button", { name: "Preview" }),
      ).toBeVisible();
      await expect(
        providerPage.getByRole("button", { name: "Preview" }),
      ).toBeEnabled();
      await providerPage.getByRole("button", { name: "Preview" }).click();

      // Verify quote card preview appears
      await expect(providerPage.getByText("SGD 1,200.00")).toBeVisible();
      await expect(
        providerPage.getByText(
          "Client's office - 123 Business Street, Singapore",
        ),
      ).toBeVisible();

      // Send the quote
      await expect(
        providerPage.getByRole("button", { name: "Send Quote" }),
      ).toBeVisible();
      await providerPage.getByRole("button", { name: "Send Quote" }).click();

      // Wait for the quote to appear in the chat
      // The switch should reset to Message mode after sending
      await expect(providerPage.getByRole("switch")).not.toBeChecked();
      // Verify the quote appears in the chat messages
      await expect(providerPage.getByText("SGD 1,200.00").last()).toBeVisible();

      // Verify that the provider (quote sender) does NOT see unread indicators for their own quote
      await navigateViaNavbar(providerPage, "Enquiries");

      // Check that the "Enquiries" nav link does NOT show a red dot for the provider
      {
        const { link, close } = await viewNavbarLink(providerPage, "Enquiries");
        const redDot = link.locator(".bg-red-600.rounded-full");
        await expect(redDot).not.toBeVisible();
        await close();
      }

      // === CLIENT FLOW: View and accept quote ===
      // Note: Realtime updates don't work reliably in test environment,
      // so we'll refresh the page to see the quote

      // First, navigate to client enquiries to see unread indicators from quote
      await navigateViaNavbar(clientPage, "Enquiries");

      // Check that the "Enquiries" nav link shows a red dot for the new quote
      {
        const { link, close } = await viewNavbarLink(clientPage, "Enquiries");
        const redDot = link.locator(".bg-red-600.rounded-full");
        await expect(redDot).toBeVisible();
        await close();
      }

      // Check that the provider's message row shows unread indicators
      const providerMessageRow = clientPage
        .locator("a")
        .filter({ hasText: provider.organizationName });
      await expect(providerMessageRow).toBeVisible();

      // Check for red dot and bold text in the sidebar
      const clientSidebarRedDot = providerMessageRow.locator(
        ".bg-red-600.rounded-full",
      );
      await expect(clientSidebarRedDot).toBeVisible();

      const providerNameElement = providerMessageRow
        .locator("span")
        .filter({ hasText: provider.organizationName });
      await expect(providerNameElement).toHaveClass(/font-bold/);

      // Click on the conversation to view the quote
      await providerMessageRow.click();
      await clientPage.waitForURL(/\/enquiries\/[^/]+/);

      await clientPage.reload();

      // Verify quote card appears after refresh
      await expect(clientPage.getByText("SGD 1,200.00")).toBeVisible();
      await expect(
        clientPage.getByText(
          "Client's office - 123 Business Street, Singapore",
        ),
      ).toBeVisible();

      // Verify Accept & Pay button is visible for client
      await expect(
        clientPage.getByRole("button", { name: "Accept & Pay" }),
      ).toBeVisible();

      // Navigate back to enquiries to verify unread indicators are now gone for client
      await navigateViaNavbar(clientPage, "Enquiries");

      // Verify the "Enquiries" nav link no longer shows a red dot (quote was read)
      {
        const { link, close } = await viewNavbarLink(clientPage, "Enquiries");
        const redDot = link.locator(".bg-red-600.rounded-full");
        await expect(redDot).not.toBeVisible();
        await close();
      }

      // Verify the sidebar no longer shows red dot for this conversation
      const finalProviderMessageRow = clientPage
        .locator("a")
        .filter({ hasText: provider.organizationName });
      const finalClientSidebarRedDot = finalProviderMessageRow.locator(
        ".bg-red-600.rounded-full",
      );
      await expect(finalClientSidebarRedDot).not.toBeVisible();

      // Verify the provider organization name is no longer bold
      const finalProviderNameElement = finalProviderMessageRow
        .locator("span")
        .filter({ hasText: provider.organizationName });
      await expect(finalProviderNameElement).toHaveClass(/font-medium/);
    } finally {
      // Clean up contexts
      await clientContext.close();
      await providerContext.close();
    }
  });
});
