import { test as teardown } from "@playwright/test";

import { TEST_EMAIL_DOMAIN, TEST_ORGANIZATION_PREFIX } from "./utils/constants";
import { deleteTestUser, supabaseAdmin } from "./utils/db.helper";

teardown("cleanup all test data", async () => {
  await cleanupTestUsers(`%test-%${TEST_EMAIL_DOMAIN}`);
  await cleanupTestOrganizations();
});

async function cleanupTestUsers(emailPattern: string): Promise<void> {
  // Find all test users matching the pattern
  const { data: profiles } = await supabaseAdmin
    .from("profiles")
    .select("id, email")
    .like("email", emailPattern);

  if (profiles && profiles.length > 0) {
    for (const profile of profiles) {
      await deleteTestUser(profile.id);
    }
  }
}

async function cleanupTestOrganizations(): Promise<void> {
  // Get all provider organizations
  const { data: allOrgs } = await supabaseAdmin
    .from("provider_organizations")
    .select("id, name");

  // Get all linked organization IDs
  const { data: linkedOrgIds } = await supabaseAdmin
    .from("providers")
    .select("organization_id");

  if (allOrgs && linkedOrgIds) {
    const linkedIds = new Set(linkedOrgIds.map((p) => p.organization_id));
    const orphanedOrgs = allOrgs.filter((org) => !linkedIds.has(org.id));

    // Delete orphaned organizations that look like test data
    for (const org of orphanedOrgs) {
      // Only delete organizations that match our test prefix exactly
      if (org.name.startsWith(TEST_ORGANIZATION_PREFIX)) {
        // Delete the organization (CASCADE will handle workshops, quotes, chat_rooms, providers)
        const { error } = await supabaseAdmin
          .from("provider_organizations")
          .delete()
          .eq("id", org.id);

        if (!error) {
          console.log(`Deleted orphaned test organization: ${org.name}`);
        }
      }
    }
  }
}
