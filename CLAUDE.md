# Wellness Events Marketplace Project

## Project Info

- **GitHub**: exclipy/wellness-workshops
- **Package Manager**: Always use `pnpm` (never `npm`)

## Project Structure

### Core Directories

- **`/app`** - Next.js 15 App Router pages

  - `(auth)` - Authentication pages (login, signup, password reset)
  - `(home)` - Public landing page
  - `(loggedin)` - Protected pages requiring authentication
  - `admin` - Admin dashboard and management pages
  - `api` - API routes for when server actions aren't appropriate (eg. for webhooks)
  - `enquiries` - Messaging and quote features

- **`/components`** - React components organized by feature

  - `ui/` - Reusable UI primitives (shadcn-generated)
  - `auth/` - Authentication forms and components
  - `dashboard/` - Provider dashboard components
  - `profile/` - User profile management
  - `workshops/` - Workshop cards, filters, and lists
  - `messages/` - Chat and quote components
  - `admin/` - Admin-specific components

- **`/lib`** - Core utilities and business logic

  - `utils.ts` - Common utilities (cn, parseSearchParams)
  - `schemas.ts` - Zod validation schemas
  - `auth.ts` - Authentication helpers
  - `format.ts` - Formatting for dates, times, etc.
  - `airwallex-client.ts` - Payment API wrapper for Airwallex
  - `temporal-utils.ts` - Date/time utilities
  - `user-helpers.ts` - User-related functions

- **`/types`** - TypeScript type definitions

  - `database.types.ts` - Auto-generated Supabase types
  - Domain types: `user.ts`, `workshop.ts`, `booking.ts`, `chat.ts`, `quote.ts`, `organization.ts`

- **`/hooks`** - Custom React hooks

  - `use-mobile.tsx` - Mobile screen size detection
  - `use-realtime-table-changes.tsx` - Supabase realtime subscriptions

- **`/contexts`** - React context providers

  - `supabase-auth-provider.tsx` - Authentication context

- **`/supabase`** - Database configuration

  - `migrations/` - SQL migration files
  - `config.toml` - Local Supabase configuration

- **`/utils/supabase`** - Supabase client setup
  - `server.ts` - Server-side client creation

## Development Commands

```bash
# Code Quality
pnpm run check        # Run all lints and typechecks
pnpm run lint         # Run linter only
pnpm run typecheck    # Run TypeScript checking only
pnpm run format       # Format with Prettier
pnpm run depcheck     # Check unused dependencies

# Testing
pnpm run test:e2e          # Run e2e tests
pnpm run test:e2e:report   # Show test report

# Database
pnpm supabase migration new <name>  # Create migration
pnpm supabase gen types --local > types/database.types.ts  # Generate TypeScript types
```

**Tip**: If `.next` directory has errors, delete it and rebuild.

## Test Users

All test users use password: `asdfasdf`

**Providers:**

- <EMAIL>

**Clients:**

- <EMAIL>

## Code Patterns

### No optional props

Make props required by default. Only make optional if there's a valid default behavior.
✅ foo: Foo // ALWAYS consider this first
✅ foo: Foo | null // OK if null is meaningful
✅ foo: Foo | null | undefined // OK if arg is filled by a source that may be undefined
❌ foo?: Foo // AVOID - requires default values and makes API unclear

Example: Instead of `isProvider?: boolean` with `isProvider = false`, use `isProvider:
boolean`

### Next.js 15 URL Parameters

Always await parameters in server components:

```typescript
const resolvedParams = await params;
const resolvedSearchParams = await searchParams;
```

### Data Validation

- Use Zod for external data validation (URL params, forms)
- Server components: use `parseSearchParams` utility
- Client components: use `useZodSearchParams` hook
- Form Handling: react-hook-form + Zod validation

## Database Conventions

- **Table names**: `workshops`, `bookings`, `profiles` (not events/registrations)
- **Workshop formats**: `in_person`, `online`, `hybrid`
- **Datetime handling**: All datetimes stored in UTC
- **Schema reference**: Check `types/database.types.ts` or use supabase-dev MCP

## Migration Best Practices

Write migrations as atomic operations - the CLI automatically wraps them in transactions.

## Development Tips

- Use `pnpm run check` after large changes to ensure it compiles, passes linting and is well formatted

## Directory-specific instructions:

tests/e2e: @tests/e2e/CLAUDE.md
