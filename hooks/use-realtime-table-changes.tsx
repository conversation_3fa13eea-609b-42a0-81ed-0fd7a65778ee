import type { RealtimePostgresChangesPayload } from "@supabase/supabase-js";
import { useEffect, useState } from "react";

import { useSupabase } from "@/contexts/supabase-auth-provider";
import type { Database } from "@/types/database.types";

type TableName = keyof Database["public"]["Tables"];
type TableRow<T extends TableName> = Database["public"]["Tables"][T]["Row"];

export function useRealtimeTableChanges<T extends TableName>({
  schema = "public",
  table,
  filter,
  onTableChange,
}: {
  schema?: string;
  table: T;
  filter: {
    column: string;
    operator: "eq" | "neq" | "gt" | "gte" | "lt" | "lte";
    value: string | number | boolean;
  };
  onTableChange: (payload: RealtimePostgresChangesPayload<TableRow<T>>) => void;
}): { isConnected: boolean } {
  const { supabase } = useSupabase();
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Create a unique channel name based on table and filter
    const channelName = `${table}-changes-${Date.now()}`;

    // Create and subscribe to the channel
    const channel = supabase
      .channel(channelName)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema,
          table,
          filter: `${filter.column}=${filter.operator}.${filter.value}`,
        },
        (payload: RealtimePostgresChangesPayload<TableRow<T>>) => {
          onTableChange(payload);
        },
      )
      .subscribe((status) => {
        if (status === "SUBSCRIBED") {
          setIsConnected(true);
        }
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [supabase, schema, table, filter, onTableChange]);

  return { isConnected };
}
