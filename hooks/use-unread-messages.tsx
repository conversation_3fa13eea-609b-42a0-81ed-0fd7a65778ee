"use client";

import { useQuery } from "@tanstack/react-query";

import { createClient } from "@/utils/supabase/client";

/**
 * React Query hook to check if user has any unread messages
 * Uses efficient database function that stops at the first unread message found
 */
export function useUnreadMessages(userId: string | null) {
  return useQuery({
    queryKey: ["unread-messages", userId],
    queryFn: async () => {
      if (!userId) return false;

      const supabase = createClient();

      const { data, error } = await supabase.rpc(
        "get_user_has_unread_messages",
        {
          p_user_id: userId,
        },
      );

      if (error) {
        console.error("Error fetching unread messages:", error);
        throw error;
      }

      return typeof data === "boolean" ? data : false;
    },
    enabled: !!userId,
  });
}
