# Quote Creation as Message Attachment Plan

## Overview

Currently, the quote creation form directly creates both a quote and a message in a single operation. The new implementation will:

1. Create a quote as an attachment to a draft message
2. Visually indicate the quote attachment in the message composer
3. Allow the user to send the message with the quote attachment using the regular send button
4. Use a plugin system to allow different types of attachments without modifying the core message component

## Current Implementation Analysis

The current implementation has these key components:

- `components/messages/quote-form.tsx`: Form for creating quotes that sends both the quote and a message
- `components/quote-plugin.tsx`: Plugin for rendering quotes in messages
- `lib/attachment-plugins/registry.ts`: Registry for attachment plugins
- `app/messages/actions.ts`: Server actions for creating quotes and messages
- `components/chat-message.tsx`: Component for rendering messages with attachments
- `components/realtime-chat.tsx`: Component for the chat interface with message input

The current flow:

1. User opens quote form dialog
2. User fills out quote details and message content
3. On submit, `createQuote` action creates a quote and a message with the quote attached
4. The message is displayed with the quote attachment using the `QuoteAttachmentPlugin`

## Implementation Plan

### 1. Update Quote Form Component

### 3. Modify Quote Form Component

**File: `/components/messages/quote-form.tsx`**

- Remove message sending functionality
- Change to create a quote object without sending it
- Add the quote to the draft message context as an attachment
- Update UI to indicate it's creating an attachment, not sending a message

### 4. Create Attachment Preview Component

**File: `/components/messages/attachment-preview.tsx`**

- Create a component to display attachment previews in the message composer
- Support different attachment types through the plugin system
- Allow removing attachments before sending

### 5. Update Message Composer Component

**File: `/components/realtime-chat.tsx`**

- Integrate with draft message context
- Display attachment previews in the message composer
- Modify send message logic to include attachments
- Add visual indicator for attachments

### 6. Update Server Actions

**File: `/app/messages/actions.ts`**

- No changes needed to the existing `createQuote` action
- We'll use the existing action as-is when sending a message with a quote attachment
- This maintains the existing pattern where quotes are always tied to messages

### 7. Update Attachment Plugin System

**File: `/lib/attachment-plugins/registry.ts`**

- Extend plugin interface to support rendering in different contexts (message view vs. composer preview)
- Add methods to handle different rendering contexts without unnecessary data transformation

**File: `/components/quote-plugin.tsx`**

- Update to implement the extended plugin interface
- Add support for rendering in the composer preview

## Implementation Order

1. Implement draft message context with quote data support
2. Update quote plugin to support previews
3. Create quote preview component
4. Use the existing createQuote action without modifications
5. Modify quote form to store quote data in context
6. Update message composer to display quote previews

## Future Extensions

While this implementation is focused specifically on quotes, the pattern could be extended to support other attachment types in the future:

- File attachments
- Image attachments
- Event invitations
- Polls
- Payment requests

For each new attachment type, we would:

1. Add specific data handling in the draft message context
2. Create a dedicated preview component
3. Implement server-side handling in appropriate server actions
4. Update the plugin system to render the new attachment type
