# User Flows - Current Implementation

This document maps out the current user flows in the Wellness Events Marketplace application, distinguishing between different user roles (logged out users, clients, and providers) based on the actual code implementation.

## Authentication State Machine

```mermaid
stateDiagram-v2
    [*] --> LoggedOut

    state LoggedOut {
        [*] --> BrowsePublic
        BrowsePublic --> Login
        BrowsePublic --> Signup
        Login --> LoggedIn
        Signup --> ConfirmEmail
        ConfirmEmail --> Login
    }

    state LoggedIn {
        state fork_state <<fork>>
        [*] --> fork_state
        fork_state --> ClientFlow: User is client
        fork_state --> ProviderFlow: User is provider
    }

    LoggedIn --> LoggedOut: Logout
```

## Client User Flow State Machine

```mermaid
stateDiagram-v2
    [*] --> ClientDashboard

    state ClientDashboard {
        [*] --> ViewProfile
        ViewProfile --> EditProfile
        [*] --> BrowseWorkshops
        BrowseWorkshops --> ViewWorkshopDetails
        ViewWorkshopDetails --> BookWorkshop
        BookWorkshop --> Checkout
        Checkout --> PaymentConfirmation
        PaymentConfirmation --> ViewBookings
    }
```

## Provider User Flow State Machine

```mermaid
stateDiagram-v2
    [*] --> ProviderOnboarding

    state ProviderOnboarding {
        [*] --> CreateProviderProfile: First login
        CreateProviderProfile --> ProviderDashboard
    }

    state ProviderDashboard {
        [*] --> ManageWorkshops
        ManageWorkshops --> CreateWorkshop
        ManageWorkshops --> EditWorkshop
        ManageWorkshops --> ViewOwnWorkshopDetails
        [*] --> ManageBookings
        ManageBookings --> ViewBookingDetails
        [*] --> ViewProfile
        ViewProfile --> EditProfile
        [*] --> BrowseWorkshops
        BrowseWorkshops --> ViewOtherWorkshopDetails
        ViewOtherWorkshopDetails --> BookWorkshop: Currently allowed
        BookWorkshop --> Checkout
        Checkout --> PaymentConfirmation
    }
```

## Detailed User Journeys

### Logged Out User Journey

1. Can browse the homepage and view available workshops
2. Can view workshop details but cannot book
3. Must sign up or log in to proceed with booking
4. Can choose to be a client or provider during signup

### Client User Journey

1. Can browse and view all workshops
2. Can book workshops for their team
3. Can view their profile and booking history
4. Can manage their upcoming and past bookings
5. Can edit their profile information

### Provider User Journey

1. Must complete provider profile setup after registration
2. Can create and manage their own workshops
3. Can view and manage bookings for their workshops
4. Has access to a provider dashboard with workshop and booking management
5. Can edit their provider profile and organization details
6. **Can also book other providers' workshops** (current implementation doesn't restrict this)
7. Can view their booked workshops in their profile

## Current Implementation Analysis

### User Role Implementation

- User roles are stored in the user metadata during signup (`is_provider` flag in auth signup, confirmed in `components/auth/signup-form.tsx`)
- User profiles are created in the `profiles` table with corresponding role information
- The `getUser()` function in `lib/auth.ts` retrieves the current user session
- The `requireProvider` and `requireProviderWithDetails` functions in `lib/auth.ts` enforce provider-only access to certain routes

### Navigation Implementation

- The navbar component (`components/navbar.tsx`) conditionally renders navigation items based on authentication status and user role
- The profile page (`app/profile/page.tsx`) shows different UI elements based on user role
- The dashboard page (`app/dashboard/page.tsx`) is specifically for providers to manage their workshops and bookings

### Booking Process Implementation

- The workshop registration component (`components/workshops/workshop-registration.tsx`) allows any logged-in user to book workshops without role checking
- If a user tries to book without being logged in, they are redirected to the login page (line 39 in `workshop-registration.tsx`)
- The checkout form (`components/checkout/checkout-form.tsx`) processes payments and creates booking records for any authenticated user
- Bookings are linked to the user ID via `client_id` field regardless of whether they are actually a client or provider

### Missing Role-Based Restrictions

- **No restriction on providers booking workshops** - Confirmed in code review; the `WorkshopRegistration` component only checks if a user is authenticated, not their role
- **Booking process available to all users** - The "Book for Your Team" button is shown to all authenticated users
- **No validation in backend** - The bookings table accepts entries from any authenticated user ID in the `client_id` field

## Proposed Improvements

### Role-Based UI Elements

- Modify the `WorkshopRegistration` component to check user role and disable the booking button for providers. Add messaging to tell them to log in as a client account to book.
- Add conditional rendering in the workshop detail page based on user role
- Update the checkout process to validate that the user is a client before allowing booking

### Database Constraints

- Add Row Level Security (RLS) policies to enforce role-based access control at the database level
- Add validation in the booking creation process to ensure only clients can create bookings

### User Experience Enhancements

- Add clear messaging for providers viewing workshops to indicate they cannot book them
- Provide alternative actions for providers (e.g., "Contact Provider" instead of "Book Workshop")
- Improve the dashboard to show a clearer distinction between provider and client functionality
