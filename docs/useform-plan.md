# Plan to Standardize Form Implementation with react-hook-form

## Current Status

After reviewing all form components in the codebase, I've identified the following forms:

### Forms Already Using `useForm`

1. **Profile Form** (`components/profile/profile-form.tsx`)
2. **Workshop Form** (`components/dashboard/workshop-form.tsx`)
3. **Checkout Form** (`components/checkout/checkout-form.tsx`)
4. **Login Form** (`components/auth/login-form.tsx`)
5. **Signup Form** (`components/auth/signup-form.tsx`)

### Forms NOT Using `useForm`

1. **Workshop Registration Form** (`components/workshops/workshop-registration.tsx`)
   - Currently using a custom form implementation with React state
   - Submits via a direct server action call

## Implementation Plan

### 1. Update Workshop Registration Form

The Workshop Registration form needs to be updated to use `react-hook-form` for consistency with the rest of the application. Here's the detailed implementation plan:

#### Step 1: Add Schema Definition

Create a Zod schema for the workshop registration form in `lib/schemas.ts`:

```typescript
// Add to lib/schemas.ts
export const workshopRegistrationSchema = z.object({
  quantity: z
    .number()
    .min(1, { message: "Minimum 1 participant required" })
    .max(10, { message: "Maximum participants exceeded" }),
});
```

#### Step 2: Update Component Imports

Update the imports in `components/workshops/workshop-registration.tsx`:

```typescript
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { workshopRegistrationSchema } from "@/lib/schemas";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
```

#### Step 3: Implement useForm

Replace the current state-based implementation with `useForm`:

```typescript
// Replace useState(1) with useForm
const form = useForm<z.infer<typeof workshopRegistrationSchema>>({
  resolver: zodResolver(workshopRegistrationSchema),
  defaultValues: {
    quantity: 1,
  },
});

// Get quantity from form values instead of state
const quantity = form.watch("quantity");
const totalPrice = pricePerPerson * quantity;
```

#### Step 4: Update Form Submission

Modify the handleSubmit function to work with react-hook-form:

```typescript
// Update handleSubmit function
async function handleSubmit(
  values: z.infer<typeof workshopRegistrationSchema>,
) {
  setError(null);

  startTransition(async () => {
    const result = await registerForWorkshop(
      workshopId,
      values.quantity,
      pricePerPerson * values.quantity,
    );

    // If we get here, it means the redirect didn't happen, which indicates an error
    if (result?.error) {
      setError(result.error);
      if (result.redirectUrl) {
        router.push(result.redirectUrl);
      }
    }
  });
}
```

#### Step 5: Update Form UI

Update the form UI to use FormField components while maintaining the same design:

```tsx
<Form {...form}>
  <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
    <FormField
      control={form.control}
      name="quantity"
      render={({ field }) => (
        <FormItem>
          <FormLabel htmlFor="quantity">Number of participants</FormLabel>
          <div className="mt-1.5 flex items-center">
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() => field.onChange(Math.max(1, field.value - 1))}
              disabled={field.value <= 1 || isProvider || isPending}
            >
              -
            </Button>
            <FormControl>
              <Input
                id="quantity"
                type="number"
                min="1"
                max={maxAllowed}
                value={field.value}
                onChange={(e) =>
                  field.onChange(Number.parseInt(e.target.value) || 1)
                }
                className="mx-2 text-center"
                disabled={isProvider || isPending}
              />
            </FormControl>
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={() =>
                field.onChange(Math.min(maxAllowed, field.value + 1))
              }
              disabled={field.value >= maxAllowed || isProvider || isPending}
            >
              +
            </Button>
          </div>
          <p className="mt-1 text-sm text-muted-foreground">
            Number of employees that will attend
          </p>
          <FormMessage />
        </FormItem>
      )}
    />

    {/* Rest of the form remains the same */}

    <Button
      className="w-full"
      size="lg"
      type="submit"
      disabled={isPending || isProvider}
    >
      {isPending
        ? "Processing..."
        : isProvider
          ? "Not Available for Providers"
          : "Book for Your Team"}
    </Button>
  </form>
</Form>
```

## Benefits of Standardization

1. **Consistency**: All forms will follow the same pattern, making the codebase more maintainable
2. **Type Safety**: Leveraging Zod schemas with TypeScript for better type checking
3. **Form Validation**: Consistent validation approach across all forms
4. **Developer Experience**: Easier onboarding for new developers with a standard pattern
5. **Error Handling**: Standardized error display and validation messaging

## Implementation Timeline

1. Update Workshop Registration Form - Estimated time: 1-2 hours
2. Testing and validation - Estimated time: 1 hour

## Next Steps

1. Create a pull request with the changes
2. Document the standardized form pattern for future reference
3. Consider creating a reusable form hook or component for common form patterns
4. Add unit tests for form validation
