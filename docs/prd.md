## 1. Introduction

## 1.1 Purpose

The Wellness Workshops Marketplace is a platform designed to connect wellness workshop providers with businesses seeking corporate wellness events. The platform enables providers to showcase their offerings with flexible scheduling options, while businesses can browse, inquire about, and book workshops for their employees.

## 1.2 Scope

This document outlines the requirements for the Wellness Workshops Marketplace, distinguishing between MVP (Minimum Viable Product) features and future enhancements. The MVP will focus on essential functionality to enable the core user journey, while additional features will be implemented in subsequent releases.

## 1.3 Definitions

- **Provider Organization**: Wellness businesses that offer workshops (yoga studios, meditation centers, nutrition practices, etc.)
- **Provider User**: Individual wellness professionals who are attached to a provider organization and use the platform
- **Client/Business**: Companies looking to book wellness workshops for their employees
- **Employees :** Employee of the company
- **Workshop**: A wellness session offered by a provider with flexible scheduling
- **Booking**: A confirmed workshop reservation with a specific date, time, and payment

# 2. Product Overview

## 2.1 Product Vision

To create a hassle free marketplace for corporate wellness workshops, connecting quality providers with businesses seeking to improve employee wellbeing through engaging workshops.

## 2.2 Target Users

### 2.2.1 Providers

- Wellness professionals (yoga instructors, meditation coaches, nutritionists, fitness trainers, etc.)
- Small wellness businesses looking to expand their corporate client base
- Independent contractors in the wellness space

### 2.2.2 Clients (Businesses)

- HR departments and wellness program managers
- Corporate event planners
- Team managers looking to organize team-building activities
- Companies of all sizes interested in employee wellbeing initiatives

## 2.3 User Journeys

### 2.3.1 Provider Journey

1. Register as a provider user and create/join a provider organization
2. List workshops with details, images, and flexible scheduling options
3. Receive booking inquiries from businesses
4. Finalize workshop details with the client
5. Confirm bookings and receive payments
6. Deliver workshops and mark them as done

### 2.3.2 Business Journey

1. Register as a business user
2. Browse and search for workshops based on type, location, price, etc.
3. View detailed workshop information and provider profiles
4. Inquire about specific workshops and discuss details with providers. Common queries include:
   1. Availability
   2. Price
   3. Min / Max capacity
   4. Location
   5. What happens in the workshop
5. Book and pay for workshops
6. Coordinate workshop session - e.g. setup, arrival time, material
7. Attend workshops and leave reviews

# 3. Feature Requirements

## 3.1 MVP Features

The following features represent the minimum functionality required for the initial product launch:

### 3.1.1 User Authentication and Profiles

**Provider Profiles**

- Registration and login functionality
- Combined form for creating both provider user and provider organization profiles
- Provider User profile with:
  - Name
  - Email
  - Phone number
  - Authentication credentials
- Provider Organization profile with:
  - Business name
  - Business description
  - Profile photo
  - Contact information
- Database representation as two separate entities with a relationship between them
- For MVP, each provider user is attached to exactly one provider organization

**Business Profiles**

- Registration and login functionality
- Basic company information:
  - Company name
  - Location
  - Contact information
    - Contact name
    - Contact email
    - Contact phone number

### 3.1.2 Workshop Management

**Workshop Creation (Providers)**

- Workshop title and description
- Workshop category/type
- Duration
- Base pricing
- Location options (provider's venue, client's office, virtual)
- If provider venue, provider list venue location
- Simple availability preferences (days of week)
- Basic images
- Pre-requisites - e.g. need cooking equipment, etc

**Workshop Discovery (Businesses)**

- Browse workshops by category
- Basic search functionality with limited filters:
  - Workshop category/type
  - Location/delivery method
  - Price range
  - Availability - can use days

**Workshop Details Page**

- Workshop information
- Provider profile (excluding contact info)
- Pricing details
- Availability info
- Call-to-action for leaving an enquiry - this could then send an email to provider contact, but they would need a way to respond back to the business
- Call-to-action for booking

### 3.1.3 Booking and Payment Process

**Booking Flow**

- Simple booking form with:
  - Date and time selection
  - Participant count
  - Location confirmation
- Booking summary review
- Booking lead time should be at least 1 week in advance
- Terms and conditions acceptance

**Payment Processing**

- Secure payment gateway integration (Stripe)
- Credit card payment method
- Basic receipt generation
- Simple cancellation policy

### 3.1.4 Dashboard and Management Tools

**Provider Dashboard**

- Workshop management (create, edit, delete)
- Booking requests and status
- Simple list view of scheduled workshops
- Basic payment history

**Business Dashboard**

- Upcoming and past workshops
- Basic booking history
- Provider contact information

# 4. Non-Functional Requirements

## 4.1 Performance

- Page load times under 2 seconds
- Support for concurrent users
- Responsive design for all device types
- Efficient search and filtering

## 4.2 Security

- Secure user authentication
- Payment information protection (PCI compliance)
- Data encryption
- Privacy controls
- GDPR and other regulatory compliance

## 4.3 Reliability

- 99.9% uptime
- Data backup and recovery procedures
- Error handling and logging

## 4.4 Scalability

- Architecture that supports user growth
- Database design for efficient scaling
- Cloud infrastructure with auto-scaling capabilities

# 5. Future Enhancements (Post-MVP)

The following features will be implemented in subsequent releases after the MVP launch:

## 5.1 Enhanced User Profiles

- Provider Organization profiles with:
  - Credentials and certifications
  - Portfolio of past work (images, testimonials)
  - Detailed service offerings
- Provider User management:
  - Multiple provider users per organization
  - Role-based permissions (admin, instructor, assistant)
  - User-specific credentials and expertise
- Business profiles with:
  - Industry information
  - Company size
  - Multiple locations
  - Department structure

## 5.2 Messaging System

- In-app messaging between providers and businesses
- Message notifications
- File and image sharing
- Message templates for common inquiries
- Chat history and archiving

## 5.3 Basic Review System

- Simple star rating
- Basic written feedback option

## 5.4 Advanced Workshop Management

- Detailed workshop information
- Equipment requirements
- Special accommodations
- Multiple images and media
- Availability calendar with time slots
- Workshop packages and series options

## 5.5 Advanced Search and Discovery

- Enhanced filtering options
- Sorting by multiple criteria
- Similar workshop suggestions
- Personalized recommendations
- Featured workshops section

## 5.6 Advanced Booking and Payment

- Multiple payment methods (credit card, ACH, etc.)
- Deposit options for larger bookings
- Detailed invoicing
- Flexible refund and cancellation policies
- Payment status tracking

## 5.7 Enhanced Dashboard Features

- Calendar view of scheduled workshops
- Analytics and reporting
- Client/provider relationship management
- Workshop materials and resources sharing
- Advanced review system with multiple categories
- Review moderation and provider responses

## 5.8 Integration Features

- Calendar integration (Google Calendar, Outlook)
- Contract generation and e-signatures
- Subscription options for recurring workshops
- Provider verification and certification badges
- Business verification
- Mobile applications

# 6. Technical Requirements

## 6.1 Platform Architecture

- Next.js frontend with React and TypeScript
- Supabase for authentication, database, and storage
- Server Components for data fetching
- Server Actions for API endpoints
- Responsive design with Tailwind CSS and shadcn/ui
- Database schema supporting the separation of provider users and provider organizations

## 6.2 Integrations

- Payment processor (Stripe)
- Email service provider
- Analytics tools
- Cloud storage

# 7. Success Metrics

## 7.1 Business Metrics

- Number of registered providers and businesses
- Workshop listings growth
- Booking conversion rate
- Average booking value
- Repeat bookings
- Platform revenue

## 7.2 User Satisfaction Metrics

- Provider satisfaction ratings
- Business satisfaction ratings
- Net Promoter Score (NPS)
- Feature usage statistics
- Support ticket volume and resolution time

# 8. Launch Plan

## 8.1 MVP Timeline

- Alpha release: Internal testing
- Beta release: Limited provider and business testing
- Public launch: Full platform availability

## 8.2 Marketing Strategy

- Provider acquisition strategy
- Business outreach plan
- Content marketing approach
- Social media strategy
- Launch events and promotions

# 9. Appendix

## 9.1 Competitive Analysis

- Overview of similar platforms
- Differentiating factors
- Market positioning

## 9.2 User Research Findings

- Key insights from provider interviews
- Business needs assessment
- Pain points addressed by the platform
