- [ ] Don't use RLS - disable client side supabase.
- [ ] Deduplicate functions and types
- [x] Figure out the tailwind colors problem (quote cards)
- [ ] System to automatically update supabase schema docs
- [ ] Use supabase declarative schema
- [ ] Logging and monitoring
- [ ] Integration tests
- [x] Chat
  - [x] Don't use supabase realtime for chat messages; just use it as a notification system to refresh the page
  - [x] Emails should go to the provider person who's actually in the chat
  - [x] Don't press En<PERSON> to send; encourage longer messages
- [ ] Separate server/client/common libs
- [ ] Remove optional parameters and fix other low hanging type issues
- [ ] Remove ResponsiveDataDisplay
- [x] Don't read .env file directly
