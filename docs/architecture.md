# Wellness Workshops Marketplace - Architecture Documentation

This document outlines the architecture of the Wellness Workshops Marketplace application, explaining how different components interact and how data flows through the system.

## System Overview

The Wellness Workshops Marketplace is a full-stack application built with:

- **Frontend**: Next.js 14+ (App Router), React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (Authentication, Database, Storage)
- **State Management**: React's built-in state management with Server Components and Server Actions
- **Form Handling**: React Hook Form with Zod validation

## Architecture Diagram

<!-- TODO: Add architecture diagram -->

## Server vs. Client Components

Next.js allows for both server and client components, each with their own strengths and use cases. This section outlines when to use each type of component and provides guidelines for component architecture in the Wellness Events Marketplace.

### When to Use Server Components

Server components should be the default choice for most components in the application. Use server components when:

1. **Data Fetching**: The component needs to fetch data from the database or external APIs.

   - Example: Workshop listing pages, user profile data display
   - Benefit: Eliminates client-side data fetching waterfalls and reduces JavaScript bundle size

2. **SEO and Initial Load**: The component contains content that should be indexed by search engines or visible on initial page load.

   - Example: Workshop details, company information, pricing
   - Benefit: Content is rendered on the server and sent as HTML, improving SEO and initial load performance

3. **Access to Backend Resources**: The component needs direct access to backend resources that shouldn't be exposed to the client.

   - Example: Database queries, environment variables, sensitive business logic
   - Benefit: Keeps sensitive operations on the server

4. **Parameter Validation**: The component needs to validate URL or form parameters before rendering.

   - Example: Workshop filters, search functionality
   - Benefit: Ensures data integrity and improves security

5. **Role-Based Access Control**: The component needs to check user permissions before rendering content.
   - Example: Admin dashboards, provider-only features
   - Benefit: Prevents unauthorized access to sensitive features

### When to Use Client Components

Client components should be used selectively for interactive UI elements. Use client components when:

1. **User Interactivity**: The component requires user interactions like clicking, typing, or dragging.

   - Example: Forms, interactive filters, buttons with complex behavior
   - Benefit: Provides immediate feedback without server roundtrips

2. **Browser APIs**: The component needs access to browser-only APIs like localStorage, navigator, or window.

   - Example: Components that use geolocation, local storage for preferences
   - Benefit: Enables browser-specific functionality

3. **Client-Side State**: The component needs to maintain state that changes frequently based on user interaction.

   - Example: Shopping carts, multi-step forms, interactive wizards
   - Benefit: Provides a responsive user experience

4. **Effects and Lifecycle**: The component needs to use React hooks like useEffect or useState.

   - Example: Components with animations, timers, or subscriptions
   - Benefit: Enables component lifecycle management

5. **Third-Party Libraries**: The component uses third-party libraries that depend on client-side JavaScript.
   - Example: Components using charting libraries, date pickers
   - Benefit: Enables integration with client-side ecosystem

### Hybrid Approach Best Practices

For optimal performance and user experience, follow these best practices:

1. **Component Splitting**: Split components into server and client parts when needed.

   - Server component: Fetch and validate data, handle permissions
   - Client component: Handle interactivity and state management
   - Example: `WorkshopFilters` (server) and `WorkshopFiltersClient` (client)

2. **Props Passing**: Pass data from server components to client components via props.

   - Example: Pass validated filter parameters from server to client filter UI

3. **Server Actions**: Use server actions for data mutations instead of client-side API calls.

   - Example: Form submissions, data updates
   - Prefer pure server actions for form submissions rather than making forms client components just for loading states

4. **Progressive Enhancement**: Design components to work without JavaScript first, then enhance with client interactivity.

   - Example: Forms should submit without JavaScript, then add client validation

5. **Form Implementation**:
   - Forms don't need to be client components simply for handling loading states
   - Use server actions for form submission whenever possible
   - Only use client components for forms when client-side validation or complex interactive behavior is required
   - Consider using a hybrid approach where the form is a server component but uses a client component for validation

## Component Audit Results

Based on an audit of the existing codebase, the following components have been identified as candidates for conversion to server components or a hybrid approach:

### Already Converted

- ✅ **Navbar**: Converted to a server component that fetches user data and passes it to a client component for interactivity.
- ✅ **WorkshopFilters**: Converted to a server component that validates URL parameters and passes them to a client component.

### High-Priority Conversion Candidates

- 🔄 **WorkshopRegistration**: Currently a client component that handles workshop registration. Can be converted to:

  - Server component: Fetch workshop data, validate user permissions, implement server action for registration
  - Client component (minimal): Handle quantity selection with optimistic UI updates

- 🔄 **CheckoutForm**: Currently a client component for payment processing. Can be converted to:

  - Server component: Validate workshop availability, prepare checkout data, implement server action for checkout
  - Client component (if needed): Handle complex form validation or real-time feedback

- 🔄 **Profile Forms**: Currently client components. Can be converted to:
  - Server component with server actions for form submission
  - Client-side validation can be handled with minimal client components if necessary

### Medium-Priority Conversion Candidates

- 🔄 **Dashboard Components**: Components like `workshops-table.tsx` and `registrations-table.tsx` could benefit from server-side data fetching and filtering.

- 🔄 **Auth Forms**: Login and signup forms should use server components with server actions for form submission. Client components should only be used for immediate validation feedback if necessary.

### Components to Keep as Client Components

- ⚠️ **UI Components**: Most UI components from shadcn/ui should remain as client components due to their interactive nature.

- ⚠️ **ThemeProvider**: Must remain a client component as it uses browser APIs and React context.

## Implementation Strategy

When converting components from client to server or hybrid approach:

1. **Identify Data Requirements**: Determine what data the component needs and where it should be fetched.

2. **Extract Client Logic**: Move interactive elements to a separate client component.

3. **Create Server Component**: Implement a server component that fetches and validates data.

4. **Connect Components**: Pass data from server to client component via props.

5. **Test Thoroughly**: Ensure the component works correctly after conversion, especially for edge cases.

6. **Update Documentation**: Mark the component as converted in the plan.md file and update this document with any new patterns or learnings.

This strategy will be applied incrementally to prioritized components, starting with those that offer the most performance and maintainability benefits.
