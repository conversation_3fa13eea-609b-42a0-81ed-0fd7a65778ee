# Quote in Chat Feature Implementation Plan

This document outlines the plan to implement a feature allowing providers to send workshop quotes to clients within the chat interface.

## 1. Database Schema Design

New tables, enums, and modifications to existing tables are required.

**A. New Enum: `quote_status`**
(Used in the `quotes` table)

- `pending` (Default: Quote created, awaiting client action)
- `rejected` (Client rejects the terms)
- `expired` (Quote was not actioned before an optional expiry time)
- `paid` (C<PERSON> has accepted and paid)
- `superceded` (A new quote has been created that overrides this one)
- `cancelled` (Provider cancels the quote before client action or expiry)

**B. New Table: `quotes`**

Stores the details of a specific workshop instance quote.

| Column              | Type                  | Nullable | Default             | Key         | References                                        | Comment                                          |
| :------------------ | :-------------------- | :------- | :------------------ | :---------- | :------------------------------------------------ | :----------------------------------------------- |
| `id`                | `uuid`                | false    | `gen_random_uuid()` | PRIMARY KEY |                                                   | Unique ID for the quote                          |
| `workshop_id`       | `uuid`                | false    |                     | FOREIGN KEY | `public.workshops(id)`                            | The base workshop this quote is for              |
| `chat_room_id`      | `uuid`                | false    |                     | FOREIGN KEY | `public.chat_rooms(id) ON DELETE CASCADE`         | Chat room where the quote was proposed           |
| `provider_id`       | `uuid`                | false    |                     | FOREIGN KEY | `auth.users(id)`                                  | The provider (user) who created the quote        |
| `client_id`         | `uuid`                | false    |                     | FOREIGN KEY | `auth.users(id)`                                  | The client (user) this quote is for              |
| `proposed_datetime` | `timestamptz`         | false    |                     |             |                                                   | Proposed date and time for the workshop instance |
| `location`          | `text`                | false    |                     |             |                                                   | Full address or location description             |
| `price`             | `numeric(10,2)`       | false    |                     |             |                                                   | Quoted price                                     |
| `currency`          | `text`                | false    |                     |             | e.g., 'USD', 'AUD' (matches `workshops.currency`) | Currency of the price                            |
| `notes`             | `text`                | true     |                     |             |                                                   | Freeform notes from the provider                 |
| `status`            | `public.quote_status` | false    | `'pending'`         |             |                                                   | Status of the quote                              |
| `expires_at`        | `timestamptz`         | true     |                     |             |                                                   | Optional expiry date for the quote               |
| `created_at`        | `timestamptz`         | false    | `now()`             |             |                                                   | Timestamp when the quote was created             |
| `updated_at`        | `timestamptz`         | false    | `now()`             |             |                                                   | Timestamp when the quote was last updated        |

_Indexes: `idx_quotes_workshop_id`, `idx_quotes_chat_room_id`, `idx_quotes_provider_id`, `idx_quotes_client_id`, `idx_quotes_status`_
_RLS: To be configured (e.g., provider can manage their quotes, client can view quotes directed to them)._
_Trigger: `update_quotes_updated_at` before update._

**C. Modifications to `chat_messages` Table**

Add a column to optionally link to quotes.

| Column                     | Type   | Nullable | Default | Key         | References                             | Comment                                             |
| :------------------------- | :----- | :------- | :------ | :---------- | :------------------------------------- | :-------------------------------------------------- |
| ... (existing columns) ... |
| `quote_id`                 | `uuid` | true     | `NULL`  | FOREIGN KEY | `public.quotes(id) ON DELETE SET NULL` | Optional link to the quote attached to this message |

## 2. Implementation Plan

**Phase 1: Backend Setup & Quote Creation**

1.  **Database Migrations (SQL):**

    - Define and create the `quote_status` enum.
    - Create the `quotes` table with all specified columns, constraints, foreign keys, and indexes.
    - Set up the `updated_at` trigger for the `quotes` table.
    - Alter the `chat_messages` table to add the `quote_id` column.
    - Implement Row Level Security (RLS) policies for the `quotes` table.

2.  **Server Actions (e.g., Next.js Server Actions):**
    - `createQuote(quoteData)`:
      - **Inputs:** `chat_room_id`, `workshop_id` (derived from chat room context), `message_content` (text of the chat message), `proposed_datetime`, `location`, `price`, `currency`, `notes_for_quote` (distinct from message_content).
      - **Logic:**
        - Identify `provider_id` from authenticated session.
        - Identify `client_id` from `chat_room` participants.
        - Validate inputs using a Zod schema.
        - Perform a database transaction:
          1.  Insert a new record into the `quotes` table with `status = 'pending'` (using `notes_for_quote` for the quote's internal notes).
          2.  Insert a new record into `chat_messages` table with appropriate `sender_id` (provider), `room_id`, `content = message_content`, and `quote_id` (linking to the new quote).
      - **Return:** Success or error.
    - `getQuoteDetails(quoteId)`: (Helper for displaying quote card if not efficiently joined initially)
      - Fetches quote by ID. RLS policies will ensure authorization.

**Phase 2: Frontend - Provider UI (Quote Form & Chat Integration)**

1.  **"Attach Quote" Button (or similar UI in chat input area):**

    - Allows provider to initiate quote creation while composing a message or as an action on a composed message.

2.  **Quote Form (Dialog/Sheet component using shadcn/ui):**
    - Modal opens when the "Attach Quote" action is triggered.
    - **Fields for Quote Details:**
      - Date & Time (Calendar and time pickers)
      - Location (Textarea for full address/description)
      - Price
      - Currency (Dropdown)
      - Notes for Quote (Textarea - these are notes _on the quote itself_, visible on the quote card)
      - (Workshop associated with the chat room will be implicit).
    - The main chat message content will be entered in the regular chat input box, not duplicated in this form.
    - On submission, the quote details are prepared. The `createQuote` server action will be called with these details AND the content of the main chat input field.
    - Manage loading states and display success/error feedback.
    - Close dialog on successful submission, and the message with the attached quote should be sent/appear in chat.

**Phase 3: Frontend - Displaying Quote Card in Chat**

✅ **IMPLEMENTED:**

1.  **Attachment Plugin System:**

    - Created a flexible plugin registry system in `lib/attachment-plugins/registry.ts`
    - Implemented the `AttachmentPlugin` interface with `canRender` and `render` methods
    - Set up a singleton registry instance for managing plugins

2.  **Quote Attachment Plugin:**

    - Implemented `QuoteAttachmentPlugin` in `lib/attachment-plugins/quote-plugin.tsx`
    - Created a visually appealing quote card UI using shadcn/ui components
    - Added formatting utilities for dates, times, and currency
    - Implemented status badges with appropriate colors

3.  **Updated Chat Message Component:**

    - Modified `ChatMessageItem` to use the plugin system
    - Added support for rendering attachments alongside regular message content
    - Ensured proper styling for both own and other users' messages

4.  **Data Structure Updates:**

    - Updated `types/chat.ts` to include `quotes` field of type `QuoteWithWorkshopDetails`
    - Ensured consistent user object structure with `id` and `name` properties
    - Refactored `getChatMessages` in `app/messages/actions.ts` for better type safety
    - Implemented separate queries for messages, profiles, and quotes

5.  **Enhanced Real-time Chat Hook:**
    - Updated `useRealtimeChat` to accept and use `currentUserId`
    - Fixed type issues and improved error handling
    - Ensured proper message structure when sending messages

**Phase 4: Real-time Updates** ⏳ **TO BE IMPLEMENTED**

1.  **Supabase Realtime:**
    - Configure real-time subscriptions for the `chat_messages` table so new quote messages (and their cards) appear instantly.
    - Extend this so that if a quote's status changes, the card in the chat updates in real-time.

**Phase 5: Client Actions** ✅ **IMPLEMENTED**

1.  **Quote Response UI:**

    - Added "Accept Quote" and "Reject Quote" buttons to the quote card for clients
    - Implemented confirmation dialogs with toast notifications for user feedback
    - Added conditional rendering based on user role (provider vs. client)
    - Included loading states during quote acceptance/rejection

2.  **Server Actions:**
    - Implemented `acceptQuote(quoteId)` and `rejectQuote(quoteId)` server actions in `app/messages/actions.ts`
    - Added validation to ensure only authorized users can act on quotes
    - Updated `quotes.status` in the database based on client actions
    - Created system messages with NULL sender_id to notify about status changes

**Phase 6: Payment Integration** ⏳ **TO BE IMPLEMENTED**

1.  **Payment Flow:**

    - Integrate with a payment provider (e.g., Stripe) when a client accepts a quote
    - Create payment intent/session with quote details
    - Handle payment success/failure callbacks
    - Update `quotes.status` to `paid` upon successful payment

2.  **Booking Creation:**
    - Create a corresponding record in the `bookings` table using details from the accepted quote
    - Update the client's "Upcoming Workshops" list

**Phase 7: Maintenance & Enhancements** ⏳ **TO BE IMPLEMENTED**

1.  **Quote Lifecycle Management:**

    - Implement quote expiry logic to update status to `expired` if `expires_at` is passed
    - Add ability for providers to cancel pending quotes
    - Allow providers to create new versions of quotes (superseding old ones)

2.  **Notifications:**
    - Send email notifications for quote-related events
    - Implement in-app notifications for new quotes and status changes

## Implementation Summary

### ✅ Completed (May 17, 2025)

- Database schema design and types
- Plugin system for chat message attachments
- Quote attachment rendering in chat messages
- Server-side data fetching for quotes with workshop details
- Real-time chat integration with user identification
- Client actions for accepting/rejecting quotes
- System messages for quote status changes
- Database schema updates to support system messages (NULL sender_id)
- Updated RLS policies to allow system messages

### ⏳ Next Steps

1. Test the current implementation with real data
2. Implement real-time updates for quote status changes
3. Integrate payment processing
4. Add booking creation upon successful payment
5. Implement quote lifecycle management
