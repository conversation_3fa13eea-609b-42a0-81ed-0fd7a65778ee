# Supabase Schema Summary (Generated from db_dump.sql)

This document details the structure of the `public` schema based on the provided SQL dump.

## Custom Types & Enums

### Enums

- **`booking_status`**: `pending`, `confirmed`, `cancelled`, `completed`
- **`day_of_week`**: `monday`, `tuesday`, `wednesday`, `thursday`, `friday`, `saturday`, `sunday`
- **`payment_status`**: `pending`, `paid`, `refunded`, `failed`
- **`quote_status`**: `pending`, `rejected`, `expired`, `paid`, `superceded`, `cancelled`
- **`user_type`**: `provider`, `client`
- **`venue_type`**: `provider_location`, `client_location`, `provider_or_client_location`, `online`
- **`workshop_format`**: `in_person`, `online`, `hybrid`

### Composite Types

- **`time_slot`**:
  - `start_time`: time(0) without time zone
  - `end_time`: time(0) without time zone
- **`workshop_daily_availability`**:
  - `monday`: public.time_slot
  - `tuesday`: public.time_slot
  - `wednesday`: public.time_slot
  - `thursday`: public.time_slot
  - `friday`: public.time_slot
  - `saturday`: public.time_slot
  - `sunday`: public.time_slot

---

## Tables (`public` Schema)

### `bookings`

Stores workshop bookings.

| Column               | Type                     | Nullable | Default            | Key         | References      |
| :------------------- | :----------------------- | :------- | :----------------- | :---------- | :-------------- |
| id                   | uuid                     | false    | gen\*random_uuid() | PRIMARY KEY |                 |
| workshop_id          | uuid                     | false    |                    | FOREIGN KEY | `workshops(id)` |
| client_id            | uuid                     | false    |                    | FOREIGN KEY | `clients(id)`   |
| status               | booking_status           | false    | 'pending'          |             |                 |
| participant_count    | integer                  | false    |                    |             |                 |
| booking_datetime     | timestamp with time zone | false    |                    |             |                 |
| total_price          | numeric(10,2)            | false    |                    |             |                 |
| special_requirements | text                     | true     |                    |             |                 |
| terms_accepted       | boolean                  | false    | false              |             |                 |
| created_at           | timestamp with time zone | false    | now()              |             |                 |
| updated_at           | timestamp with time zone | false    | now()              |             |                 |

\_Indexes: `idx_bookings_client_id`, `idx_bookings_workshop_id`\*
_Trigger: `update_bookings_updated_at` before update_

---

### `categories`

Workshop categories.

| Column            | Type                     | Nullable | Default            | Key         | References            |
| :---------------- | :----------------------- | :------- | :----------------- | :---------- | :-------------------- |
| id                | uuid                     | false    | gen\*random_uuid() | PRIMARY KEY |                       |
| name              | text                     | false    |                    |             |                       |
| created_at        | timestamp with time zone | false    | now()              |             |                       |
| updated_at        | timestamp with time zone | false    | now()              |             |                       |
| category_group_id | uuid                     | true     |                    | FOREIGN KEY | `category_groups(id)` |

\_Index: `idx_categories_category_group_id`\*
_Trigger: `update_categories_updated_at` before update_

---

### `category_groups`

Groups for categories.

| Column     | Type                     | Nullable | Default            | Key         | References |
| :--------- | :----------------------- | :------- | :----------------- | :---------- | :--------- |
| id         | uuid                     | false    | gen\*random_uuid() | PRIMARY KEY |            |
| name       | text                     | false    |                    |             |            |
| created_at | timestamp with time zone | false    | now()              |             |            |
| updated_at | timestamp with time zone | false    | now()              |             |            |

\_Trigger: `update_category_groups_updated_at` before update (Note: Dump shows no trigger, but likely intended)\*

---

### `chat_rooms`

Stores chat rooms between clients and providers for specific workshops.

| Column                   | Type        | Nullable | Default            | Key         | References                                            | Comment                                                           |
| :----------------------- | :---------- | :------- | :----------------- | :---------- | :---------------------------------------------------- | :---------------------------------------------------------------- |
| id                       | uuid        | false    | gen\*random_uuid() | PRIMARY KEY |                                                       | Unique identifier for the chat room                               |
| workshop_id              | uuid        | false    |                    | FOREIGN KEY | `public.workshops(id) ON DELETE CASCADE`              | The workshop this chat room is associated with                    |
| client_id                | uuid        | false    |                    | FOREIGN KEY | `auth.users(id) ON DELETE CASCADE`                    | The ID of the client (user) who initiated the chat                |
| provider_organization_id | uuid        | false    |                    | FOREIGN KEY | `public.provider_organizations(id) ON DELETE CASCADE` | The ID of the provider organization responsible for the workshop  |
| created_at               | timestamptz | false    | now()              |             |                                                       | Timestamp when the chat room was created                          |
| updated_at               | timestamptz | false    | now()              |             |                                                       | Timestamp when the chat room was last updated (e.g., new message) |

\_Constraint: `unique_chat_room` UNIQUE (`workshop_id`, `client_id`)\*
_Indexes: `idx_chat_rooms_client_id`, `idx_chat_rooms_provider_organization_id`_
_RLS Enabled_

---

### `chat_messages`

Stores individual messages within a chat room.

| Column     | Type        | Nullable | Default            | Key         | References                                | Comment                                                     |
| :--------- | :---------- | :------- | :----------------- | :---------- | :---------------------------------------- | :---------------------------------------------------------- |
| id         | uuid        | false    | gen\*random_uuid() | PRIMARY KEY |                                           | Unique identifier for the message                           |
| room_id    | uuid        | false    |                    | FOREIGN KEY | `public.chat_rooms(id) ON DELETE CASCADE` | The chat room this message belongs to                       |
| sender_id  | uuid        | true     | NULL               | FOREIGN KEY | `auth.users(id) ON DELETE CASCADE`        | The user ID of the message sender. NULL for system messages |
| quote_id   | uuid        | true     |                    | FOREIGN KEY | `public.quotes(id) ON DELETE SET NULL`    | Optional reference to a quote attachment                    |
| content    | text        | false    |                    |             |                                           | The text content of the message                             |
| created_at | timestamptz | false    | now()              |             |                                           | Timestamp when the message was sent                         |

_Constraint: CHECK (`length(content) > 0`)\*
\_Indexes: `idx_chat_messages_room_id_created_at`, `idx_chat_messages_sender_id`_
_RLS Enabled_

#### RLS Policies for chat_messages

1. **Select Policy**: Users can select messages from rooms they are part of (as client or provider)

2. **Insert Policy**: Users can insert messages if:
   - They are authenticated AND
   - Either:
     - For regular messages: sender_id matches their auth.uid()
     - For system messages: sender_id is NULL
   - AND they have access to the chat room (as client or provider)

_Trigger: `on_new_chat_message_update_room` after insert (updates the chat_room.updated_at timestamp)_

---

### `clients`

Stores client information (linked to profiles).

| Column        | Type                     | Nullable | Default | Key         | References     |
| :------------ | :----------------------- | :------- | :------ | :---------- | :------------- |
| id            | uuid                     | false    |         | PRIMARY KEY | `profiles(id)` |
| company\*name | text                     | false    |         |             |                |
| location      | text                     | true     |         |             |                |
| created_at    | timestamp with time zone | false    | now()   |             |                |
| updated_at    | timestamp with time zone | false    | now()   |             |                |

\_Trigger: `update_clients_updated_at` before update\*

---

### `payments`

Stores payment details for bookings.

| Column         | Type                     | Nullable | Default            | Key         | References     |
| :------------- | :----------------------- | :------- | :----------------- | :---------- | :------------- |
| id             | uuid                     | false    | gen\*random_uuid() | PRIMARY KEY |                |
| booking_id     | uuid                     | false    |                    | FOREIGN KEY | `bookings(id)` |
| amount         | numeric(10,2)            | false    |                    |             |                |
| status         | payment_status           | false    | 'pending'          |             |                |
| payment_method | text                     | true     |                    |             |                |
| transaction_id | text                     | true     |                    |             |                |
| payment_date   | timestamp with time zone | true     |                    |             |                |
| created_at     | timestamp with time zone | false    | now()              |             |                |
| updated_at     | timestamp with time zone | false    | now()              |             |                |

\_Index: `idx_payments_booking_id`\*
_Trigger: `update_payments_updated_at` before update_

---

### `profiles`

Stores common user profile information, linked to `auth.users`.

| Column       | Type                     | Nullable | Default | Key         | References       |
| :----------- | :----------------------- | :------- | :------ | :---------- | :--------------- |
| id           | uuid                     | false    |         | PRIMARY KEY | `auth.users(id)` |
| email        | text                     | false    |         |             |                  |
| full\*name   | text                     | true     |         |             |                  |
| phone_number | text                     | true     |         |             |                  |
| user_type    | user_type                | false    |         |             |                  |
| created_at   | timestamp with time zone | false    | now()   |             |                  |
| updated_at   | timestamp with time zone | false    | now()   |             |                  |

\_Trigger: `update_profiles_updated_at` before update\*

---

### `provider_organizations`

Stores provider organization details.

| Column            | Type                     | Nullable | Default            | Key         | References |
| :---------------- | :----------------------- | :------- | :----------------- | :---------- | :--------- |
| id                | uuid                     | false    | gen\*random_uuid() | PRIMARY KEY |            |
| name              | text                     | false    |                    |             |            |
| description       | text                     | true     |                    |             |            |
| profile_photo_url | text                     | true     |                    |             |            |
| city              | text                     | true     |                    |             |            |
| country           | text                     | true     |                    |             |            |
| created_at        | timestamp with time zone | false    | now()              |             |            |
| updated_at        | timestamp with time zone | false    | now()              |             |            |

\_Trigger: `update_provider_organizations_updated_at` before update\*

---

### `providers`

Links user profiles (`profiles`) to provider organizations (`provider_organizations`).

| Column          | Type                     | Nullable | Default | Key         | References                   |
| :-------------- | :----------------------- | :------- | :------ | :---------- | :--------------------------- |
| id              | uuid                     | false    |         | PRIMARY KEY | `profiles(id)`               |
| created\*at     | timestamp with time zone | false    | now()   |             |                              |
| updated_at      | timestamp with time zone | false    | now()   |             |                              |
| organization_id | uuid                     | false    |         | FOREIGN KEY | `provider_organizations(id)` |
| role            | text                     | true     | 'admin' |             |                              |

\_Trigger: `update_providers_updated_at` before update\*

---

### `workshop_categories` (Junction Table)

Links workshops and categories (M:M).

| Column       | Type                     | Nullable | Default | Key         | References       |
| :----------- | :----------------------- | :------- | :------ | :---------- | :--------------- |
| workshop\*id | uuid                     | false    |         | PRIMARY, FK | `workshops(id)`  |
| category_id  | uuid                     | false    |         | PRIMARY, FK | `categories(id)` |
| created_at   | timestamp with time zone | false    | now()   |             |                  |

\_Indexes: `idx_workshop_categories_category_id`, `idx_workshop_categories_workshop_id`\*

---

### `workshops`

Stores workshop details.

| Column                   | Type                        | Nullable | Default             | Key         | References                   | Comment                                                    |
| :----------------------- | :-------------------------- | :------- | :------------------ | :---------- | :--------------------------- | :--------------------------------------------------------- |
| id                       | uuid                        | false    | gen\*random_uuid()  | PRIMARY KEY |                              |                                                            |
| provider_id              | uuid                        | false    |                     | FOREIGN KEY | `provider_organizations(id)` | FK points to organization, not individual provider profile |
| name                     | text                        | false    |                     |             |                              |                                                            |
| description              | text                        | true     |                     |             |                              |                                                            |
| image_url                | text                        | true     |                     |             |                              |                                                            |
| duration                 | text                        | false    |                     |             |                              |                                                            |
| min_capacity             | integer                     | true     |                     |             |                              |                                                            |
| max_capacity             | integer                     | true     |                     |             |                              |                                                            |
| format                   | workshop_format             | false    | 'in_person'         |             |                              |                                                            |
| location                 | text                        | true     |                     |             |                              |                                                            |
| venue_type               | venue_type                  | false    | 'provider_location' |             |                              |                                                            |
| prerequisites            | text                        | true     |                     |             |                              |                                                            |
| price                    | numeric(10,2)               | false    |                     |             |                              |                                                            |
| group_discount_available | boolean                     | false    | false               |             |                              |                                                            |
| created_at               | timestamp with time zone    | false    | now()               |             |                              |                                                            |
| updated_at               | timestamp with time zone    | false    | now()               |             |                              |                                                            |
| pricing_model            | text                        | false    | 'per_person'        |             |                              | Pricing model: per_person or total                         |
| client_site_travel_fee   | numeric(10,2)               | true     | 0                   |             |                              | Additional fee for client location                         |
| currency                 | text                        | false    | 'AUD'               |             |                              | Currency (AUD, IDR, SGD, MYR)                              |
| lead_time                | text                        | true     |                     |             |                              | Minimum lead time required (e.g. "2 weeks")                |
| availability             | workshop_daily_availability | true     |                     |             |                              | Daily time slots (start/end time or NULL)                  |
| published                | boolean                     | false    | true                |             |                              | Is workshop visible to users?                              |

\_Constraints: `workshops_currency_check`, `workshops_pricing_model_check`\*
_Index: `idx_workshops_provider_id`_
_Trigger: `update_workshops_updated_at` before update_

---

### `quotes`

Stores the details of workshop quotes sent to clients.

| Column                   | Type          | Nullable | Default           | Key         | References                                | Comment                                   |
| :----------------------- | :------------ | :------- | :---------------- | :---------- | :---------------------------------------- | :---------------------------------------- |
| id                       | uuid          | false    | gen_random_uuid() | PRIMARY KEY |                                           | Unique ID for the quote                   |
| workshop_id              | uuid          | false    |                   | FOREIGN KEY | `public.workshops(id)`                    | The base workshop this quote is for       |
| chat_room_id             | uuid          | false    |                   | FOREIGN KEY | `public.chat_rooms(id) ON DELETE CASCADE` | Chat room where the quote was proposed    |
| provider_organization_id | uuid          | false    |                   | FOREIGN KEY | `public.provider_organizations(id)`       | The provider organization for this quote  |
| client_id                | uuid          | false    |                   | FOREIGN KEY | `auth.users(id)`                          | The client this quote is for              |
| proposed_datetime        | timestamptz   | false    |                   |             |                                           | Proposed date and time for the workshop   |
| location                 | text          | false    |                   |             |                                           | Full address or location description      |
| price                    | numeric(10,2) | false    |                   |             |                                           | Quoted price                              |
| currency                 | text          | false    |                   |             |                                           | Currency of the price (e.g., 'AUD')       |
| notes                    | text          | true     |                   |             |                                           | Freeform notes from the provider          |
| status                   | quote_status  | false    | 'pending'         |             |                                           | Status of the quote                       |
| expires_at               | timestamptz   | true     |                   |             |                                           | Optional expiry date for the quote        |
| created_at               | timestamptz   | false    | now()             |             |                                           | Timestamp when the quote was created      |
| updated_at               | timestamptz   | false    | now()             |             |                                           | Timestamp when the quote was last updated |

_Indexes: `idx_quotes_workshop_id`, `idx_quotes_chat_room_id`, `idx_quotes_provider_organization_id`, `idx_quotes_client_id`, `idx_quotes_status`_
_Trigger: `update_quotes_updated_at` before update_
_RLS Enabled_

#### RLS Policies for quotes

1. **Provider Policy**: `Provider organization members can manage quotes`

   - Allows providers to perform all operations on quotes for their organization
   - USING: Provider's organization_id matches the quote's provider_organization_id

2. **Client View Policy**: `Clients can view their quotes`

   - Allows clients to view quotes where they are the client_id
   - USING: client_id = auth.uid()

3. **Client Update Policy**: `Clients can update their quotes`
   - Allows clients to update quotes where they are the client_id (for accepting/rejecting)
   - USING: client_id = auth.uid()
   - WITH CHECK: client_id = auth.uid()

---

## Views (`public` Schema)

### `chat_messages_with_sender`

Joins chat_messages with profiles (via auth.users) to include sender's full_name.

| Column           | Type        | Source                   | Comment                   |
| :--------------- | :---------- | :----------------------- | :------------------------ |
| id               | uuid        | chat_messages.id         | Message ID                |
| room_id          | uuid        | chat_messages.room_id    | Associated chat room      |
| sender_id        | uuid        | chat_messages.sender_id  | User who sent the message |
| content          | text        | chat_messages.content    | Message content           |
| created_at       | timestamptz | chat_messages.created_at | When message was sent     |
| sender_full_name | text        | profiles.full_name       | Sender's display name     |

---

### `chat_room_details_view`

Joins chat_rooms with workshops, profiles (for client), and provider_organizations.

| Column                                  | Type        | Source                                   | Comment                    |
| :-------------------------------------- | :---------- | :--------------------------------------- | :------------------------- |
| room_id                                 | uuid        | chat_rooms.id                            | Chat room ID               |
| room_created_at                         | timestamptz | chat_rooms.created_at                    | When room was created      |
| room_updated_at                         | timestamptz | chat_rooms.updated_at                    | When room was last updated |
| workshop_id                             | uuid        | chat_rooms.workshop_id                   | Associated workshop        |
| workshop_name                           | text        | workshops.name                           | Name of the workshop       |
| client_id                               | uuid        | chat_rooms.client_id                     | Client user ID             |
| client_full_name                        | text        | profiles.full_name                       | Client's display name      |
| provider_organization_id                | uuid        | chat_rooms.provider_organization_id      | Provider organization ID   |
| provider_organization_name              | text        | provider_organizations.name              | Organization name          |
| provider_organization_profile_photo_url | text        | provider_organizations.profile_photo_url | Organization profile photo |
