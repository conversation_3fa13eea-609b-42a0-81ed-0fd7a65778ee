# Chat Signal Implementation Plan

## Overview

This document outlines a plan to improve the real-time chat implementation in PulseSpace. The current approach uses Supabase channels to directly transfer message data between clients, which creates potential inconsistencies and inefficiencies. The new approach will use Supabase channels purely for signaling that a message has been added, and rely on Next.js's data fetching mechanisms to retrieve the actual message content.

## Current Implementation

The current implementation has these characteristics:

1. `useRealtimeChat` hook:

   - Creates a Supabase channel subscription
   - Sends complete message objects through the channel
   - Maintains local state for messages
   - Merges local messages with initial messages in the component

2. Message storage:

   - Messages are stored in the database via `storeChatMessage` server action
   - The same message content is also sent through the Supabase channel

3. Issues with current approach:
   - Duplicate data flow (database + realtime channel)
   - Potential for message inconsistencies
   - Inefficient use of the realtime channel (sending full message objects)

## Proposed Implementation

### 1. Create a New Signaling Hook

Replace the current `useRealtimeChat` hook with a new `useRealtimeChatSignal` hook that only handles signaling:

```typescript
// hooks/use-realtime-chat-signal.tsx
import { useCallback, useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";

export function useRealtimeChatSignal({
  roomId,
  onNewMessage,
}: {
  roomId: string;
  onNewMessage: () => void;
}) {
  const supabase = createClient();
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Create a channel for signaling only
    const channel = supabase.channel(`chat:${roomId}`);

    channel
      .on("broadcast", { event: "NEW_MESSAGE" }, () => {
        // When we receive a signal, trigger the callback to refresh data
        onNewMessage();
      })
      .subscribe(async (status) => {
        if (status === "SUBSCRIBED") {
          setIsConnected(true);
        }
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [roomId, onNewMessage, supabase]);

  // Function to signal other participants that a new message was added
  const signalNewMessage = useCallback(() => {
    if (!isConnected) return;

    supabase.channel(`chat:${roomId}`).send({
      type: "broadcast",
      event: "NEW_MESSAGE",
      payload: {}, // Empty payload, just a signal
    });
  }, [roomId, isConnected, supabase]);

  return { isConnected, signalNewMessage };
}
```

### 2. Update the RealtimeChat Component

Modify the existing client component to use the new signaling approach:

```typescript
// components/realtime-chat.tsx
"use client";

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { useRealtimeChatSignal } from '@/hooks/use-realtime-chat-signal';
import { ChatMessageItem } from '@/components/chat-message-item';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Send, PlusCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { type ChatMessage } from '@/types/chat';

export interface RealtimeChatProps {
  roomId: string;
  username: string;
  currentUserId: string;
  messages: ChatMessage[];
  onMessageSent?: (message: ChatMessage) => void;
  onOpenCreateQuoteDialog?: () => void;
  isClient?: boolean;
}

export const RealtimeChat = ({
  roomId,
  username,
  currentUserId,
  messages,
  onMessageSent,
  onOpenCreateQuoteDialog,
  isClient,
}: RealtimeChatProps) => {
  const [newMessage, setNewMessage] = useState('');
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  // Use the signaling hook
  const { isConnected, signalNewMessage } = useRealtimeChatSignal({
    roomId,
    onNewMessage: () => {
      // Refresh the page data when a new message is signaled
      router.refresh();
    }
  });

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !isConnected || isPending) return;

    const message = newMessage.trim();
    setNewMessage('');

    startTransition(async () => {
      try {
        // Send the message to the database
        const response = await fetch('/api/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            roomId,
            content: message,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to send message');
        }

        // Signal to other participants that a new message was added
        signalNewMessage();

        // Refresh the page to show the new message
        router.refresh();

        // Call the onMessageSent callback if provided
        if (onMessageSent) {
          const data = await response.json();
          onMessageSent(data.message);
        }
      } catch (error) {
        console.error('Error sending message:', error);
      }
    });
  };

  return (
    <div className="flex flex-1 flex-col bg-background text-foreground antialiased">
      <div className="relative flex-1">
        <div className="absolute inset-0 flex flex-col-reverse overflow-y-auto p-4">
          <div>
            {messages.length === 0 ? (
              <div className="text-center text-sm text-muted-foreground">
                No messages yet. Start the conversation!
              </div>
            ) : null}
            {messages.map((message, index) => {
              const prevMessage = index > 0 ? messages[index - 1] : null;
              const showHeader =
                !prevMessage || prevMessage.user?.name !== message.user?.name;

              return (
                <div key={message.id}>
                  <ChatMessageItem
                    message={message}
                    isOwnMessage={message.user?.id === currentUserId}
                    showHeader={showHeader}
                    isClient={isClient}
                  />
                </div>
              );
            })}
          </div>
        </div>
      </div>

      <form
        onSubmit={handleSendMessage}
        className="flex w-full items-center gap-2 border-t border-border p-4"
      >
        {onOpenCreateQuoteDialog && (
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full"
            onClick={onOpenCreateQuoteDialog}
            type="button"
          >
            <PlusCircle className="size-5" />
            <span className="sr-only">Create Quote</span>
          </Button>
        )}
        <Input
          className={cn(
            "flex-1 rounded-full bg-background text-sm transition-all duration-300",
          )}
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Type a message..."
          disabled={!isConnected}
        />
        {isConnected && newMessage.trim() && (
          <Button
            className="aspect-square rounded-full duration-300 animate-in fade-in slide-in-from-right-4"
            type="submit"
            disabled={!isConnected || !newMessage.trim() || isPending}
          >
            <Send className="size-4" />
          </Button>
        )}
      </form>
    </div>
  );
};
```

### 3. Create a New API Route for Sending Messages

Create a new API route to handle message sending:

```typescript
// app/api/messages/route.ts
import { NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import { storeChatMessage } from "@/app/messages/actions";

export async function POST(request: Request) {
  try {
    const { roomId, content } = await request.json();

    if (!roomId || !content) {
      return NextResponse.json(
        { error: "Room ID and content are required" },
        { status: 400 },
      );
    }

    const result = await storeChatMessage(roomId, content);

    if (!result.message) {
      return NextResponse.json(
        { error: result.error || "Failed to store message" },
        { status: 500 },
      );
    }

    // Revalidate the chat room page
    revalidatePath(`/messages/${roomId}`);

    return NextResponse.json({ message: result.message });
  } catch (error) {
    console.error("Error in messages API:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 },
    );
  }
}
```

### 4. Update the MessagesWithStorage Component

Modify the existing component to use the new approach:

```typescript
// components/messages/messages-with-storage.tsx
"use client";

import { useState } from "react";

import { QuoteForm } from "@/components/messages/quote-form";
import { RealtimeChat } from "@/components/realtime-chat";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { type ChatMessage } from "@/types/chat";
import type { TypedProfile } from "@/types/user";
import { isClient } from "@/types/user";

interface MessagesWithStorageProps {
  roomId: string;
  workshopId: string;
  messages: ChatMessage[];
  currentUser: TypedProfile;
  otherParticipant: {
    name: string;
    id: string;
  };
}

export function MessagesWithStorage({
  roomId,
  workshopId,
  messages,
  currentUser,
  otherParticipant,
}: MessagesWithStorageProps) {
  const [isQuoteDialogOpen, setIsQuoteDialogOpen] = useState(false);

  return (
    <>
      <RealtimeChat
        roomId={roomId}
        username={currentUser.full_name || ""}
        currentUserId={currentUser.id}
        messages={messages}
        onOpenCreateQuoteDialog={
          isClient(currentUser)
            ? undefined
            : () => setIsQuoteDialogOpen(true)
        }
        isClient={isClient(currentUser)}
      />

      <Dialog open={isQuoteDialogOpen} onOpenChange={setIsQuoteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create Quote</DialogTitle>
          </DialogHeader>
          <QuoteForm
            chatRoomId={roomId}
            workshopId={workshopId}
            clientId={otherParticipant.id}
            onSuccess={() => setIsQuoteDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
```

### 5. Update the Chat Room Page Component

Ensure the page component fetches messages and passes them to the client components:

```typescript
// app/messages/[roomId]/page.tsx
import { Suspense } from "react";
import { getChatMessages, getChatRoomDetails } from "@/app/messages/actions";
import { MessagesWithStorage } from "@/components/messages/messages-with-storage";
import { requireUser } from "@/lib/auth";

export default async function ChatRoomPage({
  params,
}: {
  params: { roomId: string };
}) {
  const { roomId } = params;
  const user = await requireUser();

  // Fetch room details and messages
  const { room } = await getChatRoomDetails(roomId);
  const { messages } = await getChatMessages(roomId);

  if (!room) {
    return <div>Chat room not found</div>;
  }

  // Determine the other participant
  const isUserClient = user.id === room.client?.id;
  const otherParticipant = {
    name: isUserClient
      ? room.provider_organization?.name || "Provider"
      : room.client?.full_name || "Client",
    id: isUserClient
      ? room.provider_organization?.id || ""
      : room.client?.id || "",
  };

  return (
    <div className="flex h-full flex-col">
      <Suspense fallback={<div>Loading chat...</div>}>
        <MessagesWithStorage
          roomId={roomId}
          workshopId={room.workshop?.id || ""}
          messages={messages || []}
          currentUser={user}
          otherParticipant={otherParticipant}
        />
      </Suspense>
    </div>
  );
}
```

## Components to Delete/Replace

1. **Delete**: `hooks/use-realtime-chat.tsx`

   - This will be replaced by the new `hooks/use-realtime-chat-signal.tsx`

2. **Modify**: `components/realtime-chat.tsx`

   - Keep the component but update its implementation as shown above

3. **Modify**: `components/messages/messages-with-storage.tsx`

   - Update to use the new approach but keep the component

4. **Add**: `app/api/messages/route.ts`
   - New API route for sending messages

## Types to Update

1. Move the `ChatMessage` type from `hooks/use-realtime-chat.tsx` to `types/chat.ts` if it's not already there.

## Implementation Steps

1. **Create the new hook**:

   - Create `useRealtimeChatSignal` in a new file
   - Delete the old `useRealtimeChat` hook after implementation is complete

2. **Update the RealtimeChat component**:

   - Modify to use the new signaling approach
   - Update props and implementation

3. **Create the API route**:

   - Add the new route for sending messages
   - Ensure it properly handles errors and revalidation

4. **Update the MessagesWithStorage component**:

   - Modify to work with the new approach
   - Update props to accept messages from the parent

5. **Update the Chat Room page**:

   - Ensure it fetches messages and passes them to client components
   - Add proper error handling and loading states

6. **Testing**:
   - Test with multiple users to ensure real-time updates work correctly
   - Verify that messages are consistent between users
   - Check performance improvements

## Benefits of This Approach

1. **Cleaner separation of concerns**:

   - Database is the single source of truth for messages
   - Real-time channel is only used for signaling

2. **More efficient**:

   - Smaller payloads over the real-time channel
   - Leverages Next.js's built-in caching and revalidation

3. **More reliable**:

   - No potential for message inconsistencies
   - All users see the same data from the database

4. **Better developer experience**:
   - Simpler mental model
   - Follows Next.js best practices for data fetching
