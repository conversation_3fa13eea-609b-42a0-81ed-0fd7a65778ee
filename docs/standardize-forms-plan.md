# Form Standardization Plan

This document outlines the plan to standardize forms across the Pulse Space application to ensure consistency in both appearance and behavior.

## Current State

Most forms follow similar patterns, but there are some inconsistencies in:

1. UI layout and styling
2. Form implementation details
3. Action handler implementations
4. Error and success state handling

## Recommendations

### 1. Form UI Components

- **Card Component Usage**: Standardize all forms to use the Card component

  - All forms should use Card, CardHeader, CardContent, CardFooter
  - CardHeader should contain CardTitle and CardDescription
  - Form content should be placed in CardContent
  - Action buttons should be placed in Card<PERSON>ooter

- **Spacing Consistency**:

  - Standardize form spacing to `space-y-4` for form element gaps
  - Use `space-y-8` for section gaps within forms
  - Use `space-x-4` for button groups

- **Button Styling**:
  - Primary action buttons should be full width (`w-full`) in auth forms
  - In admin/management forms, use right-aligned button groups
  - Cancel buttons should use `variant="outline"`
  - Consistently use loading spinners

### 2. Form Implementation

- **Form Schema**:

  - Place form schemas in a central location (e.g., `lib/schemas.ts`) if they need to be shared between client and
    server. Otherwise, put them directly in the file where used.
  - Use consistent naming convention: `nameSchema`
  - Export typed versions of form values: `type NameFormValues = z.infer<typeof nameSchema>`

- **Form State Management**:

  - Consistently initialize state variables:
    ```typescript
    const [error, setError] = useState<string | null>(null);
    const [isPending, startTransition] = useTransition();
    ```
  - Reset error state at the start of each submission

- **Optional Parameters**:
  - For string parameters, consistently use empty strings as defaults
  - For number parameters, use 0 as default
  - For boolean parameters, use false as default
  - Document the defaults in the parameter definitions

### 3. Action Handler Standardization

- **Type Definitions**:

  - Create consistent return types for all actions
  - Standardize on `{ success: boolean, error?: string }` pattern
  - Add specific fields for response data when needed

- **Parameter Handling**:

  - Define all action parameters explicitly with types
  - Use default parameter values for optional parameters
  - Validate required parameters at the start of the function

- **Error Handling**:

  - Use consistent error handling pattern:
    ```typescript
    try {
      // Operation code
    } catch (error) {
      return {
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      };
    }
    ```
  - Be careful not to perform any redirect inside a try clause because it will throw an error that we don't want to
    catch.

- **Cache Invalidation and Redirects**:
  - Only use `revalidatePath()` when there is no redirect
  - When using redirect, don't use revalidatePath as the redirect will cause a fresh page load

### 4. Implementation Plan

1. **Refactor Forms in Order**:
   - [ ] Auth forms (login, signup, password reset)
   - [ ] Profile forms
   - [ ] Workshop management forms
   - [ ] Checkout forms
