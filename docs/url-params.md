# Type-Safe URL Parameter Handling

This document explains our approach to handling URL parameters in a type-safe way using Zod schemas.

## Overview

URL parameters are a common source of bugs in web applications, as they are essentially untyped strings. In our application, we use Zod to validate and transform URL parameters into properly typed values.

## Key Components

### 1. Shared Schemas (`/lib/schemas.ts`)

We define all our validation schemas in one central place, allowing them to be reused between server and client components:

```typescript
// Example from /lib/schemas.ts
export const workshopSearchSchema = z.object({
  type: z.enum(["in_person", "online", "hybrid"]).optional(),
  location: z.string().optional(),
  date: z.enum(["today", "week", "month"]).optional(),
  price: z
    .string()
    .regex(/^(\d*)-?(\d*)$/)
    .optional(),
  page: z.coerce.number().int().positive().default(1),
});

export type WorkshopSearchParams = z.infer<typeof workshopSearchSchema>;
```

### 2. Server-Side Parser (`/lib/utils.ts`)

For server components, we use the `parseSearchParams` utility function:

```typescript
export function parseSearchParams<T extends z.ZodTypeAny>(
  searchParams:
    | ReadonlyURLSearchParams
    | { [key: string]: string | string[] | undefined }
    | null
    | undefined,
  schema: T,
): z.infer<T> {
  // Convert searchParams to a regular object if it's ReadonlyURLSearchParams
  const paramsObj =
    searchParams instanceof URLSearchParams
      ? Object.fromEntries(searchParams.entries())
      : searchParams || {};

  // Parse and return safe defaults if validation fails
  try {
    return schema.parse(paramsObj);
  } catch (error) {
    return schema.optional().parse(undefined);
  }
}
```

### 3. Client-Side Hook (`/hooks/use-search-params.ts`)

For client components, we use the `useZodSearchParams` hook:

```typescript
export function useZodSearchParams<T extends z.ZodTypeAny>(schema: T) {
  const searchParams = useSearchParams();
  const params = parseSearchParams(searchParams, schema);

  const createUrl = useCallback(
    (updatedParams: Partial<z.infer<T>>) => {
      const newParams = new URLSearchParams(searchParams?.toString());
      Object.entries(updatedParams).forEach(([key, value]) => {
        if (value === undefined || value === null) {
          newParams.delete(key);
        } else {
          newParams.set(key, String(value));
        }
      });
      return `?${newParams.toString()}`;
    },
    [searchParams],
  );

  return { params, createUrl };
}
```

## Usage Examples

### In Server Components

```typescript
import { parseSearchParams } from "@/lib/utils";
import { workshopSearchSchema } from "@/lib/schemas";

export default async function Page({ searchParams }) {
  // Parse with validation
  const { type, location, page } = parseSearchParams(
    searchParams,
    workshopSearchSchema,
  );

  // Now these params are properly typed and validated
  // - type is either undefined or one of: "in_person", "online", "hybrid"
  // - location is either undefined or a string
  // - page is a number (defaults to 1 if missing or invalid)
}
```

### In Client Components

```typescript
import { useZodSearchParams } from "@/hooks/use-search-params";
import { workshopSearchSchema } from "@/lib/schemas";

export function Filters() {
  // Get validated params and URL creator function
  const { params, createUrl } = useZodSearchParams(workshopSearchSchema);

  // Use validated params safely
  const currentType = params.type; // Union type: "in_person" | "online" | "hybrid" | undefined

  // Create a type-safe URL with new parameters
  const handleFilterChange = () => {
    const url = createUrl({ type: "online", page: 1 });
    router.push(`/workshops${url}`);
  };
}
```

## Benefits

1. **Type Safety**: All parameters are properly typed based on the schema
2. **Validation**: Invalid values are caught and returned as defaults
3. **Consistency**: Same validation logic on both client and server
4. **Developer Experience**: Autocomplete and type checking for URL parameters
5. **Centralization**: All schema definitions in one place

## Best Practices

1. Always define schemas in `/lib/schemas.ts`
2. Use the appropriate utility based on component type (server vs client)
3. Provide sensible defaults in your schemas
4. Keep schema definitions close to their usage in the documentation
5. Use the typed params instead of accessing searchParams directly
