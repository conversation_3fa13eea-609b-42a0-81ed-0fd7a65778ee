# Payment System Integration Guide

## Overview

This document provides a comprehensive guide for implementing a payment system in the PulseSpace application. The codebase has placeholder code and database infrastructure ready for payment provider integration.

## Payment System Architecture

The payment system follows this flow:

1. **Client accepts a quote** → Payment dialog opens
2. **Payment intent created** → Quote status updated to `payment_intent_created`
3. **Client submits payment** → Quote status updated to `payment_processing`
4. **Webhook receives confirmation** → Quote status updated to `paid`, ledger entries created
5. **Provider earnings tracked** → 80% to provider, 20% platform fee

## Implementation Checklist

### 1. Choose and Set Up Payment Provider

- Select a payment provider (Stripe, Square, PayPal, etc.)
- Create developer account and get API credentials
- Set up webhook endpoints in provider dashboard

### 2. Environment Variables

Add these environment variables for your payment provider:

```bash
# Replace with your payment provider's variable names
PAYMENT_API_KEY=your_api_key_here
PAYMENT_CLIENT_ID=your_client_id_here  # if required
PAYMENT_WEBHOOK_SECRET=your_webhook_secret_here
NEXT_PUBLIC_PAYMENT_ENV=sandbox  # or production
```

### 3. Files to Implement

#### Required Files:

1. **`lib/payment-client.ts`** - Payment provider API client
2. **`types/payment.d.ts`** - TypeScript definitions for payment provider SDK
3. **`app/api/payment/webhook/route.ts`** - Webhook handler for payment events

#### Files with Placeholders:

1. **`components/messages/payment-dialog.tsx`** - Payment UI (search for "TODO")
2. **`app/enquiries/payment-actions.ts`** - Payment intent creation (search for "TODO")

## Database Infrastructure

The database schema is already set up and payment-provider agnostic:

### Tables

- **`quotes`** - Extended with payment fields (`payment_intent_id`, `paid_at`, `provider_earnings`, `fee`)
- **`provider_ledger`** - Tracks all financial transactions
- **`withdrawal_requests`** - Manages provider withdrawal requests
- **`webhook_events`** - Prevents duplicate webhook processing

### Quote Status Flow

```
pending → payment_intent_created → payment_processing → paid
                ↓
        payment_failed (can retry)
```

## Supabase RPC Functions

Use these secure functions for payment processing:

### 1. `create_payment_intent_for_quote`

**Purpose**: Updates quote with payment intent ID and sets status to `payment_intent_created`.

**Signature**:

```sql
create_payment_intent_for_quote(
  p_quote_id UUID,
  p_payment_intent_id TEXT
) RETURNS VOID
```

**Usage**:

```typescript
const { error } = await supabase.rpc("create_payment_intent_for_quote", {
  p_quote_id: quoteId,
  p_payment_intent_id: paymentIntent.id,
});
```

**Security**: Only allows clients to update their own quotes in `pending` status.

### 2. `submit_payment_for_quote`

**Purpose**: Updates quote status from `payment_intent_created` to `payment_processing`.

**Signature**:

```sql
submit_payment_for_quote(p_quote_id UUID) RETURNS VOID
```

**Usage**:

```typescript
const { error } = await supabase.rpc("submit_payment_for_quote", {
  p_quote_id: quoteId,
});
```

**Security**: Only works if quote has a payment intent and is in correct status.

### 3. `process_webhook_event`

**Purpose**: Atomically records webhook events to prevent duplicate processing.

**Signature**:

```sql
process_webhook_event(
  p_event_id TEXT,
  p_event_type TEXT,
  p_payment_intent_id TEXT
) RETURNS BOOLEAN
```

**Usage**:

```typescript
const { data: isNewEvent } = await supabase.rpc("process_webhook_event", {
  p_event_id: eventId,
  p_event_type: event.type,
  p_payment_intent_id: paymentIntentId,
});

if (isNewEvent) {
  // Process the webhook event
} else {
  // Event already processed, skip
}
```

**Returns**: `true` if event is new, `false` if already processed.

## Implementation Steps

### Step 1: Create Payment Client

Create `lib/payment-client.ts`:

```typescript
interface PaymentClient {
  createPaymentIntent(params: {
    amount: number;
    currency: string;
    metadata: Record<string, string>;
  }): Promise<{
    id: string;
    client_secret: string;
  }>;

  // Add other methods as needed
}

export function getPaymentClient(): PaymentClient {
  // Implement your payment provider client
}
```

### Step 2: Update Payment Actions

In `app/enquiries/payment-actions.ts`, replace the TODO placeholders:

```typescript
import { getPaymentClient } from "@/lib/payment-client";

export async function createPaymentIntent(quoteId: string) {
  // ... existing validation code ...

  // Replace placeholder with actual implementation
  const paymentClient = getPaymentClient();
  const intentResponse = await paymentClient.createPaymentIntent({
    amount: quote.price,
    currency: quote.currency.toUpperCase(),
    metadata: {
      quote_id: quote.id,
      provider_id: quote.workshops?.provider_id || "",
      client_id: user.id,
    },
  });

  // Use RPC function to update quote
  await supabase.rpc("create_payment_intent_for_quote", {
    p_quote_id: quoteId,
    p_payment_intent_id: intentResponse.id,
  });

  return {
    id: intentResponse.id,
    client_secret: intentResponse.client_secret,
  };
}
```

### Step 3: Update Payment Dialog

In `components/messages/payment-dialog.tsx`, replace the TODO placeholders:

```typescript
// Replace placeholder imports
import { PaymentElement } from "your-payment-provider-sdk";

function PaymentForm({ quote, onClose }: PaymentFormProps) {
  // Replace placeholder initialization
  useEffect(() => {
    const initializePayment = async () => {
      // Initialize your payment provider SDK
      const paymentIntent = await createPaymentIntent(quote.id);
      setPaymentIntentData(paymentIntent);

      // Mount payment element
      // Implementation depends on your provider
    };

    initializePayment();
  }, [quote.id]);

  // Replace placeholder confirmation
  const triggerConfirm = async () => {
    await submitPayment(quote.id);

    // Confirm payment with your provider
    const result = await yourPaymentProvider.confirmPayment({
      // Provider-specific parameters
    });

    if (result.success) {
      toast.success("Payment submitted! Processing...");
      onClose();
    }
  };
}
```

### Step 4: Create Webhook Handler

Create `app/api/payment/webhook/route.ts`:

```typescript
import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { calculatePaymentBreakdown } from "@/lib/payment-utils";

export async function POST(request: NextRequest) {
  const body = await request.text();

  // Verify webhook signature (provider-specific)
  const signature = request.headers.get("your-signature-header");
  if (!verifySignature(body, signature)) {
    return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
  }

  const event = JSON.parse(body);
  const supabase = await createClient();

  // Check idempotency
  const { data: isNewEvent } = await supabase.rpc("process_webhook_event", {
    p_event_id: event.id,
    p_event_type: event.type,
    p_payment_intent_id: event.data.id,
  });

  if (!isNewEvent) {
    return NextResponse.json({ status: "already_processed" });
  }

  // Handle payment success
  if (event.type === "payment_succeeded") {
    await handlePaymentSuccess(event.data);
  }

  return NextResponse.json({ received: true });
}

async function handlePaymentSuccess(paymentIntent: any) {
  const supabase = await createClient();
  const quoteId = paymentIntent.metadata.quote_id;

  // Get quote in payment_processing state
  const { data: quote } = await supabase
    .from("quotes")
    .select("*")
    .eq("id", quoteId)
    .eq("status", "payment_processing")
    .single();

  if (!quote) return;

  // Calculate earnings breakdown
  const { serviceFee, providerEarnings } = calculatePaymentBreakdown(
    paymentIntent.amount,
  );

  // Update quote to paid status (using service role)
  await supabase
    .from("quotes")
    .update({
      status: "paid",
      paid_at: new Date().toISOString(),
      provider_earnings: providerEarnings,
      fee: serviceFee,
    })
    .eq("id", quoteId);

  // Create ledger entries
  await supabase.from("provider_ledger").insert([
    {
      provider_id: quote.provider_id,
      transaction_type: "payment",
      amount: providerEarnings,
      currency: quote.currency,
      quote_id: quoteId,
      description: "Payment received",
    },
    {
      provider_id: quote.provider_id,
      transaction_type: "fee",
      amount: -serviceFee,
      currency: quote.currency,
      quote_id: quoteId,
      description: "Platform fee",
    },
  ]);
}
```

## Payment Utilities

The `lib/payment-utils.ts` file contains helper functions:

```typescript
export const SERVICE_FEE_RATE = 0.2; // 20%

export function calculatePaymentBreakdown(totalAmount: number): {
  serviceFee: number;
  providerEarnings: number;
} {
  const serviceFee = parseFloat((totalAmount * SERVICE_FEE_RATE).toFixed(2));
  return {
    serviceFee,
    providerEarnings: totalAmount - serviceFee,
  };
}
```

## Security Considerations

1. **Webhook Verification**: Always verify webhook signatures in production
2. **Environment Variables**: Store API keys securely
3. **RLS Policies**: Use provided RPC functions to respect Row Level Security
4. **Idempotency**: Always use `process_webhook_event` to prevent duplicate processing
5. **Atomic Operations**: Financial operations are atomic via RPC functions

## Testing

1. **Payment Flow**: Test complete payment flow from quote acceptance to completion
2. **Webhook Processing**: Test webhook idempotency and duplicate event handling
3. **Error Handling**: Test payment failures and retry scenarios
4. **Ledger Accuracy**: Verify provider earnings and platform fees are calculated correctly
5. **Security**: Test webhook signature verification and unauthorized access prevention

## Next Steps

1. Choose your payment provider and review their documentation
2. Implement the payment client wrapper
3. Update the placeholder code in payment dialog and actions
4. Create the webhook handler
5. Test thoroughly in sandbox/development environment
6. Deploy and monitor in production

The database infrastructure and RPC functions are ready to support any payment provider integration.
