{"permissions": {"allow": ["Bash(cp:*)", "Bash(find:*)", "Bash(gh pr diff:*)", "Bash(gh pr view:*)", "Bash(git add:*)", "Bash(git checkout:*)", "Bash(git cherry-pick:*)", "Bash(git commit:*)", "Bash(grep:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(node:*)", "Bash(pnpm add:*)", "Bash(pnpm check:*)", "Bash(pnpm run check:*)", "Bash(pnpm run test:*)", "Bash(pnpm typecheck:*)", "Bash(rg:*)", "Bash(rm:*)", "<PERSON><PERSON>(sed:*)", "Bash(wc:*)", "mcp__fetch__fetch", "mcp__github__get_pull_request", "mcp__github__get_pull_request_comments", "mcp__perplexity__perplexity_search_web", "mcp__playwright__browser_click", "mcp__playwright__browser_close", "mcp__playwright__browser_navigate", "mcp__playwright__browser_resize", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_type", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_evaluate", "mcp__puppeteer__puppeteer_fill", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__supabase-dev__query", "mcp__supabase-prod__list_projects", "mcp__supabase-prod__list_tables", "Bash(rg:*)", "<PERSON><PERSON>(diff:*)", "Bash(git log:*)", "Bash(pnpm run:*)", "mcp__browser-tools__getSelectedElement", "mcp__browser-tools__runNextJSAudit", "mcp__figma__get_figma_data", "mcp__playwright__browser_press_key", "mcp__github__get_issue_comments", "mcp__github__get_pull_request_reviews", "Bash(gh api:*)", "mcp__lsp__diagnostics", "mcp__lsp__references", "mcp__ide__getDiagnostics", "mcp__github__get_pull_request_files", "WebFetch(domain:www.airwallex.com)", "Bash(pnpm lint:*)", "mcp__slack__slack_list_channels", "mcp__slack__slack_post_message", "WebFetch(domain:ui.shadcn.com)", "mcp__figma__download_figma_images"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase-dev"]}