Run `pnpm typecheck`. If there are more than 3 different files with type errors, fix them and you are done. If there are
more, read on.

For each file with type errors, create a file called "tsconfig-<UNIQUE_ID>.json" (substituting <UNIQUE_ID>) with the
following contents (substituting file name):

{
"extends": "./tsconfig.json",
"include": [ "path/to/file.ts" ]
}

Spawn an agent to focus on fixing each file individually. Give the agent the error messages for its file verbatim and
ask it to fix the errors. To check and iterate on the compilation status, the agent MUST use the command
`pnpm typecheck -p tsconfig-<UNIQUE_ID>.json`. It MUST NOT use any other command for typechecking (eg.
`npx tsc path/to/file.ts` or any `pnpm typecheck` without the -p flag). When instructing the agent about this command,
give it lots of emphasis so they get it. If the agent succeeds, in fixing all type errors, it should remove the
tsconfig-<UNIQUE_ID>.json file. If it does not succeed, that's OK; it should just report back that it failed with a
summary of what made it difficult.

When all agents have completed, compile a report of all agents' outputs.
