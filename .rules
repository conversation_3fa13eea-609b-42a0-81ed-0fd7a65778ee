# Wellness Events Marketplace - AI Assistant Guidelines

Use docs/plan.md as a todo list. When you finish a task, check it off the list. If you think of new tasks, add it to the list. Use sub-tasks as appropriate.

Before each change, look around the codebase to make sure existing conventions are followed.

After each change, run `pnpm run check` and ensure TODO.md is updated. If there is an error in the `.next` directory, try deleting the directory and trying again since it is generated.

When requested to perform a code review,

1. Run the `git diff` to see the changes.
2. If necessary, read the changed files from disk in their entirety.
3. Understand what what code was added, removed, modified and moved.
4. Flag if:
   - anything looks wrong
   - any moved code was unintionally changed
   - any code can now be simplified or removed

Use modern NextJS including server components, server actions and useOptimistic where possible.

Use pnpm for package management.

To add a shadcn component, run `pnpm dlx shadcn@latest add <name>`.

Don't run any Supabase commands without permission. But you can suggest them. Supabase commands should be preceded by `pnpm`. eg. `pnpm supabase migration up` to apply migrations.

Run `pnpm supabase migration new <name>` to create a new migration so the timestamp in the file name is correct.

## Core Operating Principles

1. **Instruction Understanding**

   - Carefully interpret user requirements for the wellness events marketplace
   - Ask specific questions when clarification is needed
   - Identify technical constraints within the Next.js, React, TypeScript, and Supabase stack

2. **Implementation Planning**

   - Create detailed implementation plans for each feature
   - Prioritize user experience for both event organizers and attendees
   - Consider scalability for future feature implementation

3. **Quality Assurance**
   - Verify implementations against accessibility standards
   - Ensure responsive design across all device types
   - Maintain consistent code quality and performance

## Technology Stack

### Frontend

- Next.js (App Router): Latest stable version
- React: Latest stable version
- TypeScript: Strict mode enabled
- Tailwind CSS: For styling
- shadcn/ui: For component library
- React Hook Form: For form handling
- Zod: For validation
- Lucide React: For icons

### Backend

- Supabase: For database, authentication, and storage
- Server Components: For data fetching
- Server Actions: For API endpoints

### Development Tools

- ESLint: For code quality
- Prettier: For code formatting
- TypeScript: For type checking
- Jest/React Testing Library: For testing

## Code Quality Standards

### 1. Naming Conventions

- Components: PascalCase (e.g., `EventCard`, `ProfileHeader`)
- Files: kebab-case (e.g., `event-card.tsx`, `profile-header.tsx`)
- Functions: camelCase (e.g., `getEvents`, `updateProfile`)
- Constants: UPPER_SNAKE_CASE (e.g., `API_URL`, `MAX_EVENTS`)
- Types/Interfaces: PascalCase (e.g., `EventType`, `UserProfile`)

### 2. Component Structure

- Use functional React components with TypeScript
- Keep files small, refactor to split them when they get too big
- Separate business logic from UI components
- Use proper error boundaries

### 3. Styling Approach

- Use Tailwind CSS for styling
- Follow shadcn/ui component patterns
- Maintain consistent spacing and sizing
- Ensure responsive design for all components

## API Implementation

- Use Server Actions for data mutations
- Server Components for data fetching
- Implement proper error handling
- Validate all inputs with Zod
- Structure API responses consistently

## Accessibility Standards

- Follow WCAG 2.1 AA guidelines
- Ensure keyboard navigation
- Implement proper ARIA attributes
- Maintain sufficient color contrast
- Support screen readers
- Manage focus for modals and dialogs

## Performance Optimization

- Use Next.js Image component
- Minimize client-side components
- Utilize server components where appropriate
