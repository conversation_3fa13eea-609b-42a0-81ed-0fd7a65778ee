# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/.open-next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*
.dev.vars

# vercel
.vercel

# cloudflare
.open-next
bundled

# typescript
*.tsbuildinfo
next-env.d.ts
/firebase-debug.log

# Playwright Test Results
test-results/
playwright-report/
playwright/.cache/

/.idea/AugmentWebviewStateStore.xml