-- Insert auth users first (no dependencies)
INSERT INTO auth.users (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at, invited_at, confirmation_token, confirmation_sent_at, recovery_token, recovery_sent_at, email_change_token_new, email_change, email_change_sent_at, last_sign_in_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, created_at, updated_at, phone, phone_confirmed_at, phone_change, phone_change_token, phone_change_sent_at, email_change_token_current, email_change_confirm_status, banned_until, reauthentication_token, reauthentication_sent_at, is_sso_user, deleted_at, is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '13ed4fe6-9795-4280-8e46-1861d96cf7d9', 'authenticated', 'authenticated', '<EMAIL>', '$2a$10$SryNcNQetuAaeyPoTDl5QONQqSP5tlEIjSnD4PixtJ/l8k6YG5XYK', '2025-06-19 11:35:41.435088 +00:00', null, '', null, '', null, '', '', null, '2025-06-19 11:35:41.436885 +00:00', '{"provider": "email", "providers": ["email"]}', '{"sub": "13ed4fe6-9795-4280-8e46-1861d96cf7d9", "email": "<EMAIL>", "full_name": "Client Kevin", "email_verified": true, "phone_verified": false}', null, '2025-06-19 11:35:41.430928 +00:00', '2025-06-19 11:35:41.438034 +00:00', null, null, '', '', null, '', 0, null, '', null, false, null, false);
INSERT INTO auth.users (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at, invited_at, confirmation_token, confirmation_sent_at, recovery_token, recovery_sent_at, email_change_token_new, email_change, email_change_sent_at, last_sign_in_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, created_at, updated_at, phone, phone_confirmed_at, phone_change, phone_change_token, phone_change_sent_at, email_change_token_current, email_change_confirm_status, banned_until, reauthentication_token, reauthentication_sent_at, is_sso_user, deleted_at, is_anonymous) VALUES ('00000000-0000-0000-0000-000000000000', '21fe12dd-8836-4058-9f6a-3d108dc9b42a', 'authenticated', 'authenticated', '<EMAIL>', '$2a$10$IFyhE5/6jR8H27uLcTdCquqVeVhVFpHLJbSiUTLJIfWqbfNwpZm8m', '2025-06-19 11:34:48.441215 +00:00', null, '', null, '', null, '', '', null, '2025-06-19 11:34:48.443909 +00:00', '{"provider": "email", "providers": ["email"]}', '{"sub": "21fe12dd-8836-4058-9f6a-3d108dc9b42a", "email": "<EMAIL>", "full_name": "Provider Kevin", "email_verified": true, "phone_verified": false}', null, '2025-06-19 11:34:48.422823 +00:00', '2025-06-19 11:34:48.445548 +00:00', null, null, '', '', null, '', 0, null, '', null, false, null, false);

-- Insert profiles (depends on auth.users)
INSERT INTO public.profiles (id, email, full_name, phone_number, user_type, created_at, updated_at) VALUES 
('21fe12dd-8836-4058-9f6a-3d108dc9b42a', '<EMAIL>', 'Kevin Provider', '+61412345678', 'provider', '2025-06-19 11:34:48.422823 +00:00', '2025-06-19 11:34:48.422823 +00:00'),
('13ed4fe6-9795-4280-8e46-1861d96cf7d9', '<EMAIL>', 'Kevin Client', '+61423456789', 'client', '2025-06-19 11:35:41.430928 +00:00', '2025-06-19 11:35:41.430928 +00:00');

-- Insert category groups (no dependencies)
INSERT INTO public.category_groups (id, name, created_at, updated_at) VALUES ('24ca7f68-938c-4b99-989a-f526ac87a4b9', 'Physical health', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00');
INSERT INTO public.category_groups (id, name, created_at, updated_at) VALUES ('6d74f6a6-f031-4f96-b3ff-a5d10a475496', 'Mental health', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00');
INSERT INTO public.category_groups (id, name, created_at, updated_at) VALUES ('bfc70525-7bbf-4a60-9a60-3f2f5ba4b33d', 'Occupational wellbeing', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00');
INSERT INTO public.category_groups (id, name, created_at, updated_at) VALUES ('3a85cf36-0d6a-44f8-b3db-4b9fb9efc555', 'Social wellbeing', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00');

-- Insert categories (depends on category_groups)
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('64f3068e-e050-4d80-9196-de9973889f55', 'Physical fitness', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', '24ca7f68-938c-4b99-989a-f526ac87a4b9');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('13876868-1023-47fe-ba4c-70997ac7955c', 'Nutrition', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', '24ca7f68-938c-4b99-989a-f526ac87a4b9');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('081ac6ae-221f-48d7-9758-961549ae3279', 'Sleep', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', '24ca7f68-938c-4b99-989a-f526ac87a4b9');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('494820cb-c38d-41fc-b163-6f2fd2cb1dbe', 'General mental health and wellbeing', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', '6d74f6a6-f031-4f96-b3ff-a5d10a475496');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('510ee6f2-5820-495a-9c00-b30d26449a2a', 'Stress management', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', '6d74f6a6-f031-4f96-b3ff-a5d10a475496');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('04690230-ffbf-4dfe-a834-db020821712c', 'Focus and productivity', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', '6d74f6a6-f031-4f96-b3ff-a5d10a475496');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('83d02651-5679-474f-bd2a-b62af3d6f9e2', 'Self reflection', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', '6d74f6a6-f031-4f96-b3ff-a5d10a475496');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('8150b511-a5bb-4117-81ce-237bf9b437b3', 'Leadership', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', 'bfc70525-7bbf-4a60-9a60-3f2f5ba4b33d');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('8a690826-b88a-46c2-ae8e-081101133df9', 'Emotional intelligence / Interpersonal skills', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', 'bfc70525-7bbf-4a60-9a60-3f2f5ba4b33d');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('51663539-4024-4c2e-bf94-f2302ab058d0', 'Financial wellbeing', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', 'bfc70525-7bbf-4a60-9a60-3f2f5ba4b33d');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('957ab534-969a-41a7-b1dd-cd006ebbd3aa', 'Work-life design', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', 'bfc70525-7bbf-4a60-9a60-3f2f5ba4b33d');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('ad6b23bc-c8c5-4648-8243-ca496acd7c24', 'Parenting', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', '3a85cf36-0d6a-44f8-b3db-4b9fb9efc555');
INSERT INTO public.categories (id, name, created_at, updated_at, category_group_id) VALUES ('9bf9a964-42a1-42ab-b0dd-7689c1e72021', 'Relationships', '2025-03-25 10:11:06.851443 +00:00', '2025-03-25 10:11:06.851443 +00:00', '3a85cf36-0d6a-44f8-b3db-4b9fb9efc555');

-- Insert client record (depends on profiles)
INSERT INTO public.clients (id, company_name, location, created_at, updated_at) VALUES 
('13ed4fe6-9795-4280-8e46-1861d96cf7d9', 'SimpleVibe Technologies', 'Sydney, NSW', '2025-06-19 11:35:41.430928 +00:00', '2025-06-19 11:35:41.430928 +00:00');

-- Insert provider organization (no dependencies)
INSERT INTO public.provider_organizations (id, name, description, profile_photo_url, city, country, location, created_at, updated_at) VALUES 
('550e8400-e29b-41d4-a716-446655440000', 'Mindful Movement Studio', 'We specialize in holistic wellness programs that combine physical fitness, mindfulness, and nutrition education to help organizations build healthier, more productive teams.', 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400', 'Singapore', 'Singapore', '123 Wellness Street, #04-15 Marina Bay', '2025-06-19 11:34:48.422823 +00:00', '2025-06-19 11:34:48.422823 +00:00');

-- Insert provider record (depends on profiles and provider_organizations)
INSERT INTO public.providers (id, organization_id, role, created_at, updated_at) VALUES 
('21fe12dd-8836-4058-9f6a-3d108dc9b42a', '550e8400-e29b-41d4-a716-446655440000', 'admin', '2025-06-19 11:34:48.422823 +00:00', '2025-06-19 11:34:48.422823 +00:00');

-- Insert workshops (depends on provider_organizations)
-- Workshop 1: In-person yoga class
INSERT INTO public.workshops (id, provider_id, name, description, image_url, duration, min_capacity, max_capacity, format, location, venue_type, prerequisites, price, currency, group_discount_available, client_site_travel_fee, lead_time, availability, published, pricing_model, created_at, updated_at) VALUES 
('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440000', 'Mindful Morning Yoga', 'Start your team''s day with an energizing yoga session designed for all fitness levels. This workshop focuses on stress reduction, flexibility, and team bonding through partner poses and breathing exercises.', 'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=800', '60 minutes', 5, 20, 'in_person', 'Singapore', 'client_location', 'None - suitable for beginners', 450, 'AUD', true, 150, '1 week notice required', null, true, 'total', '2025-06-19 12:00:00 +00:00', '2025-06-19 12:00:00 +00:00');

-- Workshop 2: Online nutrition workshop
INSERT INTO public.workshops (id, provider_id, name, description, image_url, duration, min_capacity, max_capacity, format, location, venue_type, prerequisites, price, currency, group_discount_available, client_site_travel_fee, lead_time, availability, published, pricing_model, created_at, updated_at) VALUES 
('660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440000', 'Nutrition for Peak Performance', 'Learn evidence-based nutrition strategies to boost energy, improve focus, and enhance overall wellbeing. Includes personalized meal planning templates and healthy snack ideas for the office.', 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=800', '90 minutes', 10, 50, 'online', null, 'online', 'Internet connection required', 35, 'AUD', true, 0, '3 days notice', null, true, 'per_person', '2025-06-19 12:00:00 +00:00', '2025-06-19 12:00:00 +00:00');

-- Workshop 3: Hybrid stress management program
INSERT INTO public.workshops (id, provider_id, name, description, image_url, duration, min_capacity, max_capacity, format, location, venue_type, prerequisites, price, currency, group_discount_available, client_site_travel_fee, lead_time, availability, published, pricing_model, created_at, updated_at) VALUES 
('660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440000', 'Stress Management Masterclass', 'A comprehensive program combining mindfulness techniques, time management strategies, and practical tools for managing workplace stress. Participants can join in-person or virtually.', 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?w=800', '2 hours', 8, 30, 'hybrid', 'Singapore', 'provider_location', 'None', 75, 'AUD', false, 0, '5 days notice', null, true, 'per_person', '2025-06-19 12:00:00 +00:00', '2025-06-19 12:00:00 +00:00');

-- Link workshops to categories (depends on workshops and categories)
-- Yoga workshop - Physical fitness and Stress management
INSERT INTO public.workshop_categories (workshop_id, category_id, created_at) VALUES 
('660e8400-e29b-41d4-a716-446655440001', '64f3068e-e050-4d80-9196-de9973889f55', '2025-06-19 12:00:00 +00:00'),
('660e8400-e29b-41d4-a716-446655440001', '510ee6f2-5820-495a-9c00-b30d26449a2a', '2025-06-19 12:00:00 +00:00');

-- Nutrition workshop - Nutrition and Focus/productivity
INSERT INTO public.workshop_categories (workshop_id, category_id, created_at) VALUES 
('660e8400-e29b-41d4-a716-446655440002', '13876868-1023-47fe-ba4c-70997ac7955c', '2025-06-19 12:00:00 +00:00'),
('660e8400-e29b-41d4-a716-446655440002', '04690230-ffbf-4dfe-a834-db020821712c', '2025-06-19 12:00:00 +00:00');

-- Stress management workshop - Stress management, General mental health, Work-life design
INSERT INTO public.workshop_categories (workshop_id, category_id, created_at) VALUES 
('660e8400-e29b-41d4-a716-446655440003', '510ee6f2-5820-495a-9c00-b30d26449a2a', '2025-06-19 12:00:00 +00:00'),
('660e8400-e29b-41d4-a716-446655440003', '494820cb-c38d-41fc-b163-6f2fd2cb1dbe', '2025-06-19 12:00:00 +00:00'),
('660e8400-e29b-41d4-a716-446655440003', '957ab534-969a-41a7-b1dd-cd006ebbd3aa', '2025-06-19 12:00:00 +00:00');