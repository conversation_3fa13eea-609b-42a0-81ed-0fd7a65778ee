-- Allow clients to update their own quotes (for accepting/rejecting)

-- Create a new policy that allows clients to update quotes where they are the client_id
CREATE POLICY "Clients can update their quotes"
ON public.quotes
FOR UPDATE
USING (client_id = auth.uid())
WITH CHECK (client_id = auth.uid());

-- Update the documentation comment on the existing policy to clarify
COMMENT ON POLICY "Provider organization members can manage quotes" ON public.quotes
IS 'Allows providers to perform all operations on quotes for their organization';

COMMENT ON POLICY "Clients can view their quotes" ON public.quotes
IS 'Allows clients to view quotes where they are the client_id';

COMMENT ON POLICY "Clients can update their quotes" ON public.quotes
IS 'Allows clients to update quotes where they are the client_id (for accepting/rejecting)';
