-- Create the quote_status enum
CREATE TYPE public.quote_status AS ENUM (
    'pending',
    'rejected',
    'expired',
    'paid',
    'superceded',
    'cancelled'
);

-- Create the quotes table
CREATE TABLE public.quotes (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    workshop_id uuid NOT NULL,
    chat_room_id uuid NOT NULL,
    provider_organization_id uuid NOT NULL,
    client_id uuid NOT NULL,
    proposed_datetime timestamptz NOT NULL,
    location text NOT NULL,
    price numeric(10,2) NOT NULL,
    currency text NOT NULL,
    notes text NULL,
    status public.quote_status NOT NULL DEFAULT 'pending',
    expires_at timestamptz NULL,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    CONSTRAINT quotes_pkey PRIMARY KEY (id),
    CONSTRAINT quotes_workshop_id_fkey FOREIGN KEY (workshop_id) REFERENCES public.workshops(id),
    CONSTRAINT quotes_chat_room_id_fkey FOREIGN KEY (chat_room_id) REFERENCES public.chat_rooms(id) ON DELETE CASCADE,
    CONSTRAINT quotes_provider_organization_id_fkey FOREIGN KEY (provider_organization_id) REFERENCES public.provider_organizations(id),
    CONSTRAINT quotes_client_id_fkey FOREIGN KEY (client_id) REFERENCES auth.users(id)
);

-- Add indexes to the quotes table
CREATE INDEX idx_quotes_workshop_id ON public.quotes(workshop_id);
CREATE INDEX idx_quotes_chat_room_id ON public.quotes(chat_room_id);
CREATE INDEX idx_quotes_provider_organization_id ON public.quotes(provider_organization_id);
CREATE INDEX idx_quotes_client_id ON public.quotes(client_id);
CREATE INDEX idx_quotes_status ON public.quotes(status);

-- Trigger to update 'updated_at' on quotes table
CREATE TRIGGER update_quotes_updated_at
BEFORE UPDATE ON public.quotes
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at();

-- Alter chat_messages table to add quote_id
ALTER TABLE public.chat_messages
ADD COLUMN quote_id uuid NULL,
ADD CONSTRAINT chat_messages_quote_id_fkey FOREIGN KEY (quote_id) REFERENCES public.quotes(id) ON DELETE SET NULL;

-- RLS for quotes table
ALTER TABLE public.quotes ENABLE ROW LEVEL SECURITY;

-- Provider organization members can manage quotes
CREATE POLICY "Provider organization members can manage quotes"
ON public.quotes
FOR ALL
USING (
    EXISTS (
        SELECT 1
        FROM public.providers p
        WHERE p.id = auth.uid()
        AND p.organization_id = provider_organization_id
    )
);

-- Clients can view their own quotes
CREATE POLICY "Clients can view their quotes"
ON public.quotes
FOR SELECT
USING (client_id = auth.uid());
