-- Allow NULL sender_id in chat_messages table for system messages

-- First, drop the foreign key constraint
ALTER TABLE public.chat_messages
DROP CONSTRAINT IF EXISTS chat_messages_sender_id_fkey;

-- Modify the sender_id column to allow NULL values
ALTER TABLE public.chat_messages
ALTER COLUMN sender_id DROP NOT NULL;

-- Re-add the foreign key constraint with ON DELETE CASCADE, but allow NULL values
ALTER TABLE public.chat_messages
ADD CONSTRAINT chat_messages_sender_id_fkey 
FOREIGN KEY (sender_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Update the comment to reflect the change
COMMENT ON COLUMN public.chat_messages.sender_id IS 'The user ID of the message sender. NULL for system messages.';
