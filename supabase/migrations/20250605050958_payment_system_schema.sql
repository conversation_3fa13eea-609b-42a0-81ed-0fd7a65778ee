-- Payment System Schema Migration
-- Extends quotes table and adds ledger system for Airwallex integration

-- Extend existing quotes table with payment fields
ALTER TABLE quotes ADD COLUMN payment_intent_id TEXT;
ALTER TABLE quotes ADD COLUMN paid_at TIMESTAMPTZ;
ALTER TABLE quotes ADD COLUMN provider_earnings DECIMAL(10,2);
ALTER TABLE quotes ADD COLUMN fee DECIMAL(10,2);

-- Add payment states to existing quote_status enum
ALTER TYPE quote_status ADD VALUE 'payment_processing';
ALTER TYPE quote_status ADD VALUE 'payment_failed';

-- Create enums for better type safety
CREATE TYPE transaction_type AS ENUM ('payment', 'withdrawal', 'fee', 'refund');
CREATE TYPE withdrawal_status AS ENUM ('pending', 'completed', 'rejected');

-- Withdrawal requests (create first to avoid circular reference)
CREATE TABLE withdrawal_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL,
  bank_details JSONB NOT NULL,
  status withdrawal_status NOT NULL DEFAULT 'pending',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  processed_at TIMESTAMPTZ
);

-- Simple ledger for transaction history
CREATE TABLE provider_ledger (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  transaction_type transaction_type NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL,
  quote_id UUID REFERENCES quotes(id) ON DELETE SET NULL,
  withdrawal_id UUID REFERENCES withdrawal_requests(id) ON DELETE SET NULL,
  description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT ledger_single_reference
      CHECK (
          (transaction_type IN ('payment', 'fee') AND quote_id IS NOT NULL AND withdrawal_id IS NULL)
              OR (transaction_type = 'withdrawal' AND withdrawal_id IS NOT NULL AND quote_id IS NULL)
              OR (transaction_type = 'refund' AND quote_id IS NULL AND withdrawal_id IS NULL)
          )
);

-- Add index for efficient balance calculations
CREATE INDEX idx_provider_ledger_provider_currency ON provider_ledger(provider_id, currency);
CREATE INDEX idx_provider_ledger_quote_id ON provider_ledger(quote_id);
CREATE INDEX idx_provider_ledger_withdrawal_id ON provider_ledger(withdrawal_id);

-- Add constraint to ensure positive withdrawal amounts
ALTER TABLE withdrawal_requests ADD CONSTRAINT withdrawal_amount_positive CHECK (amount > 0);

-- Add index for withdrawal status queries
CREATE INDEX idx_withdrawal_requests_status ON withdrawal_requests(status);
CREATE INDEX idx_withdrawal_requests_provider ON withdrawal_requests(provider_id);

-- View for current balances (calculated from ledger)
CREATE VIEW provider_balances AS
SELECT 
  provider_id,
  currency,
  COALESCE(SUM(amount), 0) as balance
FROM provider_ledger 
GROUP BY provider_id, currency;

-- Add row level security policies

-- Provider ledger: Users can view their own ledger entries
ALTER TABLE provider_ledger ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own ledger entries" ON provider_ledger
  FOR SELECT USING (provider_id = auth.uid());

-- Admins can view all ledger entries
CREATE POLICY "Admins can view all ledger entries" ON provider_ledger
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND user_type = 'admin'
    )
  );

-- Withdrawal requests: Users can view and create their own requests
ALTER TABLE withdrawal_requests ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own withdrawal requests" ON withdrawal_requests
  FOR SELECT USING (provider_id = auth.uid());

CREATE POLICY "Users can create own withdrawal requests" ON withdrawal_requests
  FOR INSERT WITH CHECK (provider_id = auth.uid());

-- Admins can view and update all withdrawal requests
CREATE POLICY "Admins can manage all withdrawal requests" ON withdrawal_requests
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND user_type = 'admin'
    )
  );