-- Change quotes foreign key constraint to CASCADE delete when workshop is deleted
-- This ensures quotes are automatically deleted when their associated workshop is removed

-- Drop the existing foreign key constraint
ALTER TABLE quotes
DROP CONSTRAINT quotes_workshop_id_fkey;

-- Add the constraint back with CASCADE delete
ALTER TABLE quotes
ADD CONSTRAINT quotes_workshop_id_fkey
FOREIGN KEY (workshop_id)
REFERENCES workshops(id)
ON DELETE CASCADE;