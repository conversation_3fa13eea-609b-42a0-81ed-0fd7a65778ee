-- Add 'payment_intent_created' if it doesn't exist
DO $$ 
BEGIN
  -- Add the enum value if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'payment_intent_created' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'quote_status')) THEN
    ALTER TYPE quote_status ADD VALUE 'payment_intent_created';
  END IF;
END $$;

-- Add fee column for platform fees (idempotent)
ALTER TABLE quotes ADD COLUMN IF NOT EXISTS fee DECIMAL(10,2);

-- Update RLS policies to handle new payment states (idempotent)
-- Users can view their own quotes in any payment state
DROP POLICY IF EXISTS "Users can view their own quotes" ON quotes;
CREATE POLICY "Users can view their own quotes" ON quotes
  FOR SELECT USING (
    auth.uid() = client_id OR 
    auth.uid() = (SELECT provider_id FROM workshops WHERE id = workshop_id)
  );

-- Clients can update their own quotes, but with field-level restrictions
-- Payment-related fields are protected and can only be updated by specific Server Actions
DROP POLICY IF EXISTS "Clients can update their own quotes" ON quotes;
CREATE POLICY "Clients can update their own quotes" ON quotes
  FOR UPDATE USING (
    auth.uid() = client_id AND 
    status IN ('pending', 'payment_intent_created', 'payment_processing', 'payment_failed')
  );

-- TODO: Add field-level restrictions using a security-definer function
-- to prevent clients from updating payment_intent_id, provider_earnings, fee, paid_at
-- This would require creating a custom function that validates the update columns