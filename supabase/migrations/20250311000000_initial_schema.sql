-- Note: The auth schema is automatically created by Supabase

-- <PERSON><PERSON> enum types
CREATE TYPE user_type AS ENUM ('provider', 'client');
CREATE TYPE workshop_format AS ENUM ('in_person', 'online', 'hybrid');
CREATE TYPE venue_type AS ENUM ('provider_location', 'client_location');
CREATE TYPE booking_status AS ENUM ('pending', 'confirmed', 'cancelled', 'completed');
CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'refunded', 'failed');

-- Create categories table
CREATE TABLE categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create profiles table that extends auth.users
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  full_name TEXT,
  phone_number TEXT,
  profile_photo_url TEXT,
  user_type user_type NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create providers table
CREATE TABLE providers (
  id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
  org_name TEXT NOT NULL,
  org_description TEXT,
  areas_of_expertise TEXT,
  city TEXT,
  country TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create clients table
CREATE TABLE clients (
  id UUID PRIMARY KEY REFERENCES profiles(id) ON DELETE CASCADE,
  company_name TEXT NOT NULL,
  location TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create workshops table
CREATE TABLE workshops (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id UUID NOT NULL REFERENCES providers(id) ON DELETE CASCADE,
  category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
  name TEXT NOT NULL,
  description TEXT,
  image_url TEXT,
  duration TEXT NOT NULL,
  min_capacity INTEGER,
  max_capacity INTEGER,
  format workshop_format NOT NULL DEFAULT 'in_person',
  location TEXT,
  venue_type venue_type NOT NULL DEFAULT 'provider_location',
  prerequisites TEXT,
  price_per_person DECIMAL(10, 2) NOT NULL,
  group_discount_available BOOLEAN NOT NULL DEFAULT FALSE,
  availability TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create bookings table
CREATE TABLE bookings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workshop_id UUID NOT NULL REFERENCES workshops(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  status booking_status NOT NULL DEFAULT 'pending',
  participant_count INTEGER NOT NULL,
  booking_datetime TIMESTAMPTZ NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL,
  special_requirements TEXT,
  terms_accepted BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create payments table
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  booking_id UUID NOT NULL REFERENCES bookings(id) ON DELETE CASCADE,
  amount DECIMAL(10, 2) NOT NULL,
  status payment_status NOT NULL DEFAULT 'pending',
  payment_method TEXT,
  transaction_id TEXT,
  payment_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX idx_workshops_provider_id ON workshops(provider_id);
CREATE INDEX idx_workshops_category_id ON workshops(category_id);
CREATE INDEX idx_bookings_workshop_id ON bookings(workshop_id);
CREATE INDEX idx_bookings_client_id ON bookings(client_id);
CREATE INDEX idx_payments_booking_id ON payments(booking_id);

-- Create RLS policies
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE workshops ENABLE ROW LEVEL SECURITY;
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Create a function to check if a user is authenticated
CREATE OR REPLACE FUNCTION auth.is_authenticated() RETURNS BOOLEAN AS $$
BEGIN
  RETURN (auth.uid() IS NOT NULL);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is a provider
CREATE OR REPLACE FUNCTION auth.is_provider() RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles WHERE id = auth.uid() AND user_type = 'provider'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is a client
CREATE OR REPLACE FUNCTION auth.is_client() RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles WHERE id = auth.uid() AND user_type = 'client'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create policies for profiles table
CREATE POLICY "Users can view their own profile" ON profiles
  FOR SELECT USING (id = auth.uid());

CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Profiles are viewable by authenticated users" ON profiles
  FOR SELECT USING (auth.is_authenticated());

-- Create policies for categories table
CREATE POLICY "Categories are viewable by everyone" ON categories
  FOR SELECT USING (true);

-- Create policies for providers table
CREATE POLICY "Providers can view their own data" ON providers
  FOR SELECT USING (id = auth.uid());

CREATE POLICY "Providers can update their own data" ON providers
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Provider profiles are viewable by authenticated users" ON providers
  FOR SELECT USING (auth.is_authenticated());

-- Create policies for clients table
CREATE POLICY "Clients can view their own data" ON clients
  FOR SELECT USING (id = auth.uid());

CREATE POLICY "Clients can update their own data" ON clients
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Client profiles are viewable by authenticated users" ON clients
  FOR SELECT USING (auth.is_authenticated());

-- Create policies for workshops table
CREATE POLICY "Workshops are viewable by everyone" ON workshops
  FOR SELECT USING (true);

CREATE POLICY "Providers can insert their own workshops" ON workshops
  FOR INSERT WITH CHECK (provider_id = auth.uid() AND auth.is_provider());

CREATE POLICY "Providers can update their own workshops" ON workshops
  FOR UPDATE USING (provider_id = auth.uid() AND auth.is_provider());

CREATE POLICY "Providers can delete their own workshops" ON workshops
  FOR DELETE USING (provider_id = auth.uid() AND auth.is_provider());

-- Create policies for bookings table
CREATE POLICY "Clients can view their own bookings" ON bookings
  FOR SELECT USING (client_id = auth.uid() AND auth.is_client());

CREATE POLICY "Providers can view bookings for their workshops" ON bookings
  FOR SELECT USING (workshop_id IN (SELECT id FROM workshops WHERE provider_id = auth.uid()));

CREATE POLICY "Clients can insert their own bookings" ON bookings
  FOR INSERT WITH CHECK (client_id = auth.uid() AND auth.is_client());

CREATE POLICY "Clients can update their own bookings" ON bookings
  FOR UPDATE USING (client_id = auth.uid() AND auth.is_client());

-- Create policies for payments table
CREATE POLICY "Clients can view their own payments" ON payments
  FOR SELECT USING (booking_id IN (SELECT id FROM bookings WHERE client_id = auth.uid()));

CREATE POLICY "Providers can view payments for their workshops" ON payments
  FOR SELECT USING (booking_id IN (SELECT id FROM bookings WHERE workshop_id IN (SELECT id FROM workshops WHERE provider_id = auth.uid())));

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_profiles_updated_at
BEFORE UPDATE ON profiles
FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_providers_updated_at
BEFORE UPDATE ON providers
FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_clients_updated_at
BEFORE UPDATE ON clients
FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_workshops_updated_at
BEFORE UPDATE ON workshops
FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_bookings_updated_at
BEFORE UPDATE ON bookings
FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_payments_updated_at
BEFORE UPDATE ON payments
FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_categories_updated_at
BEFORE UPDATE ON categories
FOR EACH ROW EXECUTE FUNCTION update_updated_at();
