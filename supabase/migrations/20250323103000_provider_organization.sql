-- Migration to separate provider users and provider organizations
-- Create provider_organizations table
CREATE TABLE provider_organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  profile_photo_url TEXT,
  city TEXT,
  country TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add organization_id to the providers table (nullable at first)
ALTER TABLE providers ADD COLUMN organization_id UUID REFERENCES provider_organizations(id) ON DELETE CASCADE;

-- Update is_provider function to check both profile type and provider table
CREATE OR REPLACE FUNCTION auth.is_provider() R<PERSON>URNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() AND user_type = 'provider' 
    AND EXISTS (SELECT 1 FROM providers WHERE id = auth.uid())
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is an organization admin
CREATE OR REPLACE FUNCTION auth.is_organization_admin(org_id UUID) RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM providers 
    WHERE id = auth.uid() AND organization_id = org_id
    -- In MVP, all provider users are admins of their organization
    -- Future implementation would check admin role
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get a user's organization ID
CREATE OR REPLACE FUNCTION auth.get_user_organization_id() RETURNS UUID AS $$
DECLARE
  org_id UUID;
BEGIN
  SELECT organization_id INTO org_id FROM providers WHERE id = auth.uid();
  RETURN org_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable RLS on the new table
ALTER TABLE provider_organizations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for provider_organizations
CREATE POLICY "Provider organizations are viewable by everyone" ON provider_organizations
  FOR SELECT USING (true);

CREATE POLICY "Provider users can update their organization" ON provider_organizations
  FOR UPDATE USING (id IN (SELECT organization_id FROM providers WHERE id = auth.uid()));

-- Remove the existing foreign key constraint on workshops
ALTER TABLE workshops DROP CONSTRAINT IF EXISTS workshops_provider_id_fkey;

-- Drop existing workshop policies related to provider actions
DROP POLICY IF EXISTS "Providers can insert their own workshops" ON workshops;
DROP POLICY IF EXISTS "Providers can update their own workshops" ON workshops;
DROP POLICY IF EXISTS "Providers can delete their own workshops" ON workshops;

-- Create new workshop policies to check organization membership
CREATE POLICY "Providers can insert workshops for their organization" ON workshops
  FOR INSERT WITH CHECK (
    provider_id = (SELECT organization_id FROM providers WHERE id = auth.uid()) 
    AND auth.is_provider()
  );

CREATE POLICY "Providers can update workshops for their organization" ON workshops
  FOR UPDATE USING (
    provider_id = (SELECT organization_id FROM providers WHERE id = auth.uid())
    AND auth.is_provider()
  );

CREATE POLICY "Providers can delete workshops for their organization" ON workshops
  FOR DELETE USING (
    provider_id = (SELECT organization_id FROM providers WHERE id = auth.uid())
    AND auth.is_provider()
  );

-- Create trigger for provider_organizations updated_at
CREATE TRIGGER update_provider_organizations_updated_at
BEFORE UPDATE ON provider_organizations
FOR EACH ROW EXECUTE FUNCTION update_updated_at();

-- Note: profile_photo_url is already added to provider_organizations in the table creation

-- Migration of existing data
-- This is the migration logic to move from old schema to new schema
DO $$
DECLARE
  provider_row RECORD;
  profile_row RECORD;
  new_org_id UUID;
BEGIN
  -- For each existing provider
  FOR provider_row IN SELECT p.*, pr.profile_photo_url 
                     FROM providers p 
                     JOIN profiles pr ON p.id = pr.id LOOP
    -- Create a new organization based on the provider data
    INSERT INTO provider_organizations (
      name, 
      description, 
      profile_photo_url,
      city,
      country
    ) VALUES (
      provider_row.org_name,
      provider_row.org_description,
      provider_row.profile_photo_url,
      provider_row.city,
      provider_row.country
    ) RETURNING id INTO new_org_id;
    
    -- Update the provider record to link to the new organization
    UPDATE providers 
    SET organization_id = new_org_id
    WHERE id = provider_row.id;
    
    -- Update the workshops to point to the organization instead of the provider user
    UPDATE workshops 
    SET provider_id = new_org_id
    WHERE provider_id = provider_row.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Now that data is migrated, add NOT NULL constraint to organization_id
ALTER TABLE providers ALTER COLUMN organization_id SET NOT NULL;

-- Modify providers table to remove columns now in the organization table
ALTER TABLE providers DROP COLUMN org_name;
ALTER TABLE providers DROP COLUMN org_description;
ALTER TABLE providers DROP COLUMN areas_of_expertise;
ALTER TABLE providers DROP COLUMN city;
ALTER TABLE providers DROP COLUMN country;

-- Remove profile_photo_url from profiles
ALTER TABLE profiles DROP COLUMN profile_photo_url;

-- Add role field to providers for future use
ALTER TABLE providers ADD COLUMN role TEXT DEFAULT 'admin';

-- Now that all data is migrated, re-add the foreign key constraint on workshops
ALTER TABLE workshops ADD CONSTRAINT workshops_provider_id_fkey 
  FOREIGN KEY (provider_id) REFERENCES provider_organizations(id) ON DELETE CASCADE;
