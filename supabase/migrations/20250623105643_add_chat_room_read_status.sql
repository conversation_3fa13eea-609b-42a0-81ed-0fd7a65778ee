-- Create table to track when users last read each chat room
CREATE TABLE chat_room_read_status (
  room_id UUID NOT NULL REFERENCES chat_rooms(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  last_read_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  PRIMARY KEY (room_id, user_id)
);

-- Add RLS policies
ALTER TABLE chat_room_read_status ENABLE ROW LEVEL SECURITY;

-- Users can only see their own read status
CREATE POLICY "Users can view their own read status" ON chat_room_read_status
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can only update their own read status
CREATE POLICY "Users can update their own read status" ON chat_room_read_status
  FOR ALL
  USING (auth.uid() = user_id);

-- Add index for performance
CREATE INDEX idx_chat_room_read_status_room_user ON chat_room_read_status(room_id, user_id);
CREATE INDEX idx_chat_messages_room_created ON chat_messages(room_id, created_at);