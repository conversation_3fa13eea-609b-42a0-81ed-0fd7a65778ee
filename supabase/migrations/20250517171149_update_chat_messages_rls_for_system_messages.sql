-- Update RLS policy for chat_messages to allow system messages with NULL sender_id

-- First, drop the existing insert policy
DROP POLICY IF EXISTS "Allow users to insert messages into rooms they are part of" ON public.chat_messages;

-- Create a new policy that allows both regular user messages and system messages
CREATE POLICY "Allow users to insert messages into rooms they are part of" ON public.chat_messages
FOR INSERT WITH CHECK (
    -- For authenticated users, either:
    auth.role() = 'authenticated' AND (
        -- 1. Regular user messages: sender_id must match the authenticated user
        (sender_id IS NOT NULL AND auth.uid() = sender_id) OR
        -- 2. System messages: sender_id is NULL for system-generated messages
        (sender_id IS NULL)
    ) AND
    -- The user must have access to the chat room (either as client or provider)
    EXISTS (
        SELECT 1 FROM public.chat_rooms r
        WHERE r.id = room_id AND
        (r.client_id = auth.uid() OR
         EXISTS (
             SELECT 1
             FROM public.providers p
             WHERE p.id = auth.uid()
             AND p.organization_id = r.provider_organization_id
         )
        )
    )
);
