-- Drop the existing foreign key constraint on quotes.client_id
ALTER TABLE public.quotes
DROP CONSTRAINT quotes_client_id_fkey;

-- Re-add the foreign key constraint with ON DELETE CASCADE
ALTER TABLE public.quotes
ADD CONSTRAINT quotes_client_id_fkey
FOREIGN KEY (client_id)
REFERENCES auth.users(id)
ON DELETE CASCADE;

-- Also fix provider_organization_id to cascade delete quotes when provider organization is deleted
ALTER TABLE public.quotes
DROP CONSTRAINT quotes_provider_organization_id_fkey;

ALTER TABLE public.quotes
ADD CONSTRAINT quotes_provider_organization_id_fkey
FOREIGN KEY (provider_organization_id)
REFERENCES public.provider_organizations(id)
ON DELETE CASCADE;