-- supabase/migrations/20250423073517_create_chat_tables.sql

-- Enable pg_cron extension if not already enabled (needed for potential future background tasks)
-- create extension if not exists pg_cron;

-- Create chat_rooms table
create table public.chat_rooms (
    id uuid primary key default gen_random_uuid(),
    workshop_id uuid not null references public.workshops(id) on delete cascade,
    client_id uuid not null references auth.users(id) on delete cascade,
    provider_organization_id uuid not null references public.provider_organizations(id) on delete cascade,
    created_at timestamptz not null default now(),
    updated_at timestamptz not null default now(),
    constraint unique_chat_room unique (workshop_id, client_id) -- Prevent duplicate rooms for the same client/workshop
);

-- Add comments for columns
comment on table public.chat_rooms is 'Stores chat rooms between clients and providers for specific workshops.';
comment on column public.chat_rooms.workshop_id is 'The workshop this chat room is associated with.';
comment on column public.chat_rooms.client_id is 'The ID of the client (user) who initiated the chat.';
comment on column public.chat_rooms.provider_organization_id is 'The ID of the provider organization responsible for the workshop.';
comment on column public.chat_rooms.created_at is 'Timestamp when the chat room was created.';
comment on column public.chat_rooms.updated_at is 'Timestamp when the chat room was last updated (e.g., new message).';

-- Create chat_messages table
create table public.chat_messages (
    id uuid primary key default gen_random_uuid(),
    room_id uuid not null references public.chat_rooms(id) on delete cascade,
    sender_id uuid not null references auth.users(id) on delete cascade,
    content text not null check (length(content) > 0),
    created_at timestamptz not null default now()
);

-- Add comments for columns
comment on table public.chat_messages is 'Stores individual messages within a chat room.';
comment on column public.chat_messages.room_id is 'The chat room this message belongs to.';
comment on column public.chat_messages.sender_id is 'The user ID of the message sender.';
comment on column public.chat_messages.content is 'The text content of the message.';

-- Indexes for performance
create index idx_chat_rooms_client_id on public.chat_rooms(client_id);
create index idx_chat_rooms_provider_organization_id on public.chat_rooms(provider_organization_id);
create index idx_chat_messages_room_id_created_at on public.chat_messages(room_id, created_at desc);
create index idx_chat_messages_sender_id on public.chat_messages(sender_id);

-- Secure tables with Row Level Security (RLS)
alter table public.chat_rooms enable row level security;
alter table public.chat_messages enable row level security;

-- RLS Policy: Allow users to access chat rooms they are part of
create policy "Allow users to access chat rooms they are part of" on public.chat_rooms
for select using (
    auth.uid() = client_id or
    exists (
        select 1
        from public.providers p
        where p.id = auth.uid()
        and p.organization_id = provider_organization_id -- Check if user is part of the provider org
    )
);

-- RLS Policy: Allow authenticated users to insert their own chat rooms
create policy "Allow authenticated users to insert their own chat rooms" on public.chat_rooms
for insert with check (
    auth.role() = 'authenticated' and
    auth.uid() = client_id -- User can only insert if they are the client
    -- Provider organization check happens implicitly via workshop lookup in action
);

-- No update policy needed currently, updated_at is handled by trigger
-- No delete policy needed currently, handled by cascade or potentially admin action later

-- RLS Policy: Allow users to select messages from rooms they are part of
create policy "Allow users to select messages from rooms they are part of" on public.chat_messages
for select using (
    exists (
        select 1 from public.chat_rooms r
        where r.id = room_id and
        (r.client_id = auth.uid() or
         exists (
             select 1
             from public.providers p
             where p.id = auth.uid()
             and p.organization_id = r.provider_organization_id -- Check org membership
         )
        )
    )
);

-- RLS Policy: Allow users to insert messages into rooms they are part of
create policy "Allow users to insert messages into rooms they are part of" on public.chat_messages
for insert with check (
    auth.role() = 'authenticated' and
    auth.uid() = sender_id and -- User must be the sender
    exists (
        select 1 from public.chat_rooms r
        where r.id = room_id and
        (r.client_id = auth.uid() or
         exists (
             select 1
             from public.providers p
             where p.id = auth.uid()
             and p.organization_id = r.provider_organization_id -- Check org membership
         )
        )
    )
);

-- No update/delete policies for messages needed currently

-- Function to automatically update 'updated_at' timestamp on chat_rooms
create or replace function public.handle_updated_at()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql security definer;

-- Trigger to update 'updated_at' when a chat_room row is updated
create trigger on_chat_rooms_updated
before update on public.chat_rooms
for each row
execute procedure public.handle_updated_at();

-- Trigger to update chat_room 'updated_at' when a new message is inserted
create or replace function public.update_room_on_new_message()
returns trigger as $$
begin
  update public.chat_rooms
  set updated_at = now()
  where id = new.room_id;
  return new; -- Return value is ignored for AFTER triggers
end;
$$ language plpgsql security definer;

-- Trigger to update the room's updated_at timestamp after a new message
create trigger on_new_chat_message_update_room
after insert on public.chat_messages
for each row
execute procedure public.update_room_on_new_message();