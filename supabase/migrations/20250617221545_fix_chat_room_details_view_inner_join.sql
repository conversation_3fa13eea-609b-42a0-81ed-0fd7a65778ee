-- Fix chat_room_details_view to use INNER JOIN for workshops to ensure currency is non-null

-- Drop existing view
DROP VIEW IF EXISTS public.chat_room_details_view;

-- Recreate view with INNER JOIN for workshops to make currency non-nullable
CREATE VIEW public.chat_room_details_view AS
SELECT
    cr.id as room_id,
    cr.created_at as room_created_at,
    cr.updated_at as room_updated_at,
    cr.workshop_id,
    w.name as workshop_name,
    w.currency as workshop_currency,  -- Now guaranteed non-null due to INNER JOIN
    cr.client_id,
    client_profile.full_name as client_full_name,
    cr.provider_organization_id,
    po.name as provider_organization_name,
    po.profile_photo_url as provider_organization_profile_photo_url
FROM
    public.chat_rooms cr
        INNER JOIN
    public.workshops w ON cr.workshop_id = w.id
        LEFT JOIN
    public.profiles client_profile ON cr.client_id = client_profile.id
        LEFT JOIN
    public.provider_organizations po ON cr.provider_organization_id = po.id;

-- Grant appropriate permissions
GRANT SELECT ON public.chat_room_details_view TO authenticated;