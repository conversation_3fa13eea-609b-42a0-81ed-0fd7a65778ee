-- Modify workshops table to update pricing model
ALTER TABLE workshops 
  -- Rename price_per_person to price
  RENAME COLUMN price_per_person TO price;

-- Add new columns for pricing model, travel fee, and currency
ALTER TABLE workshops
  ADD COLUMN pricing_model TEXT NOT NULL DEFAULT 'per_person' CHECK (pricing_model IN ('per_person', 'total')),
  ADD COLUMN client_site_travel_fee DECIMAL(10, 2) DEFAULT 0,
  ADD COLUMN currency TEXT NOT NULL DEFAULT 'AUD' CHECK (currency IN ('AUD', 'IDR', 'SGD', 'MYR'));

-- Add comment to explain the pricing model
COMMENT ON COLUMN workshops.pricing_model IS 'Pricing model: per_person (charge per attendee) or total (flat fee)';
COMMENT ON COLUMN workshops.client_site_travel_fee IS 'Additional fee charged when workshop is held at client location';
COMMENT ON COLUMN workshops.currency IS 'Currency for workshop pricing (AUD, IDR, SGD, MYR)';