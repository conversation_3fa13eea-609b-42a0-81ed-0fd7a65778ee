-- Create day of week enum type
CREATE TYPE day_of_week AS ENUM ('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday');

-- Alter the availability column to use the new type
-- No data migration needed as per user request
ALTER TABLE workshops 
  ALTER COLUMN availability TYPE day_of_week[] 
  USING NULL;

-- Add a comment to the column for documentation
COMMENT ON COLUMN workshops.availability IS 'Array of days when the workshop is available (monday, tuesday, etc.)';
