-- Migration to create views for chat functionality

-- View 1: chat_messages_with_sender
-- Joins chat_messages with profiles (via auth.users) to include sender's full_name
CREATE VIEW public.chat_messages_with_sender AS
SELECT
    cm.id,
    cm.room_id,
    cm.sender_id,
    cm.content,
    cm.created_at,
    -- cm.updated_at, -- Removed as it does not exist in chat_messages
    p.full_name AS sender_full_name
    -- You can add other columns from profiles (aliased as p) if needed for the sender
    -- e.g., p.user_type AS sender_user_type
FROM
    public.chat_messages cm
JOIN
    auth.users u ON cm.sender_id = u.id
JOIN
    public.profiles p ON u.id = p.id;

-- View 2: chat_room_details_view
-- Joins chat_rooms with workshops, profiles (for client), and provider_organizations
CREATE VIEW public.chat_room_details_view AS
SELECT
    cr.id as room_id,
    cr.created_at as room_created_at,
    cr.updated_at as room_updated_at,
    cr.workshop_id,
    w.name as workshop_name,
    -- Add other workshop columns as needed, e.g., w.image_url
    cr.client_id,
    client_profile.full_name as client_full_name,
    -- Add other client profile columns as needed, e.g., client_profile.user_type
    cr.provider_organization_id,
    po.name as provider_organization_name,
    po.profile_photo_url as provider_organization_profile_photo_url
    FROM
    public.chat_rooms cr
LEFT JOIN
    public.workshops w ON cr.workshop_id = w.id
LEFT JOIN
    auth.users client_user ON cr.client_id = client_user.id
LEFT JOIN
    public.profiles client_profile ON client_user.id = client_profile.id
LEFT JOIN
    public.provider_organizations po ON cr.provider_organization_id = po.id;

-- It's good practice to also define how to drop these views if the migration is rolled back.
-- However, Supabase CLI `migration new` doesn't auto-generate a down migration.
-- For now, if you need to revert, you'd manually run:
-- DROP VIEW public.chat_room_details_view;
-- DROP VIEW public.chat_messages_with_sender;