-- Create a function that returns the same structure as chat_room_details_view but with unread counts
-- This provides an efficient way to get room details with unread counts in a single query
CREATE OR REPLACE FUNCTION chat_room_details_with_unread(p_user_id UUID)
RETURNS TABLE (
    room_id UUID,
    room_created_at TIMESTAMP WITH TIME ZONE,
    room_updated_at TIMESTAMP WITH TIME ZONE,
    workshop_id UUID,
    workshop_name TEXT,
    workshop_currency TEXT,
    client_id UUID,
    client_full_name TEXT,
    provider_organization_id UUID,
    provider_organization_name TEXT,
    provider_organization_profile_photo_url TEXT,
    has_unread BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cr.id as room_id,
        cr.created_at as room_created_at,
        cr.updated_at as room_updated_at,
        cr.workshop_id,
        w.name as workshop_name,
        w.currency as workshop_currency,
        cr.client_id,
        cp.full_name as client_full_name,
        cr.provider_organization_id,
        po.name as provider_organization_name,
        po.profile_photo_url as provider_organization_profile_photo_url,
        CASE 
            WHEN crs.last_read_at IS NULL THEN 
                EXISTS(SELECT 1 FROM chat_messages cm WHERE cm.room_id = cr.id)
            ELSE 
                EXISTS(SELECT 1 FROM chat_messages cm WHERE cm.room_id = cr.id AND cm.created_at > crs.last_read_at)
        END as has_unread
    FROM chat_rooms cr
    LEFT JOIN workshops w ON cr.workshop_id = w.id
    LEFT JOIN profiles cp ON cr.client_id = cp.id
    LEFT JOIN provider_organizations po ON cr.provider_organization_id = po.id
    LEFT JOIN chat_room_read_status crs ON cr.id = crs.room_id AND crs.user_id = p_user_id
    WHERE (
        -- User is the client
        cr.client_id = p_user_id
        OR 
        -- User is a provider (via their organization)
        cr.provider_organization_id IN (
            SELECT organization_id 
            FROM providers 
            WHERE id = p_user_id
        )
    )
    ORDER BY cr.updated_at DESC;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION chat_room_details_with_unread(UUID) TO authenticated;