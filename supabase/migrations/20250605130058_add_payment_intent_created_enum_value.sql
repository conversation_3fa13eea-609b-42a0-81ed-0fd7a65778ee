-- Add 'payment_intent_created' enum value
-- This must be in a separate migration from its usage due to PostgreSQL enum constraints

DO $$ 
BEGIN
  -- Add the enum value if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'payment_intent_created' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'quote_status')) THEN
    ALTER TYPE quote_status ADD VALUE 'payment_intent_created';
  END IF;
END $$;