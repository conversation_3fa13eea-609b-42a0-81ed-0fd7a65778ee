-- Add admin to user_type enum
ALTER TYPE user_type ADD VALUE IF NOT EXISTS 'admin';

-- <PERSON>reate function to check if user is admin
CREATE OR REPLACE FUNCTION auth.is_admin() RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles WHERE id = auth.uid() AND user_type = 'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;


-- Create admin policies for existing tables to allow admin access
-- Profiles - admins can view all profiles
CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (auth.is_admin());

-- Provider organizations - admins can view all
CREATE POLICY "Admins can view all provider organizations" ON provider_organizations
  FOR SELECT USING (auth.is_admin());

-- Providers - admins can view all
CREATE POLICY "Admins can view all providers" ON providers
  FOR SELECT USING (auth.is_admin());

-- Clients - admins can view all
CREATE POLICY "Admins can view all clients" ON clients
  FOR SELECT USING (auth.is_admin());

-- Workshops - admins can view all
CREATE POLICY "Ad<PERSON> can view all workshops" ON workshops
  FOR SELECT USING (auth.is_admin());

-- Bookings - admins can view all
CREATE POLICY "Admins can view all bookings" ON bookings
  FOR SELECT USING (auth.is_admin());

-- Payments - admins can view all
CREATE POLICY "Admins can view all payments" ON payments
  FOR SELECT USING (auth.is_admin());

-- Quotes - admins can view all
CREATE POLICY "Admins can view all quotes" ON quotes
  FOR SELECT USING (auth.is_admin());

-- Chat rooms - admins can view all
CREATE POLICY "Admins can view all chat rooms" ON chat_rooms
  FOR SELECT USING (auth.is_admin());

-- Chat messages - admins can view all
CREATE POLICY "Admins can view all chat messages" ON chat_messages
  FOR SELECT USING (auth.is_admin());

-- Categories - admins can view all (already public)
-- Category groups - admins can view all (already public)

-- Add admin write policies for key operations
-- Admins can insert/update/delete profiles
CREATE POLICY "Admins can manage all profiles" ON profiles
  FOR ALL USING (auth.is_admin());

-- Admins can insert/update/delete providers
CREATE POLICY "Admins can manage all providers" ON providers
  FOR ALL USING (auth.is_admin());

-- Admins can insert/update/delete clients
CREATE POLICY "Admins can manage all clients" ON clients
  FOR ALL USING (auth.is_admin());

-- Admins can insert/update/delete workshops
CREATE POLICY "Admins can manage all workshops" ON workshops
  FOR ALL USING (auth.is_admin());

-- Admins can insert/update/delete quotes
CREATE POLICY "Admins can manage all quotes" ON quotes
  FOR ALL USING (auth.is_admin());