-- Delete existing data from categories table
DELETE FROM categories;

-- Create category_groups table
CREATE TABLE category_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add category_group_id to categories table
ALTER TABLE categories ADD COLUMN category_group_id UUID REFERENCES category_groups(id) ON DELETE SET NULL;
CREATE INDEX idx_categories_category_group_id ON categories(category_group_id);

-- Remove description from categories table
ALTER TABLE categories DROP COLUMN description;

-- Create workshop_categories junction table for many-to-many relationship
CREATE TABLE workshop_categories (
  workshop_id UUID NOT NULL REFERENCES workshops(id) ON DELETE CASCADE,
  category_id UUID NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
  PRIMARY KEY (workshop_id, category_id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create index for workshop_categories
CREATE INDEX idx_workshop_categories_workshop_id ON workshop_categories(workshop_id);
CREATE INDEX idx_workshop_categories_category_id ON workshop_categories(category_id);

-- No need to migrate existing category data

-- Remove category_id from workshops table
ALTER TABLE workshops DROP COLUMN category_id;

-- Enable RLS on new tables
ALTER TABLE category_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE workshop_categories ENABLE ROW LEVEL SECURITY;

-- Create policies for category_groups table
CREATE POLICY "Category groups are viewable by everyone" ON category_groups
  FOR SELECT USING (true);

-- Create policies for workshop_categories table
CREATE POLICY "Workshop categories are viewable by everyone" ON workshop_categories
  FOR SELECT USING (true);

CREATE POLICY "Providers can insert workshop categories for their workshops" ON workshop_categories
  FOR INSERT WITH CHECK (
    workshop_id IN (SELECT id FROM workshops WHERE provider_id = auth.uid() AND auth.is_provider())
  );

CREATE POLICY "Providers can delete workshop categories for their workshops" ON workshop_categories
  FOR DELETE USING (
    workshop_id IN (SELECT id FROM workshops WHERE provider_id = auth.uid() AND auth.is_provider())
  );