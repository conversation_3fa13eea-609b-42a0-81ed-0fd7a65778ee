-- Part 1: Quotes table security
-- Remove the existing policy that allows clients to update quotes
DROP POLICY IF EXISTS "Clients can update their own quotes" ON quotes;

-- No UPDATE policy needed - only service role can update quotes directly
-- Service role bypasses RLS, so no explicit policy is required

-- Create secure functions for specific user actions
-- Function to update quote status to payment_intent_created
CREATE OR REPLACE FUNCTION create_payment_intent_for_quote(
  p_quote_id UUID,
  p_payment_intent_id TEXT
)
RETURNS VOID AS $$
BEGIN
  -- Only allow client to update their own quotes from specific states
  UPDATE quotes
  SET 
    status = 'payment_intent_created',
    payment_intent_id = p_payment_intent_id,
    updated_at = NOW()
  WHERE id = p_quote_id
    AND client_id = auth.uid()
    AND status IN ('pending', 'payment_intent_created', 'payment_failed');
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Quote not found or not eligible for payment intent creation';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update quote status to payment_processing
CREATE OR REPLACE FUNCTION submit_payment_for_quote(p_quote_id UUID)
RETURNS VOID AS $$
BEGIN
  -- Only allow client to update their own quotes from payment_intent_created state
  UPDATE quotes
  SET 
    status = 'payment_processing',
    updated_at = NOW()
  WHERE id = p_quote_id
    AND client_id = auth.uid()
    AND status = 'payment_intent_created'
    AND payment_intent_id IS NOT NULL;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Quote not found or not ready for payment submission';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION create_payment_intent_for_quote TO authenticated;
GRANT EXECUTE ON FUNCTION submit_payment_for_quote TO authenticated;

-- Note: payment_intent_id should NOT be unique in quotes table
-- A payment intent can be reused for retries when payments fail

-- Keep the existing SELECT policy for users to view their quotes
-- (This should already exist from previous migrations)

-- Part 2: Webhook idempotency
-- Create webhook events table for idempotency
CREATE TABLE IF NOT EXISTS webhook_events (
  event_id TEXT PRIMARY KEY,
  event_type TEXT NOT NULL,
  payment_intent_id TEXT,
  processed_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for faster lookups by payment_intent_id
CREATE INDEX IF NOT EXISTS idx_webhook_events_payment_intent_id ON webhook_events(payment_intent_id);

-- RLS policies
ALTER TABLE webhook_events ENABLE ROW LEVEL SECURITY;

-- No policies needed - only service role will access this table
-- Service role bypasses RLS, and regular users should never access webhook_events

-- Function to check and record webhook event atomically
CREATE OR REPLACE FUNCTION process_webhook_event(
  p_event_id TEXT,
  p_event_type TEXT,
  p_payment_intent_id TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
  v_already_processed BOOLEAN;
BEGIN
  -- Try to insert the event record
  -- Conflict detection uses the UNIQUE constraint on event_id (line 66)
  -- If event_id already exists, INSERT does nothing due to ON CONFLICT
  INSERT INTO webhook_events (event_id, event_type, payment_intent_id)
  VALUES (p_event_id, p_event_type, p_payment_intent_id)
  ON CONFLICT (event_id) DO NOTHING;
  
  -- Check if we actually inserted a row (FOUND = true if INSERT succeeded)
  v_already_processed := NOT FOUND;
  
  RETURN NOT v_already_processed; -- Return true if event is new (was inserted)
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION process_webhook_event TO authenticated;