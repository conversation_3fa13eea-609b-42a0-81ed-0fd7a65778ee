-- Drop and recreate views without SECURITY DEFINER to fix security issue

-- Drop existing views
DROP VIEW IF EXISTS public.chat_room_details_view;
DROP VIEW IF EXISTS public.chat_messages_with_sender;

-- Recreate chat_messages_with_sender view without SECURITY DEFINER
-- Direct join to profiles to avoid exposing auth.users data
CREATE VIEW public.chat_messages_with_sender AS
SELECT
    cm.id,
    cm.room_id,
    cm.sender_id,
    cm.content,
    cm.created_at,
    p.full_name AS sender_full_name
FROM
    public.chat_messages cm
LEFT JOIN
    public.profiles p ON cm.sender_id = p.id;

-- Recreate chat_room_details_view without SECURITY DEFINER
-- Direct join to profiles to avoid exposing auth.users data
CREATE VIEW public.chat_room_details_view AS
SELECT
    cr.id as room_id,
    cr.created_at as room_created_at,
    cr.updated_at as room_updated_at,
    cr.workshop_id,
    w.name as workshop_name,
    cr.client_id,
    client_profile.full_name as client_full_name,
    cr.provider_organization_id,
    po.name as provider_organization_name,
    po.profile_photo_url as provider_organization_profile_photo_url
FROM
    public.chat_rooms cr
LEFT JOIN
    public.workshops w ON cr.workshop_id = w.id
LEFT JOIN
    public.profiles client_profile ON cr.client_id = client_profile.id
LEFT JOIN
    public.provider_organizations po ON cr.provider_organization_id = po.id;

-- Grant appropriate permissions
GRANT SELECT ON public.chat_messages_with_sender TO authenticated;
GRANT SELECT ON public.chat_room_details_view TO authenticated;