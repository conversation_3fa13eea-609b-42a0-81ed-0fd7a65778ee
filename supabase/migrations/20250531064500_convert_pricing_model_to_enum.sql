-- Create pricing_model enum type
CREATE TYPE pricing_model AS ENUM ('per_person', 'total');

-- Add temporary column with enum type
ALTER TABLE workshops ADD COLUMN pricing_model_enum pricing_model;

-- Copy data from text column to enum column
UPDATE workshops 
SET pricing_model_enum = pricing_model::pricing_model
WHERE pricing_model IS NOT NULL;

-- Drop the old text column
ALTER TABLE workshops DROP COLUMN pricing_model;

-- Rename the enum column to the original name
ALTER TABLE workshops RENAME COLUMN pricing_model_enum TO pricing_model;

-- Add NOT NULL constraint after data migration
ALTER TABLE workshops ALTER COLUMN pricing_model SET NOT NULL;

-- Add default value (optional, based on your business logic)
ALTER TABLE workshops ALTER COLUMN pricing_model SET DEFAULT 'total';