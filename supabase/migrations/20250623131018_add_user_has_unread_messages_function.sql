-- Create efficient function to get total unread count for a user across all their chat rooms
-- This replaces the need to call get_unread_count for each room individually

CREATE OR REPLACE FUNCTION get_user_has_unread_messages(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has any unread messages across all their chat rooms
    -- This combines client and provider room logic into a single efficient query
    -- Uses EXISTS for maximum performance - stops as soon as one unread message is found
    RETURN EXISTS(
        SELECT 1
        FROM chat_rooms cr
        LEFT JOIN chat_room_read_status crs ON cr.id = crs.room_id AND crs.user_id = p_user_id
        WHERE (
            -- User is the client
            cr.client_id = p_user_id
            OR 
            -- User is a provider (via their organization)
            cr.provider_organization_id IN (
                SELECT organization_id 
                FROM providers 
                WHERE id = p_user_id
            )
        )
        AND (
            -- Either user has never read this room and it has messages
            (crs.last_read_at IS NULL AND EXISTS(
                SELECT 1 FROM chat_messages cm WHERE cm.room_id = cr.id
            ))
            OR
            -- Or user has read the room but there are newer messages
            (crs.last_read_at IS NOT NULL AND EXISTS(
                SELECT 1 FROM chat_messages cm 
                WHERE cm.room_id = cr.id AND cm.created_at > crs.last_read_at
            ))
        )
    );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_has_unread_messages(UUID) TO authenticated;