-- Create a type for a single time slot (without day)
CREATE TYPE time_slot AS (
    start_time TIME(0), -- TIME(0) excludes seconds
    end_time TIME(0)    -- TIME(0) excludes seconds
);

-- Create a type for workshop availability with one slot per day
CREATE TYPE workshop_daily_availability AS (
    monday time_slot,
    tuesday time_slot,
    wednesday time_slot,
    thursday time_slot,
    friday time_slot,
    saturday time_slot,
    sunday time_slot
);

-- Drop the existing availability column and create the new one
ALTER TABLE workshops
    DROP COLUMN availability,
    ADD COLUMN availability workshop_daily_availability;

-- Add a comment to explain the new structure
COMMENT ON COLUMN workshops.availability IS 'Workshop availability with one time slot per day. Each day can have a start and end time in HH:MM format, or be NULL if not available.';