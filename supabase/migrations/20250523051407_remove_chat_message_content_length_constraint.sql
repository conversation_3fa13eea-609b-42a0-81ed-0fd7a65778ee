-- Attempt to drop the content length check constraint from chat_messages
-- This assumes the constraint was auto-named 'chat_messages_content_check'.
-- If this migration fails, you may need to find the exact constraint name using:
-- SELECT conname FROM pg_constraint WHERE conrelid = 'public.chat_messages'::regclass AND pg_get_constraintdef(oid) = 'CHECK ((length(content) > 0))';
-- And then replace 'chat_messages_content_check' with the actual name in the DROP CONSTRAINT line.

ALTER TABLE public.chat_messages
DROP CONSTRAINT chat_messages_content_check;

-- Update the comment on the column to reflect the change
COMMENT ON COLUMN public.chat_messages.content IS 'The text content of the message. Can be empty, especially if an attachment is present.';
