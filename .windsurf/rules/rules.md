---
trigger: always_on
---

## Cascade Self-Maintenance of Rules

- Proactively identify and suggest new rules for files within the `.windsurf/rules/` directory based on confirmed learnings, USER preferences, and established conventions from our interactions.
- Ensure any proposed rules are concise, actionable, and aim to improve future collaboration.
- Always seek explicit USER confirmation before adding or modifying rules in these files.

Whenever adding or changing any rules, look to see if an existing rule already exists.

Table of contents for this directory:

- code-style.md: Defines code quality standards and principles for software design.
- general.md: Provides general guidelines for how to behave as an AI collaborator on the code.
- tech-stack.md: Outlines the technology stack used in the project.

Update the above table of contents as needed.
