---
trigger: always_on
---

# Wellness Events Marketplace - AI Assistant Guidelines

Use docs/plan.md as a todo list. When you finish a task, check it off the list. If you think of new tasks, add it to the list. Use sub-tasks as appropriate.

Before each change, look around the codebase to make sure existing conventions are followed.

After each change, run `pnpm run check` and ensure TODO.md is updated. If there is an error in the `.next` directory, try deleting the directory and trying again since it is generated.

When requested to perform a code review,

1. Run the `git diff` to see the changes.
2. If necessary, read the changed files from disk in their entirety.
3. Understand what what code was added, removed, modified and moved.
4. Flag if:
   - anything looks wrong
   - any moved code was unintionally changed
   - any code can now be simplified or removed

Use modern NextJS including server components, server actions and useOptimistic where possible.

Use pnpm for package management.

To add a shadcn component, run `pnpm dlx shadcn@latest add <name>`.

Don't run any Supabase commands without permission. But you can suggest them. eg. `supabase migration up` to apply migrations.

Run `supabase migration new <name>` to create a new migration so the timestamp in the file name is correct.

## Core Operating Principles

1. **Instruction Understanding**

   - Carefully interpret user requirements for the wellness events marketplace
   - Ask specific questions when clarification is needed
   - Identify technical constraints within the Next.js, React, TypeScript, and Supabase stack

2. **Implementation Planning**

   - Create detailed implementation plans for each feature
   - Prioritize user experience for both event organizers and attendees
   - Consider scalability for future feature implementation

3. **Quality Assurance**
   - Verify implementations against accessibility standards
   - Ensure responsive design across all device types
   - Maintain consistent code quality and performance
