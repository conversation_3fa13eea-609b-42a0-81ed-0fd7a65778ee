---
trigger: always_on
---

## Code Quality Standards

### 1. Naming Conventions

- Components: PascalCase (e.g., `EventCard`, `ProfileHeader`)
- Files: kebab-case (e.g., `event-card.tsx`, `profile-header.tsx`)
- Functions: camelCase (e.g., `getEvents`, `updateProfile`)
- Constants: UPPER_SNAKE_CASE (e.g., `API_URL`, `MAX_EVENTS`)
- Types/Interfaces: PascalCase (e.g., `EventType`, `UserProfile`)

### 2. Component Structure

- Use functional React components with TypeScript
- Keep files small, refactor to split them when they get too big
- Separate business logic from UI components
- Use proper error boundaries
- Prefer required props over optional props when possible

### 3. Styling Approach

- Use Tailwind CSS for styling
- Follow shadcn/ui component patterns
- Maintain consistent spacing and sizing
- Ensure responsive design for all components

## Software Design Principles

- **Minimize Complexity**: Strive for simplicity in system structure to enhance understanding and modifiability.
- **Design Deep Modules**: Create modules with simple interfaces that encapsulate powerful functionality.
- **Information Hiding**: Conceal implementation details within modules, exposing only what is necessary.
- **General-Purpose Modules**: Favor modules that are broadly applicable over specialized ones.
- **Pull Complexity Downwards**: Prioritize simple interfaces, even if it means more complex implementations within the module.
- **Meaningful Comments**: Write comments that explain non-obvious aspects of the code.
- **Define Errors Out of Existence**: Design interfaces and systems to prevent errors from occurring where possible.
- **DRY (Don't Repeat Yourself)**: Define types and interfaces once and reuse them throughout the codebase.
- **Trust Your Data Sources**: Avoid redundant validation when data has already been validated upstream.
- **Minimal Error Handling**: Let errors propagate naturally where appropriate; don't catch errors unless you're adding value.

## API Implementation

- Use Server Actions for data mutations
- Server Components for data fetching
- Implement proper error handling
- Validate all inputs with Zod
- Structure API responses consistently

## Accessibility Standards

- Follow WCAG 2.1 AA guidelines
- Ensure keyboard navigation
- Implement proper ARIA attributes
- Maintain sufficient color contrast
- Support screen readers
- Manage focus for modals and dialogs

## Performance Optimization

- Use Next.js Image component
- Minimize client-side components
- Utilize server components where appropriate

## TypeScript Best Practices

- Define shared types at the top of the file before they're used
- Avoid using `any` type; prefer explicit typing
- Avoid type assertions where possible
- Prefer non-optional function parameters over optional ones

## Error Handling

- Log errors once, at the appropriate level of abstraction
- Use descriptive error messages that help with debugging
- Only catch errors if you're adding value (logging, transforming, or recovering)
- For multiple independent operations, catch errors individually so one error doesn't stop the other operations
