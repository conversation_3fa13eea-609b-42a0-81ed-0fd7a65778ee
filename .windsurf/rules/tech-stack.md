---
trigger: always_on
---

## Technology Stack

### Frontend

- Next.js (App Router): Latest stable version
- React: Latest stable version
- TypeScript: Strict mode enabled
- Tailwind CSS: For styling
- shadcn/ui: For component library
- React Hook Form: For form handling
- Zod: For validation
- Lucide React: For icons

### Backend

- Supabase: For database, authentication, and storage
- Server Components: For data fetching
- Server Actions: For API endpoints

### Development Tools

- ESLint: For code quality
- Prettier: For code formatting
- TypeScript: For type checking
- Jest/React Testing Library: For testing
