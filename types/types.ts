import type { Database } from "@/types/database.types";

// Common database-derived types
export type DayOfWeek = Database["public"]["Enums"]["day_of_week"];
export type TimeSlot = Database["public"]["CompositeTypes"]["time_slot"];
export type WorkshopDailyAvailability =
  Database["public"]["CompositeTypes"]["workshop_daily_availability"];
export type UserType = Database["public"]["Enums"]["user_type"];
export type WorkshopFormat = Database["public"]["Enums"]["workshop_format"];
export type VenueType = Database["public"]["Enums"]["venue_type"];
export type QuoteStatus = Database["public"]["Enums"]["quote_status"];
export type PricingModel = Database["public"]["Enums"]["pricing_model"];

// Export commonly used base table types
export type Category = Database["public"]["Tables"]["categories"]["Row"];
export type CategoryGroup =
  Database["public"]["Tables"]["category_groups"]["Row"];

// Re-export from specific type files for convenience
export type { BookingWithRelations } from "./booking";
