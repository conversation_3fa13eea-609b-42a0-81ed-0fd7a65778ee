import type { Database } from "./database.types";

// Define the user types as a union type for better type safety
type UserType = "provider" | "client" | "admin";

// Extract the Profile type from the database types
type Profile = Database["public"]["Tables"]["profiles"]["Row"];

// Create a type-safe profile with proper user_type
export interface TypedProfile extends Omit<Profile, "user_type"> {
  user_type: UserType;
}

type Provider = Database["public"]["Tables"]["providers"]["Row"];
type Client = Database["public"]["Tables"]["clients"]["Row"];

// Combined user profile with provider, client, or admin details
export interface ProviderProfile extends TypedProfile {
  user_type: "provider";
  provider: Provider;
}

export interface ClientProfile extends TypedProfile {
  user_type: "client";
  client: Client;
}
