import type { Database } from "./database.types";

// Base chat room type from database
export type ChatRoom = Database["public"]["Tables"]["chat_rooms"]["Row"];

// Extended chat room types with relations
export type ChatRoomWithParticipants = ChatRoom & {
  workshop: Pick<
    Database["public"]["Tables"]["workshops"]["Row"],
    "id" | "name" | "currency"
  >;
  client?: Pick<
    Database["public"]["Tables"]["profiles"]["Row"],
    "id" | "full_name"
  > | null;
  provider_organization?: Pick<
    Database["public"]["Tables"]["provider_organizations"]["Row"],
    "id" | "name" | "profile_photo_url"
  > | null;
};

export type ChatRoomListItem = {
  id: string;
  workshop: Pick<
    Database["public"]["Tables"]["workshops"]["Row"],
    "id" | "name"
  > | null;
  client: Pick<
    Database["public"]["Tables"]["profiles"]["Row"],
    "id" | "full_name"
  > | null;
  provider_organization: Pick<
    Database["public"]["Tables"]["provider_organizations"]["Row"],
    "id" | "name" | "profile_photo_url"
  > | null;
  latest_message?: Pick<
    Database["public"]["Tables"]["chat_messages"]["Row"],
    "content" | "created_at" | "sender_id"
  > | null;
  unread_count: number;
};
