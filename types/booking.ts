import type { Database } from "./database.types";

// Base booking type from database
export type Booking = Database["public"]["Tables"]["bookings"]["Row"];
export type BookingInsert = Database["public"]["Tables"]["bookings"]["Insert"];
export type BookingUpdate = Database["public"]["Tables"]["bookings"]["Update"];

// Extended booking types with relations
export type BookingWithWorkshop = Booking & {
  workshops: Pick<
    Database["public"]["Tables"]["workshops"]["Row"],
    "id" | "name" | "format" | "provider_id"
  > | null;
};

export type BookingWithClient = Booking & {
  clients: Pick<
    Database["public"]["Tables"]["clients"]["Row"],
    "id" | "company_name"
  > | null;
};

export type BookingWithRelations = Booking & {
  workshops: Database["public"]["Tables"]["workshops"]["Row"];
  clients: {
    id: string;
    profiles: Pick<
      Database["public"]["Tables"]["profiles"]["Row"],
      "full_name"
    > | null;
  } | null;
};

export type BookingWithFullDetails = Booking & {
  workshops:
    | (Pick<
        Database["public"]["Tables"]["workshops"]["Row"],
        "id" | "name" | "format" | "provider_id"
      > & {
        provider_organizations: Pick<
          Database["public"]["Tables"]["provider_organizations"]["Row"],
          "id" | "name"
        > | null;
      })
    | null;
  clients: Pick<
    Database["public"]["Tables"]["clients"]["Row"],
    "id" | "company_name"
  > | null;
};
