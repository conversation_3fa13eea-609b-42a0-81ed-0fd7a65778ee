export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      bookings: {
        Row: {
          booking_datetime: string;
          client_id: string;
          created_at: string;
          id: string;
          participant_count: number;
          special_requirements: string | null;
          status: Database["public"]["Enums"]["booking_status"];
          terms_accepted: boolean;
          total_price: number;
          updated_at: string;
          workshop_id: string;
        };
        Insert: {
          booking_datetime: string;
          client_id: string;
          created_at?: string;
          id?: string;
          participant_count: number;
          special_requirements?: string | null;
          status?: Database["public"]["Enums"]["booking_status"];
          terms_accepted?: boolean;
          total_price: number;
          updated_at?: string;
          workshop_id: string;
        };
        Update: {
          booking_datetime?: string;
          client_id?: string;
          created_at?: string;
          id?: string;
          participant_count?: number;
          special_requirements?: string | null;
          status?: Database["public"]["Enums"]["booking_status"];
          terms_accepted?: boolean;
          total_price?: number;
          updated_at?: string;
          workshop_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "bookings_client_id_fkey";
            columns: ["client_id"];
            isOneToOne: false;
            referencedRelation: "clients";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "bookings_workshop_id_fkey";
            columns: ["workshop_id"];
            isOneToOne: false;
            referencedRelation: "workshops";
            referencedColumns: ["id"];
          },
        ];
      };
      categories: {
        Row: {
          category_group_id: string | null;
          created_at: string;
          id: string;
          name: string;
          updated_at: string;
        };
        Insert: {
          category_group_id?: string | null;
          created_at?: string;
          id?: string;
          name: string;
          updated_at?: string;
        };
        Update: {
          category_group_id?: string | null;
          created_at?: string;
          id?: string;
          name?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "categories_category_group_id_fkey";
            columns: ["category_group_id"];
            isOneToOne: false;
            referencedRelation: "category_groups";
            referencedColumns: ["id"];
          },
        ];
      };
      category_groups: {
        Row: {
          created_at: string;
          id: string;
          name: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          name: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          name?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      chat_messages: {
        Row: {
          content: string;
          created_at: string;
          id: string;
          quote_id: string | null;
          room_id: string;
          sender_id: string | null;
        };
        Insert: {
          content: string;
          created_at?: string;
          id?: string;
          quote_id?: string | null;
          room_id: string;
          sender_id?: string | null;
        };
        Update: {
          content?: string;
          created_at?: string;
          id?: string;
          quote_id?: string | null;
          room_id?: string;
          sender_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "chat_messages_quote_id_fkey";
            columns: ["quote_id"];
            isOneToOne: false;
            referencedRelation: "quotes";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "chat_messages_room_id_fkey";
            columns: ["room_id"];
            isOneToOne: false;
            referencedRelation: "chat_room_details_view";
            referencedColumns: ["room_id"];
          },
          {
            foreignKeyName: "chat_messages_room_id_fkey";
            columns: ["room_id"];
            isOneToOne: false;
            referencedRelation: "chat_rooms";
            referencedColumns: ["id"];
          },
        ];
      };
      chat_room_read_status: {
        Row: {
          created_at: string;
          last_read_at: string;
          room_id: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          last_read_at?: string;
          room_id: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          last_read_at?: string;
          room_id?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "chat_room_read_status_room_id_fkey";
            columns: ["room_id"];
            isOneToOne: false;
            referencedRelation: "chat_room_details_view";
            referencedColumns: ["room_id"];
          },
          {
            foreignKeyName: "chat_room_read_status_room_id_fkey";
            columns: ["room_id"];
            isOneToOne: false;
            referencedRelation: "chat_rooms";
            referencedColumns: ["id"];
          },
        ];
      };
      chat_rooms: {
        Row: {
          client_id: string;
          created_at: string;
          id: string;
          provider_organization_id: string;
          updated_at: string;
          workshop_id: string;
        };
        Insert: {
          client_id: string;
          created_at?: string;
          id?: string;
          provider_organization_id: string;
          updated_at?: string;
          workshop_id: string;
        };
        Update: {
          client_id?: string;
          created_at?: string;
          id?: string;
          provider_organization_id?: string;
          updated_at?: string;
          workshop_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "chat_rooms_provider_organization_id_fkey";
            columns: ["provider_organization_id"];
            isOneToOne: false;
            referencedRelation: "provider_organizations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "chat_rooms_workshop_id_fkey";
            columns: ["workshop_id"];
            isOneToOne: false;
            referencedRelation: "workshops";
            referencedColumns: ["id"];
          },
        ];
      };
      clients: {
        Row: {
          company_name: string;
          created_at: string;
          id: string;
          location: string | null;
          updated_at: string;
        };
        Insert: {
          company_name: string;
          created_at?: string;
          id: string;
          location?: string | null;
          updated_at?: string;
        };
        Update: {
          company_name?: string;
          created_at?: string;
          id?: string;
          location?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "clients_id_fkey";
            columns: ["id"];
            isOneToOne: true;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
        ];
      };
      payments: {
        Row: {
          amount: number;
          booking_id: string;
          created_at: string;
          id: string;
          payment_date: string | null;
          payment_method: string | null;
          status: Database["public"]["Enums"]["payment_status"];
          transaction_id: string | null;
          updated_at: string;
        };
        Insert: {
          amount: number;
          booking_id: string;
          created_at?: string;
          id?: string;
          payment_date?: string | null;
          payment_method?: string | null;
          status?: Database["public"]["Enums"]["payment_status"];
          transaction_id?: string | null;
          updated_at?: string;
        };
        Update: {
          amount?: number;
          booking_id?: string;
          created_at?: string;
          id?: string;
          payment_date?: string | null;
          payment_method?: string | null;
          status?: Database["public"]["Enums"]["payment_status"];
          transaction_id?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "payments_booking_id_fkey";
            columns: ["booking_id"];
            isOneToOne: false;
            referencedRelation: "bookings";
            referencedColumns: ["id"];
          },
        ];
      };
      profiles: {
        Row: {
          created_at: string;
          email: string;
          full_name: string | null;
          id: string;
          phone_number: string | null;
          updated_at: string;
          user_type: Database["public"]["Enums"]["user_type"];
        };
        Insert: {
          created_at?: string;
          email: string;
          full_name?: string | null;
          id: string;
          phone_number?: string | null;
          updated_at?: string;
          user_type: Database["public"]["Enums"]["user_type"];
        };
        Update: {
          created_at?: string;
          email?: string;
          full_name?: string | null;
          id?: string;
          phone_number?: string | null;
          updated_at?: string;
          user_type?: Database["public"]["Enums"]["user_type"];
        };
        Relationships: [];
      };
      provider_ledger: {
        Row: {
          amount: number;
          created_at: string;
          currency: string;
          description: string | null;
          id: string;
          provider_id: string;
          quote_id: string | null;
          transaction_type: Database["public"]["Enums"]["transaction_type"];
          withdrawal_id: string | null;
        };
        Insert: {
          amount: number;
          created_at?: string;
          currency: string;
          description?: string | null;
          id?: string;
          provider_id: string;
          quote_id?: string | null;
          transaction_type: Database["public"]["Enums"]["transaction_type"];
          withdrawal_id?: string | null;
        };
        Update: {
          amount?: number;
          created_at?: string;
          currency?: string;
          description?: string | null;
          id?: string;
          provider_id?: string;
          quote_id?: string | null;
          transaction_type?: Database["public"]["Enums"]["transaction_type"];
          withdrawal_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "provider_ledger_provider_id_fkey";
            columns: ["provider_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "provider_ledger_quote_id_fkey";
            columns: ["quote_id"];
            isOneToOne: false;
            referencedRelation: "quotes";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "provider_ledger_withdrawal_id_fkey";
            columns: ["withdrawal_id"];
            isOneToOne: false;
            referencedRelation: "withdrawal_requests";
            referencedColumns: ["id"];
          },
        ];
      };
      provider_organizations: {
        Row: {
          city: string | null;
          country: string | null;
          created_at: string;
          description: string | null;
          id: string;
          location: string | null;
          name: string;
          profile_photo_url: string | null;
          updated_at: string;
        };
        Insert: {
          city?: string | null;
          country?: string | null;
          created_at?: string;
          description?: string | null;
          id?: string;
          location?: string | null;
          name: string;
          profile_photo_url?: string | null;
          updated_at?: string;
        };
        Update: {
          city?: string | null;
          country?: string | null;
          created_at?: string;
          description?: string | null;
          id?: string;
          location?: string | null;
          name?: string;
          profile_photo_url?: string | null;
          updated_at?: string;
        };
        Relationships: [];
      };
      providers: {
        Row: {
          created_at: string;
          id: string;
          organization_id: string;
          role: string | null;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id: string;
          organization_id: string;
          role?: string | null;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          organization_id?: string;
          role?: string | null;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "providers_id_fkey";
            columns: ["id"];
            isOneToOne: true;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "providers_organization_id_fkey";
            columns: ["organization_id"];
            isOneToOne: false;
            referencedRelation: "provider_organizations";
            referencedColumns: ["id"];
          },
        ];
      };
      quotes: {
        Row: {
          chat_room_id: string;
          client_id: string;
          created_at: string;
          currency: string;
          expires_at: string | null;
          fee: number | null;
          id: string;
          location: string;
          notes: string | null;
          paid_at: string | null;
          payment_intent_id: string | null;
          price: number;
          proposed_datetime: string;
          provider_earnings: number | null;
          provider_organization_id: string;
          status: Database["public"]["Enums"]["quote_status"];
          updated_at: string;
          workshop_id: string;
        };
        Insert: {
          chat_room_id: string;
          client_id: string;
          created_at?: string;
          currency: string;
          expires_at?: string | null;
          fee?: number | null;
          id?: string;
          location: string;
          notes?: string | null;
          paid_at?: string | null;
          payment_intent_id?: string | null;
          price: number;
          proposed_datetime: string;
          provider_earnings?: number | null;
          provider_organization_id: string;
          status?: Database["public"]["Enums"]["quote_status"];
          updated_at?: string;
          workshop_id: string;
        };
        Update: {
          chat_room_id?: string;
          client_id?: string;
          created_at?: string;
          currency?: string;
          expires_at?: string | null;
          fee?: number | null;
          id?: string;
          location?: string;
          notes?: string | null;
          paid_at?: string | null;
          payment_intent_id?: string | null;
          price?: number;
          proposed_datetime?: string;
          provider_earnings?: number | null;
          provider_organization_id?: string;
          status?: Database["public"]["Enums"]["quote_status"];
          updated_at?: string;
          workshop_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "quotes_chat_room_id_fkey";
            columns: ["chat_room_id"];
            isOneToOne: false;
            referencedRelation: "chat_room_details_view";
            referencedColumns: ["room_id"];
          },
          {
            foreignKeyName: "quotes_chat_room_id_fkey";
            columns: ["chat_room_id"];
            isOneToOne: false;
            referencedRelation: "chat_rooms";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "quotes_provider_organization_id_fkey";
            columns: ["provider_organization_id"];
            isOneToOne: false;
            referencedRelation: "provider_organizations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "quotes_workshop_id_fkey";
            columns: ["workshop_id"];
            isOneToOne: false;
            referencedRelation: "workshops";
            referencedColumns: ["id"];
          },
        ];
      };
      webhook_events: {
        Row: {
          created_at: string | null;
          event_id: string;
          event_type: string;
          payment_intent_id: string | null;
          processed_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          event_id: string;
          event_type: string;
          payment_intent_id?: string | null;
          processed_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          event_id?: string;
          event_type?: string;
          payment_intent_id?: string | null;
          processed_at?: string | null;
        };
        Relationships: [];
      };
      withdrawal_requests: {
        Row: {
          amount: number;
          bank_details: Json;
          created_at: string;
          currency: string;
          id: string;
          processed_at: string | null;
          provider_id: string;
          status: Database["public"]["Enums"]["withdrawal_status"];
        };
        Insert: {
          amount: number;
          bank_details: Json;
          created_at?: string;
          currency: string;
          id?: string;
          processed_at?: string | null;
          provider_id: string;
          status?: Database["public"]["Enums"]["withdrawal_status"];
        };
        Update: {
          amount?: number;
          bank_details?: Json;
          created_at?: string;
          currency?: string;
          id?: string;
          processed_at?: string | null;
          provider_id?: string;
          status?: Database["public"]["Enums"]["withdrawal_status"];
        };
        Relationships: [
          {
            foreignKeyName: "withdrawal_requests_provider_id_fkey";
            columns: ["provider_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
        ];
      };
      workshop_categories: {
        Row: {
          category_id: string;
          created_at: string;
          workshop_id: string;
        };
        Insert: {
          category_id: string;
          created_at?: string;
          workshop_id: string;
        };
        Update: {
          category_id?: string;
          created_at?: string;
          workshop_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "workshop_categories_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "workshop_categories_workshop_id_fkey";
            columns: ["workshop_id"];
            isOneToOne: false;
            referencedRelation: "workshops";
            referencedColumns: ["id"];
          },
        ];
      };
      workshops: {
        Row: {
          availability:
            | Database["public"]["CompositeTypes"]["workshop_daily_availability"]
            | null;
          client_site_travel_fee: number | null;
          created_at: string;
          currency: string;
          description: string | null;
          duration: string;
          format: Database["public"]["Enums"]["workshop_format"];
          group_discount_available: boolean;
          id: string;
          image_url: string | null;
          lead_time: string | null;
          location: string | null;
          max_capacity: number | null;
          min_capacity: number | null;
          name: string;
          prerequisites: string | null;
          price: number;
          pricing_model: Database["public"]["Enums"]["pricing_model"];
          provider_id: string;
          published: boolean;
          updated_at: string;
          venue_type: Database["public"]["Enums"]["venue_type"];
        };
        Insert: {
          availability?:
            | Database["public"]["CompositeTypes"]["workshop_daily_availability"]
            | null;
          client_site_travel_fee?: number | null;
          created_at?: string;
          currency?: string;
          description?: string | null;
          duration: string;
          format?: Database["public"]["Enums"]["workshop_format"];
          group_discount_available?: boolean;
          id?: string;
          image_url?: string | null;
          lead_time?: string | null;
          location?: string | null;
          max_capacity?: number | null;
          min_capacity?: number | null;
          name: string;
          prerequisites?: string | null;
          price: number;
          pricing_model?: Database["public"]["Enums"]["pricing_model"];
          provider_id: string;
          published?: boolean;
          updated_at?: string;
          venue_type?: Database["public"]["Enums"]["venue_type"];
        };
        Update: {
          availability?:
            | Database["public"]["CompositeTypes"]["workshop_daily_availability"]
            | null;
          client_site_travel_fee?: number | null;
          created_at?: string;
          currency?: string;
          description?: string | null;
          duration?: string;
          format?: Database["public"]["Enums"]["workshop_format"];
          group_discount_available?: boolean;
          id?: string;
          image_url?: string | null;
          lead_time?: string | null;
          location?: string | null;
          max_capacity?: number | null;
          min_capacity?: number | null;
          name?: string;
          prerequisites?: string | null;
          price?: number;
          pricing_model?: Database["public"]["Enums"]["pricing_model"];
          provider_id?: string;
          published?: boolean;
          updated_at?: string;
          venue_type?: Database["public"]["Enums"]["venue_type"];
        };
        Relationships: [
          {
            foreignKeyName: "workshops_provider_id_fkey";
            columns: ["provider_id"];
            isOneToOne: false;
            referencedRelation: "provider_organizations";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Views: {
      chat_messages_with_sender: {
        Row: {
          content: string | null;
          created_at: string | null;
          id: string | null;
          room_id: string | null;
          sender_full_name: string | null;
          sender_id: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "chat_messages_room_id_fkey";
            columns: ["room_id"];
            isOneToOne: false;
            referencedRelation: "chat_room_details_view";
            referencedColumns: ["room_id"];
          },
          {
            foreignKeyName: "chat_messages_room_id_fkey";
            columns: ["room_id"];
            isOneToOne: false;
            referencedRelation: "chat_rooms";
            referencedColumns: ["id"];
          },
        ];
      };
      chat_room_details_view: {
        Row: {
          client_full_name: string | null;
          client_id: string | null;
          provider_organization_id: string | null;
          provider_organization_name: string | null;
          provider_organization_profile_photo_url: string | null;
          room_created_at: string | null;
          room_id: string | null;
          room_updated_at: string | null;
          workshop_currency: string | null;
          workshop_id: string | null;
          workshop_name: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "chat_rooms_provider_organization_id_fkey";
            columns: ["provider_organization_id"];
            isOneToOne: false;
            referencedRelation: "provider_organizations";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "chat_rooms_workshop_id_fkey";
            columns: ["workshop_id"];
            isOneToOne: false;
            referencedRelation: "workshops";
            referencedColumns: ["id"];
          },
        ];
      };
      provider_balances: {
        Row: {
          balance: number | null;
          currency: string | null;
          provider_id: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "provider_ledger_provider_id_fkey";
            columns: ["provider_id"];
            isOneToOne: false;
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
        ];
      };
    };
    Functions: {
      chat_room_details_with_unread: {
        Args: { p_user_id: string };
        Returns: {
          room_id: string;
          room_created_at: string;
          room_updated_at: string;
          workshop_id: string;
          workshop_name: string;
          workshop_currency: string;
          client_id: string;
          client_full_name: string;
          provider_organization_id: string;
          provider_organization_name: string;
          provider_organization_profile_photo_url: string;
          has_unread: boolean;
        }[];
      };
      create_payment_intent_for_quote: {
        Args: { p_quote_id: string; p_payment_intent_id: string };
        Returns: undefined;
      };
      get_unread_count: {
        Args: { p_room_id: string; p_user_id: string };
        Returns: number;
      };
      get_user_has_unread_messages: {
        Args: { p_user_id: string };
        Returns: boolean;
      };
      process_webhook_event: {
        Args: {
          p_event_id: string;
          p_event_type: string;
          p_payment_intent_id: string;
        };
        Returns: boolean;
      };
      submit_payment_for_quote: {
        Args: { p_quote_id: string };
        Returns: undefined;
      };
    };
    Enums: {
      booking_status: "pending" | "confirmed" | "cancelled" | "completed";
      day_of_week:
        | "monday"
        | "tuesday"
        | "wednesday"
        | "thursday"
        | "friday"
        | "saturday"
        | "sunday";
      payment_status: "pending" | "paid" | "refunded" | "failed";
      pricing_model: "per_person" | "total";
      quote_status:
        | "pending"
        | "rejected"
        | "expired"
        | "paid"
        | "superceded"
        | "cancelled"
        | "payment_processing"
        | "payment_failed"
        | "payment_intent_created";
      transaction_type: "payment" | "withdrawal" | "fee" | "refund";
      user_type: "provider" | "client" | "admin";
      venue_type:
        | "provider_location"
        | "client_location"
        | "provider_or_client_location"
        | "online";
      withdrawal_status: "pending" | "completed" | "rejected";
      workshop_format: "in_person" | "online" | "hybrid";
    };
    CompositeTypes: {
      time_slot: {
        start_time: string | null;
        end_time: string | null;
      };
      workshop_daily_availability: {
        monday: Database["public"]["CompositeTypes"]["time_slot"] | null;
        tuesday: Database["public"]["CompositeTypes"]["time_slot"] | null;
        wednesday: Database["public"]["CompositeTypes"]["time_slot"] | null;
        thursday: Database["public"]["CompositeTypes"]["time_slot"] | null;
        friday: Database["public"]["CompositeTypes"]["time_slot"] | null;
        saturday: Database["public"]["CompositeTypes"]["time_slot"] | null;
        sunday: Database["public"]["CompositeTypes"]["time_slot"] | null;
      };
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      booking_status: ["pending", "confirmed", "cancelled", "completed"],
      day_of_week: [
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
        "sunday",
      ],
      payment_status: ["pending", "paid", "refunded", "failed"],
      pricing_model: ["per_person", "total"],
      quote_status: [
        "pending",
        "rejected",
        "expired",
        "paid",
        "superceded",
        "cancelled",
        "payment_processing",
        "payment_failed",
        "payment_intent_created",
      ],
      transaction_type: ["payment", "withdrawal", "fee", "refund"],
      user_type: ["provider", "client", "admin"],
      venue_type: [
        "provider_location",
        "client_location",
        "provider_or_client_location",
        "online",
      ],
      withdrawal_status: ["pending", "completed", "rejected"],
      workshop_format: ["in_person", "online", "hybrid"],
    },
  },
} as const;
