import type { Database } from "./database.types";

// Base provider organization type from database
export type ProviderOrganization =
  Database["public"]["Tables"]["provider_organizations"]["Row"];
export type ProviderOrganizationInsert =
  Database["public"]["Tables"]["provider_organizations"]["Insert"];
export type ProviderOrganizationUpdate =
  Database["public"]["Tables"]["provider_organizations"]["Update"];

// Extended provider organization types with relations
export type ProviderOrganizationWithProviders = ProviderOrganization & {
  providers: Array<
    Pick<
      Database["public"]["Tables"]["providers"]["Row"],
      "id" | "organization_id"
    >
  >;
};

export type ProviderOrganizationWithWorkshops = ProviderOrganization & {
  workshops: Array<
    Pick<
      Database["public"]["Tables"]["workshops"]["Row"],
      "id" | "name" | "format" | "published"
    >
  >;
};

export type ProviderOrganizationWithFullDetails = ProviderOrganization & {
  providers: Array<
    Pick<
      Database["public"]["Tables"]["providers"]["Row"],
      "id" | "organization_id"
    > & {
      profiles: Pick<
        Database["public"]["Tables"]["profiles"]["Row"],
        "id" | "full_name" | "email"
      > | null;
    }
  >;
  workshops: Array<
    Pick<
      Database["public"]["Tables"]["workshops"]["Row"],
      "id" | "name" | "format" | "published"
    >
  >;
};
