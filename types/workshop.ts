import type { Database } from "./database.types";

// Base workshop type from database
export type Workshop = Database["public"]["Tables"]["workshops"]["Row"];
export type WorkshopInsert =
  Database["public"]["Tables"]["workshops"]["Insert"];
export type WorkshopUpdate =
  Database["public"]["Tables"]["workshops"]["Update"];

// Extended workshop types with relations
export type WorkshopWithProvider = Workshop & {
  provider_organizations: Pick<
    Database["public"]["Tables"]["provider_organizations"]["Row"],
    "id" | "name" | "profile_photo_url" | "city"
  > | null;
};

export type WorkshopWithCategories = Workshop & {
  workshop_categories: Array<{
    category_id: string;
    categories: Pick<
      Database["public"]["Tables"]["categories"]["Row"],
      "id" | "name"
    > | null;
  }>;
};

export type WorkshopWithFullDetails = Workshop & {
  provider_organizations: Pick<
    Database["public"]["Tables"]["provider_organizations"]["Row"],
    "id" | "name" | "profile_photo_url" | "city" | "country"
  > | null;
  workshop_categories: Array<{
    category_id: string;
    categories: Pick<
      Database["public"]["Tables"]["categories"]["Row"],
      "id" | "name"
    > | null;
  }>;
};
