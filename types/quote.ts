import type { Database } from "./database.types";

// Base quote type from database
export type Quote = Database["public"]["Tables"]["quotes"]["Row"];
export type QuoteInsert = Database["public"]["Tables"]["quotes"]["Insert"];
export type QuoteUpdate = Database["public"]["Tables"]["quotes"]["Update"];

// Extended quote type with workshop details
export type QuoteWithWorkshopDetails = Quote & {
  workshops: Pick<
    Database["public"]["Tables"]["workshops"]["Row"],
    "name"
  > | null;
  provider_organizations: Pick<
    Database["public"]["Tables"]["provider_organizations"]["Row"],
    "name"
  >;
};

// Quote data for email notifications - only fields needed for display
export type SimpleQuote = Pick<
  Quote,
  "proposed_datetime" | "location" | "price" | "currency" | "notes"
> & {
  workshops: Pick<
    Database["public"]["Tables"]["workshops"]["Row"],
    "name"
  > | null;
  provider_organizations: Pick<
    Database["public"]["Tables"]["provider_organizations"]["Row"],
    "name"
  >;
};
