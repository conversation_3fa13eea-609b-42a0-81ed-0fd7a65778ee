import { ProviderOrganizationForm } from "@/components/profile/provider-organization-form";
import { requireUser } from "@/lib/auth";
import { createClient } from "@/utils/supabase/server";

export default async function DashboardOrganizationPage() {
  const user = await requireUser();
  const supabase = await createClient();

  // Get user profile with provider and organization data in single query
  const { data: userWithOrg } = await supabase
    .from("profiles")
    .select(
      `
      *,
      providers!id (
        *,
        provider_organizations!organization_id (*)
      )
    `,
    )
    .eq("id", user.id)
    .single();

  if (!userWithOrg) {
    return (
      <div className="container py-10">
        <div className="mx-auto max-w-md text-center">
          <h1 className="mb-4 text-2xl font-bold text-red-600">
            Profile Not Found
          </h1>
          <p className="text-gray-600">
            Please complete your profile setup first.
          </p>
        </div>
      </div>
    );
  }

  // If this is not a provider account, show error
  if (userWithOrg.user_type !== "provider") {
    return (
      <div className="container py-10">
        <div className="mx-auto max-w-md text-center">
          <h1 className="mb-4 text-2xl font-bold text-red-600">
            Access Denied
          </h1>
          <p className="text-gray-600">
            This page is only available to wellness providers.
          </p>
        </div>
      </div>
    );
  }

  const provider = Array.isArray(userWithOrg.providers)
    ? userWithOrg.providers[0]
    : userWithOrg.providers;

  // If no provider record found, show error
  if (!provider) {
    return (
      <div className="container py-10">
        <div className="mx-auto max-w-md text-center">
          <h1 className="mb-4 text-2xl font-bold text-red-600">
            Provider Setup Incomplete
          </h1>
          <p className="text-gray-600">
            Your provider account setup is not complete.
          </p>
        </div>
      </div>
    );
  }

  const organization = Array.isArray(provider.provider_organizations)
    ? provider.provider_organizations[0]
    : provider.provider_organizations;

  if (!organization) {
    return (
      <div className="container py-10">
        <div className="mx-auto max-w-md text-center">
          <h1 className="mb-4 text-2xl font-bold text-red-600">
            Organization Not Found
          </h1>
          <p className="text-gray-600">
            Your organization details could not be found.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <h1 className="mb-6 text-2xl font-bold">Edit Organisation Details</h1>
      <ProviderOrganizationForm
        provider={provider}
        organization={organization}
      />
    </div>
  );
}
