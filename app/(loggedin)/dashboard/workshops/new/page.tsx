import { WorkshopForm } from "@/components/dashboard/workshop-form";
import { requireProviderWithDetails } from "@/lib/auth";
import { getWorkshopCategories } from "@/lib/workshop-categories-provider";
import { createClient } from "@/utils/supabase/server";

export default async function DashboardNewWorkshopPage() {
  const { provider } = await requireProviderWithDetails();
  const { categoryGroups, categories } = await getWorkshopCategories();

  // Fetch organization details for address prefilling
  const supabase = await createClient();
  const { data: organization } = await supabase
    .from("provider_organizations")
    .select("*")
    .eq("id", provider.organization_id)
    .single();

  return (
    <div className="container py-10">
      <div className="mx-auto max-w-3xl">
        <h1 className="mb-6 text-3xl font-bold tracking-tight">
          Create New Workshop
        </h1>
        <WorkshopForm
          organizationId={provider.organization_id}
          organization={organization}
          categoryGroups={categoryGroups}
          categories={categories}
          workshopCategories={[]}
        />
      </div>
    </div>
  );
}
