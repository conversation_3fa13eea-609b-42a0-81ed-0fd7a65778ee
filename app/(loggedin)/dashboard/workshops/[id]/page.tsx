import { notFound } from "next/navigation";
import React from "react";

import { WorkshopForm } from "@/components/dashboard/workshop-form";
import { requireProviderWithDetails } from "@/lib/auth";
import { getWorkshopCategories } from "@/lib/workshop-categories-provider";
import { createClient } from "@/utils/supabase/server";

interface DashboardWorkshopEditPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function DashboardWorkshopEditPage({
  params,
}: DashboardWorkshopEditPageProps) {
  const { provider } = await requireProviderWithDetails();
  const resolvedParams = await params;
  const supabase = await createClient();

  // Get workshop details
  const { data: workshop } = await supabase
    .from("workshops")
    .select("*")
    .eq("id", resolvedParams.id)
    .eq("provider_id", provider.organization_id)
    .single();

  if (!workshop) {
    notFound();
  }

  // Fetch categories and workshop categories
  const { categoryGroups, categories, workshopCategories } =
    await getWorkshopCategories(workshop.id);

  // Fetch organization details for address prefilling
  const { data: organization } = await supabase
    .from("provider_organizations")
    .select("*")
    .eq("id", provider.organization_id)
    .single();

  return (
    <div className="container py-10">
      <div className="mx-auto max-w-3xl">
        <h1 className="mb-6 text-3xl font-bold tracking-tight">
          Edit Workshop
        </h1>
        <WorkshopForm
          workshop={workshop}
          organizationId={provider.organization_id}
          organization={organization}
          categoryGroups={categoryGroups}
          categories={categories}
          workshopCategories={workshopCategories}
        />
      </div>
    </div>
  );
}
