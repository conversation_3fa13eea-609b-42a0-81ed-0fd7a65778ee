import { redirect } from "next/navigation";

import { ProfileForm } from "@/components/profile/profile-form";
import { requireUser } from "@/lib/auth";
import { createClient } from "@/utils/supabase/server";

export default async function DashboardEditPage() {
  const user = await requireUser();
  const supabase = await createClient();

  // Get user profile
  const { data: profile } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", user.id)
    .single();

  // If profile doesn't exist, redirect to main profile edit page
  if (!profile) {
    redirect("/profile/edit");
  }

  return (
    <div className="container py-10">
      <div className="mx-auto max-w-2xl">
        <h1 className="mb-6 text-3xl font-bold tracking-tight">Edit Profile</h1>
        <ProfileForm profile={profile} dashboardMode />
      </div>
    </div>
  );
}
