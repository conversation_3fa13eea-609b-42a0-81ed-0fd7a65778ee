"use server";

import { createClient } from "@/utils/supabase/server";

export interface LedgerEntry {
  id: string;
  transaction_type: "payment" | "withdrawal" | "fee" | "refund";
  amount: number;
  currency: string;
  quote_id: string | null;
  withdrawal_id: string | null;
  description: string | null;
  created_at: string;
}

export interface ProviderBalance {
  balance: number;
  currency: string;
}

export async function getProviderLedgerEntries(
  providerId: string,
): Promise<{ success: boolean; data?: LedgerEntry[]; error?: string }> {
  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from("provider_ledger")
      .select("*")
      .eq("provider_id", providerId)
      .order("created_at", { ascending: false });

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data: data as LedgerEntry[] };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "An error occurred while fetching ledger entries",
    };
  }
}

export async function getProviderBalance(
  providerId: string,
): Promise<{ success: boolean; data?: ProviderBalance; error?: string }> {
  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .rpc("get_provider_balance", { provider_id: providerId });

    if (error) {
      return { success: false, error: error.message };
    }

    return { 
      success: true, 
      data: { 
        balance: data || 0, 
        currency: "SGD" 
      } 
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "An error occurred while fetching balance",
    };
  }
}