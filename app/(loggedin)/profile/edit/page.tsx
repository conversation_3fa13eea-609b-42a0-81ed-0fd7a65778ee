import { redirect } from "next/navigation";

import { ProfileForm } from "@/components/profile/profile-form";
import { requireUser } from "@/lib/auth";
import { createClient } from "@/utils/supabase/server";

export default async function EditProfilePage() {
  const user = await requireUser();
  const supabase = await createClient();

  // Get user profile
  const { data: profile } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", user.id)
    .single();

  if (!profile) {
    // This shouldn't happen in normal flow, but handle it gracefully
    // Could redirect to a profile creation page or show an error
    redirect("/");
  }

  return (
    <div className="container py-10">
      <div className="mx-auto max-w-2xl">
        <h1 className="mb-6 text-3xl font-bold tracking-tight">Edit Profile</h1>
        <ProfileForm profile={profile} dashboardMode={false} />
      </div>
    </div>
  );
}
