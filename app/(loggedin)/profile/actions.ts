"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { Temporal } from "temporal-polyfill";

import { signOut } from "@/app/(auth)/actions";
import type {
  PricingModel,
  VenueType,
  WorkshopDailyAvailability,
  WorkshopFormat,
} from "@/types/types";
import { createAdminClient, createClient } from "@/utils/supabase/server";

type UpdateResult = {
  success?: boolean;
  error?: string;
};

export async function updateProfile(
  profileId: string,
  full_name: string,
  phone_number: string = "",
): Promise<UpdateResult> {
  // Validate profile ID
  if (!profileId) {
    return { error: "Profile ID is required" };
  }

  try {
    // Create Supabase server client
    const supabase = await createClient();

    // Update profile
    const { error: updateError } = await supabase
      .from("profiles")
      .update({
        full_name,
        phone_number,
        updated_at: Temporal.Now.instant().toString(),
      })
      .eq("id", profileId);

    if (updateError) {
      return { error: updateError.message };
    }

    return {
      success: true,
    };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "An error occurred while updating profile",
    };
  } finally {
    redirect("/profile");
  }
}

type WorkshopActionResult = {
  success?: boolean;
  error?: string;
  workshopId?: string;
};

/**
 * Server action to create or update a workshop
 */
export async function saveWorkshop(
  workshopId: string | null,
  providerId: string,
  name: string,
  description: string,
  format: WorkshopFormat,
  availability: WorkshopDailyAvailability,
  duration: string,
  location: string = "",
  venue_type: VenueType,
  prerequisites: string = "",
  price: number,
  pricing_model: PricingModel = "total",
  client_site_travel_fee: number = 0,
  currency: "AUD" | "IDR" | "SGD" | "MYR" = "SGD",
  min_capacity: number,
  max_capacity: number,
  group_discount_available: boolean = false,
  lead_time: string = "",
  image_url: string = "",
  published: boolean = true,
  categoryIds: string[] = [],
): Promise<WorkshopActionResult> {
  const isUpdate = !!workshopId;
  let result: WorkshopActionResult = { success: false };

  // Validate provider ID for new workshops
  if (!isUpdate && !providerId) {
    result = { error: "Provider ID is required" };
    return result;
  }

  try {
    // Create Supabase admin client to bypass RLS policies
    const supabase = await createAdminClient();

    if (isUpdate) {
      // Update existing workshop
      const { error: updateError } = await supabase
        .from("workshops")
        .update({
          name,
          description,
          format,
          availability,
          duration,
          location,
          venue_type,
          prerequisites,
          price,
          pricing_model,
          client_site_travel_fee,
          currency,
          min_capacity,
          max_capacity,
          group_discount_available,
          lead_time,
          image_url,
          published,
          updated_at: Temporal.Now.instant().toString(),
        })
        .eq("id", workshopId);

      if (updateError) {
        result = { error: updateError.message };
        return result;
      }

      // Update workshop categories
      // First, delete existing categories
      const { error: deleteError } = await supabase
        .from("workshop_categories")
        .delete()
        .eq("workshop_id", workshopId);

      if (deleteError) {
        result = { error: deleteError.message };
        return result;
      }

      // Then insert new categories
      if (categoryIds.length > 0) {
        const categoryInserts = categoryIds.map((categoryId) => ({
          workshop_id: workshopId,
          category_id: categoryId,
        }));

        const { error: insertCategoriesError } = await supabase
          .from("workshop_categories")
          .insert(categoryInserts);

        if (insertCategoriesError) {
          result = { error: insertCategoriesError.message };
          return result;
        }
      }

      // For workshop updates, set success with the existing ID
      result = {
        success: true,
        workshopId: workshopId,
      };
    } else {
      // Create new workshop
      const { data, error: insertError } = await supabase
        .from("workshops")
        .insert({
          name,
          description,
          format,
          availability,
          duration,
          location,
          venue_type,
          prerequisites,
          price,
          pricing_model,
          client_site_travel_fee,
          currency,
          min_capacity,
          max_capacity,
          group_discount_available,
          lead_time,
          image_url,
          published,
          provider_id: providerId,
        })
        .select("id")
        .single();

      if (insertError) {
        result = { error: insertError.message };
        return result;
      }

      // Insert workshop categories for the new workshop
      if (data && categoryIds.length > 0) {
        const categoryInserts = categoryIds.map((categoryId) => ({
          workshop_id: data.id,
          category_id: categoryId,
        }));

        const { error: insertCategoriesError } = await supabase
          .from("workshop_categories")
          .insert(categoryInserts);

        if (insertCategoriesError) {
          // If there's an error inserting categories, delete the workshop to avoid orphaned records
          await supabase.from("workshops").delete().eq("id", data.id);
          result = { error: insertCategoriesError.message };
          return result;
        }
      }

      // Get the new workshop ID
      if (data) {
        result = {
          success: true,
          workshopId: data.id,
        };
      }
    }

    revalidatePath("/dashboard");
  } catch (error) {
    result = {
      error:
        error instanceof Error
          ? error.message
          : "An error occurred while saving workshop",
    };
  } finally {
    if (result.success) {
      redirect("/dashboard");
    }
  }

  return result;
}

/**
 * Server action to delete a workshop
 */
export async function deleteWorkshop(
  workshopId: string,
): Promise<{ success: boolean; error?: string }> {
  try {
    // Create Supabase admin client to bypass RLS policies
    const supabase = await createAdminClient();

    const { error } = await supabase
      .from("workshops")
      .delete()
      .eq("id", workshopId);

    if (error) {
      return { success: false, error: error.message };
    }

    // Revalidate workshops page to reflect changes
    revalidatePath("/dashboard/workshops");
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "An error occurred while deleting workshop",
    };
  }
}

export async function deleteUserProfile(): Promise<{
  success?: boolean;
  error?: string;
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { error: "User not found" };
  }

  const adminSupabase = await createAdminClient();
  const { error: deleteError } = await adminSupabase.auth.admin.deleteUser(
    user.id,
  );

  if (deleteError) {
    return { error: deleteError.message };
  }

  await signOut();

  return { success: true };
}

export async function updateProviderOrganization(
  organizationId: string,
  name: string,
  description: string = "",
  city: string = "",
  country: string = "",
  location: string = "",
  profile_photo_url: string = "",
): Promise<UpdateResult> {
  // Validate organization ID
  if (!organizationId) {
    return { error: "Organization ID is required" };
  }

  try {
    // Create Supabase server client
    const supabase = await createClient();

    // Update provider organization
    const { error: updateError } = await supabase
      .from("provider_organizations")
      .update({
        name,
        description,
        city,
        country,
        location,
        profile_photo_url,
        updated_at: Temporal.Now.instant().toString(),
      })
      .eq("id", organizationId);

    if (updateError) {
      return { error: updateError.message };
    }

    return {
      success: true,
    };
  } catch (error) {
    return {
      error:
        error instanceof Error
          ? error.message
          : "An error occurred while updating organization details",
    };
  } finally {
    // Redirect to the profile page
    redirect("/dashboard");
  }
}
