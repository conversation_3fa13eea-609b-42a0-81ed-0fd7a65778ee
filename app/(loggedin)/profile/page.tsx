import { redirect } from "next/navigation";

import { ClientProfile } from "@/components/profile/client-profile";
import { requireUser } from "@/lib/auth";
import { createClient } from "@/utils/supabase/server";

export default async function ProfilePage() {
  const user = await requireUser();
  const supabase = await createClient();

  const { data: profile } = await supabase
    .from("profiles")
    .select("user_type")
    .eq("id", user.id)
    .single();

  if (!profile) {
    redirect("/profile/edit");
  }

  // Redirect providers to dashboard, keep clients on profile
  if (profile.user_type === "provider") {
    redirect("/dashboard");
  } else {
    return <ClientProfile userId={user.id} />;
  }
}
