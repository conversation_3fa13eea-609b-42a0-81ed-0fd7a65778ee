import { Calendar, Clock, MapPin, Users } from "lucide-react";
import type { Metadata } from "next";
import Image from "next/image";
import { notFound } from "next/navigation";
import Script from "next/script";

import { BackButton } from "@/components/back-button";
import { StartMessageButton } from "@/components/messages/start-message-button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { IconText } from "@/components/ui/icon-text";
import { getUser, getUserProfile } from "@/lib/auth";
import {
  formatPricingModel,
  formatSnakeCase,
  formatTimeSlots,
} from "@/lib/utils";
import type { Database } from "@/types/database.types";
import type { PricingModel } from "@/types/types";
import { createClient } from "@/utils/supabase/server";

// Define the workshop type with provider organization data
type WorkshopWithProvider = Database["public"]["Tables"]["workshops"]["Row"] & {
  provider_organizations:
    | Database["public"]["Tables"]["provider_organizations"]["Row"]
    | null;
};

interface WorkshopDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({
  params,
}: WorkshopDetailPageProps): Promise<Metadata> {
  const supabase = await createClient();
  const resolvedParams = await params;

  const { data: workshop } = await supabase
    .from("workshops")
    .select("name, description, price, provider_organizations!inner(name)")
    .eq("id", resolvedParams.id)
    .single();

  if (!workshop) {
    return {
      title: "Workshop Not Found",
    };
  }

  const providerName = workshop.provider_organizations?.name || "Provider";
  const title = `${workshop.name} by ${providerName}`;
  const description =
    workshop.description ||
    `Join this wellness workshop hosted by ${providerName}. ${workshop.name} - Book now on Pulse Space.`;

  return {
    title,
    description: description.slice(0, 160), // Limit to 160 chars for SEO
    openGraph: {
      title,
      description,
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
    },
    alternates: {
      canonical: `/workshops/${resolvedParams.id}`,
    },
  };
}

export default async function WorkshopDetailPage({
  params,
}: WorkshopDetailPageProps) {
  const supabase = await createClient();
  const user = await getUser();
  const resolvedParams = await params;
  // We don't need searchParams anymore since we removed the registration component

  // Fetch user profile if user is logged in - keeping this for future use
  if (user) {
    await getUserProfile(user.id);
  }

  const { data: workshop } = (await supabase
    .from("workshops")
    .select(
      "*, provider_id, provider_organizations!inner(id, name, profile_photo_url, city, country, description)",
    )
    .eq("id", resolvedParams.id)
    .single()) as { data: WorkshopWithProvider | null };

  if (!workshop) {
    notFound();
  }

  // Handle availability and duration from the new schema
  const formattedAvailability = formatTimeSlots(workshop.availability);

  // JSON-LD structured data for workshop/event
  const jsonLdEvent = {
    "@context": "https://schema.org",
    "@type": "EducationEvent",
    name: workshop.name,
    description: workshop.description,
    provider: {
      "@type": "Organization",
      name: workshop.provider_organizations?.name || "Provider",
      address:
        workshop.provider_organizations?.city &&
        workshop.provider_organizations?.country
          ? {
              "@type": "PostalAddress",
              addressLocality: workshop.provider_organizations.city,
              addressCountry: workshop.provider_organizations.country,
            }
          : undefined,
    },
    eventAttendanceMode:
      workshop.format === "online"
        ? "https://schema.org/OnlineEventAttendanceMode"
        : workshop.format === "hybrid"
          ? "https://schema.org/MixedEventAttendanceMode"
          : "https://schema.org/OfflineEventAttendanceMode",
    maximumAttendeeCapacity: workshop.max_capacity,
    offers: {
      "@type": "Offer",
      price: workshop.price,
      priceCurrency: workshop.currency || "USD",
      availability: workshop.published
        ? "https://schema.org/InStock"
        : "https://schema.org/SoldOut",
      validFrom: new Date().toISOString(),
    },
    organizer: {
      "@type": "Organization",
      name: workshop.provider_organizations?.name || "Provider",
    },
    isAccessibleForFree: false,
  };

  return (
    <>
      <Script id="jsonld-workshop" type="application/ld+json">
        {JSON.stringify(jsonLdEvent)}
      </Script>
      <div className="container py-10">
        <div className="grid grid-cols-1 gap-10 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <div className="mb-6">
              <div className="mb-4 flex items-center gap-2">
                <BackButton />
                <h1 className="text-2xl font-bold tracking-tight">
                  {workshop.name}
                </h1>
              </div>
              <div className="flex items-center">
                <Avatar className="mr-2 h-10 w-10">
                  <AvatarImage
                    src={workshop.provider_organizations?.profile_photo_url}
                    alt={
                      workshop.provider_organizations?.name || "Provider logo"
                    }
                    width={40}
                    height={40}
                  />
                  <AvatarFallback>
                    {workshop.provider_organizations?.name?.charAt(0) || "P"}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-base font-medium">
                    Hosted by{" "}
                    {workshop.provider_organizations?.name ||
                      "Anonymous Provider"}
                  </p>
                </div>
              </div>
            </div>

            {workshop.image_url && (
              <div className="relative mb-8 aspect-video overflow-hidden rounded-lg">
                <Image
                  src={workshop.image_url}
                  alt={workshop.name}
                  className="object-cover"
                  fill
                  sizes="(max-width: 768px) 100vw, 800px"
                  priority
                />
              </div>
            )}

            <div className="space-y-8">
              <div>
                <h2 className="mb-4 text-lg font-semibold">
                  About this workshop
                </h2>
                <div className="prose max-w-none">
                  <p className="whitespace-pre-line">{workshop.description}</p>
                </div>
              </div>

              <div>
                <h2 className="mb-4 text-lg font-semibold">
                  About {workshop.provider_organizations?.name || "Provider"}
                </h2>
                <div className="prose max-w-none">
                  <p className="whitespace-pre-line">
                    {workshop.provider_organizations?.description ||
                      "No provider description available."}
                  </p>
                </div>
              </div>

              {workshop.prerequisites && (
                <div>
                  <h2 className="mb-4 text-lg font-semibold">Prerequisites</h2>
                  <div className="prose max-w-none">
                    <p className="whitespace-pre-line">
                      {workshop.prerequisites}
                    </p>
                  </div>
                </div>
              )}

              <div>
                <h2 className="mb-4 text-lg font-semibold">Venue Options</h2>
                <p className="mb-4 text-base font-medium text-muted-foreground">
                  Workshop format:{" "}
                  <span className="text-foreground">
                    {formatSnakeCase(workshop.format)}
                  </span>
                </p>
                <IconText
                  icon={MapPin}
                  text={
                    <div>
                      <p className="font-medium">
                        {workshop.venue_type === "provider_location"
                          ? "At Provider's Location"
                          : workshop.venue_type === "client_location"
                            ? "At Your Office"
                            : workshop.venue_type === "online"
                              ? "Virtual Workshop"
                              : workshop.venue_type || "Flexible Location"}
                      </p>
                      <p className="text-muted-foreground">
                        {workshop.location || "Location details to be arranged"}
                      </p>
                      {workshop.venue_type === "client_location" && (
                        <p className="mt-2 text-base text-muted-foreground">
                          This provider can come to your workplace for a team
                          session
                        </p>
                      )}
                    </div>
                  }
                  iconSize="lg"
                  align="start"
                  className="[&>svg]:text-muted-foreground"
                />
              </div>
            </div>
          </div>

          <div className="lg:col-span-1">
            <div className="sticky top-20">
              <div className="overflow-hidden rounded-lg border bg-card text-card-foreground shadow-sm">
                <div className="p-6">
                  <div className="mb-4 flex items-baseline justify-between">
                    <h3 className="text-lg font-bold">
                      {new Intl.NumberFormat("en-AU", {
                        style: "currency",
                        currency: workshop.currency || "AUD",
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 2,
                      }).format(Number(workshop.price || 0))}
                      <span className="ml-1 text-base text-muted-foreground">
                        {formatPricingModel(
                          workshop.pricing_model as PricingModel | null,
                        ).toLowerCase()}
                      </span>
                    </h3>
                  </div>

                  <div className="space-y-4">
                    <IconText
                      icon={Calendar}
                      text={
                        <div>
                          <p className="font-medium">Availability</p>
                          <p className="text-muted-foreground">
                            {formattedAvailability}
                          </p>
                        </div>
                      }
                      iconSize="lg"
                      align="start"
                      className="[&>svg]:text-muted-foreground"
                    />

                    <IconText
                      icon={Clock}
                      text={
                        <div>
                          <p className="font-medium">Duration</p>
                          <p className="text-muted-foreground">
                            {workshop.duration || "Flexible duration"}
                          </p>
                        </div>
                      }
                      iconSize="lg"
                      align="start"
                      className="[&>svg]:text-muted-foreground"
                    />

                    <IconText
                      icon={Users}
                      text={
                        <div>
                          <p className="font-medium">Capacity</p>
                          <p className="text-muted-foreground">
                            {workshop.min_capacity && workshop.max_capacity
                              ? workshop.min_capacity === workshop.max_capacity
                                ? `${workshop.min_capacity} participants`
                                : `${workshop.min_capacity} to ${workshop.max_capacity} participants`
                              : workshop.max_capacity
                                ? `Up to ${workshop.max_capacity} participants`
                                : "Flexible capacity"}
                          </p>
                        </div>
                      }
                      iconSize="lg"
                      align="start"
                      className="[&>svg]:text-muted-foreground"
                    />
                  </div>

                  <StartMessageButton
                    workshopId={workshop.id}
                    currentUserId={user?.id}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
