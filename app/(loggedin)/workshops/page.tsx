import type { Metadata } from "next";

import { Section } from "@/components/ui/section";
import { WorkshopCard } from "@/components/workshops/workshop-card";
import { WorkshopFilters } from "@/components/workshops/workshop-filters";
import { workshopSearchSchema } from "@/lib/schemas";
import { parseSearchParams } from "@/lib/utils";
import { createClient } from "@/utils/supabase/server";

export const revalidate = 60; // Revalidate every minute

export const metadata: Metadata = {
  title: "Browse Wellness Workshops",
  description:
    "Explore our comprehensive collection of corporate wellness workshops. Find yoga, meditation, nutrition, stress management, and team building activities for your organization.",
  keywords: [
    "browse wellness workshops",
    "corporate workshop catalog",
    "wellness program directory",
    "find wellness providers",
    "book corporate workshops",
    "wellness workshop marketplace",
  ],
  openGraph: {
    title: "Browse Wellness Workshops | Pulse Space",
    description:
      "Explore our comprehensive collection of corporate wellness workshops. Find the perfect wellness activities for your team.",
  },
  alternates: {
    canonical: "/workshops",
  },
};

export default async function WorkshopsPage({
  searchParams,
}: {
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const supabase = await createClient();
  const resolvedParams = await searchParams;

  // Parse search params with Zod validation
  const validParams = parseSearchParams(resolvedParams, workshopSearchSchema);
  const { type: workshopType, category, date, page } = validParams || {};
  const pageSize = 9;

  // Fetch categories that have at least one published workshop
  const { data: workshopCategories } = await supabase
    .from("workshop_categories")
    .select("category_id, workshops!inner(published)")
    .eq("workshops.published", true);

  // Fetch category details for categories with workshops
  const uniqueCategoryIds = [
    ...new Set(workshopCategories?.map((wc) => wc.category_id) || []),
  ];

  const { data: categories } = await supabase
    .from("categories")
    .select("id, name")
    .in("id", uniqueCategoryIds)
    .order("name");

  // Build query
  let query = supabase
    .from("workshops")
    .select(
      `*,
      provider_organizations!inner(
        id,
        name,
        profile_photo_url,
        city
      )`,
      {
        count: "exact",
      },
    )
    .eq("published", true); // Only show published workshops

  // Apply filters
  if (workshopType) {
    const types = Array.isArray(workshopType) ? workshopType : [workshopType];
    if (types.length > 0) {
      query = query.in("format", types);
    }
  }

  if (category) {
    const categories = Array.isArray(category) ? category : [category];
    if (categories.length > 0) {
      // Get workshop IDs that have any of the selected categories
      const { data: workshopsWithCategories } = await supabase
        .from("workshop_categories")
        .select("workshop_id")
        .in("category_id", categories);

      const workshopIds =
        workshopsWithCategories?.map((wc) => wc.workshop_id) || [];

      query = query.in(
        "id",
        workshopIds.length > 0 ? workshopIds.map(String) : ["-1"],
      );
    }
  }

  // Pagination
  const from = (page - 1) * pageSize;
  const to = from + pageSize - 1;

  // Execute query
  const { data: workshops, count } = await query
    .order("created_at", { ascending: false })
    .range(from, to);

  const totalPages = count ? Math.ceil(count / pageSize) : 0;

  // Helper function to build URL params for pagination
  const buildPaginationParams = (pageNum: number) => {
    const params = new URLSearchParams();

    if (workshopType) {
      const types = Array.isArray(workshopType) ? workshopType : [workshopType];
      types.forEach((t) => params.append("type", t));
    }

    if (category) {
      const categories = Array.isArray(category) ? category : [category];
      categories.forEach((c) => params.append("category", c));
    }

    if (date) {
      params.set("date", date);
    }

    params.set("page", pageNum.toString());
    return params.toString();
  };

  return (
    <div className="container py-10">
      <Section spacing="loose" className="flex flex-col">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Corporate Wellness Workshops
          </h1>
          <p className="mt-2 text-muted-foreground">
            Browse and book wellness programs for your organisation and
            employees
          </p>
        </div>

        {/* Mobile filters appear above content */}
        <div className="lg:hidden">
          <WorkshopFilters
            searchParams={resolvedParams}
            categories={categories || []}
            mode="mobile"
          />
        </div>

        {/* Desktop: Use flexbox for dynamic width adjustment */}
        <div className="flex gap-6">
          {/* Desktop filters appear in sidebar */}
          <div className="hidden lg:block">
            <WorkshopFilters
              searchParams={resolvedParams}
              categories={categories || []}
              mode="desktop"
            />
          </div>

          <div className="flex-1">
            {workshops && workshops.length > 0 ? (
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {workshops.map((workshop) => (
                  <WorkshopCard
                    key={workshop.id}
                    workshop={workshop}
                    href={`/workshops/${workshop.id}`}
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-10 text-center">
                <h3 className="text-lg font-semibold">No workshops found</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  Try adjusting your filters or check back later for new
                  workshops.
                </p>
              </div>
            )}

            {totalPages > 1 && (
              <div className="mt-8 flex justify-center">
                <nav className="flex items-center space-x-2">
                  {Array.from({ length: totalPages }).map((_, i) => (
                    <a
                      key={i}
                      href={`/workshops?${buildPaginationParams(i + 1)}`}
                      className={`inline-flex h-9 w-9 items-center justify-center rounded-md text-sm font-medium ${
                        page === i + 1
                          ? "bg-primary text-primary-foreground"
                          : "border border-input bg-background hover:bg-accent hover:text-accent-foreground"
                      }`}
                    >
                      {i + 1}
                    </a>
                  ))}
                </nav>
              </div>
            )}
          </div>
        </div>
      </Section>
    </div>
  );
}
