import type { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://pulsespace.co";

  return {
    rules: [
      {
        userAgent: "*",
        allow: ["/"],
        disallow: [
          "/dashboard",
          "/profile",
          "/messages",
          "/api",
          "/_next",
          "/login",
          "/signup",
          "/forgot-password",
          "/reset-password",
          "/*?*", // Disallow URL parameters to prevent duplicate content
        ],
      },
      {
        userAgent: "Googlebot",
        allow: ["/"],
        disallow: [
          "/dashboard",
          "/profile",
          "/messages",
          "/api",
          "/_next",
          "/login",
          "/signup",
          "/forgot-password",
          "/reset-password",
        ],
        crawlDelay: 1,
      },
    ],
    host: baseUrl,
  };
}
