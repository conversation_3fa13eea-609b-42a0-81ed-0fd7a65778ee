import { AlertTriangle, MessageSquare } from "lucide-react";

import { getUserMessageList } from "@/app/enquiries/actions";
import { MessagesSidebar } from "@/components/messages/messages-sidebar";
import { Button } from "@/components/ui/button";
import { getUserProfile, requireUser } from "@/lib/auth";
import { isProvider } from "@/lib/user-helpers";

export const dynamic = "force-dynamic"; // Ensure fresh data on each request

export default async function MessagesPage() {
  const user = await requireUser(); // Ensure user is authenticated
  const profile = await getUserProfile(user.id);
  const isProviderUser = profile ? isProvider(profile) : false;

  const { chatList: messageList, error } = await getUserMessageList();

  if (error) {
    console.error("Error fetching chat rooms:", error);
  }

  return (
    <>
      {/* Sidebar - full width on mobile, 1/4 width on desktop */}
      <div className="w-full border-r md:w-80">
        <MessagesSidebar chatRooms={messageList} isProvider={isProviderUser} />
      </div>

      {/* Main area - hidden on mobile, 3/4 on desktop */}
      <div className="hidden flex-1 bg-muted/40 p-6 text-center md:flex md:items-center md:justify-center">
        {error ? (
          <div className="max-w-md">
            <AlertTriangle className="mx-auto mb-4 h-12 w-12 text-destructive" />
            <h2 className="mb-2 text-xl font-semibold text-destructive">
              Loading Error
            </h2>
            <p className="mb-4 text-muted-foreground">
              Could not load your conversations. Please try again later.
            </p>
            {/* Optional: could add a retry button here if applicable */}
          </div>
        ) : messageList && messageList.length > 0 ? (
          <div className="max-w-md">
            <MessageSquare className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
            <h2 className="mb-2 text-xl font-semibold">
              Select a conversation
            </h2>
            <p className="mb-4 text-muted-foreground">
              Choose a conversation from the sidebar to start messaging
            </p>
          </div>
        ) : (
          <div className="max-w-md">
            <MessageSquare className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
            <h2 className="mb-2 text-xl font-semibold">No conversations yet</h2>
            {isProviderUser ? (
              <>
                <p className="mb-4 text-balance text-muted-foreground">
                  List your workshops and wait for clients to enquire to message
                  them
                </p>
                <Button asChild>
                  <a href="/dashboard/workshops/new">List Workshops</a>
                </Button>
              </>
            ) : (
              <>
                <p className="mb-4 text-balance text-muted-foreground">
                  Start a conversation with a workshop provider to message them
                </p>
                <Button asChild>
                  <a href="/workshops">Browse Workshops</a>
                </Button>
              </>
            )}
          </div>
        )}
      </div>
    </>
  );
}
