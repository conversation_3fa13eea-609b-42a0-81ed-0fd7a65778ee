import { ChevronLeft } from "lucide-react";
import { Suspense } from "react";

import type {
  MessageRoom,
  SimpleMessageRoomListItem,
} from "@/app/enquiries/actions";
import {
  getChatMessages,
  getChatRoomDetails,
  getUserMessageList,
} from "@/app/enquiries/actions";
import { MessagesSidebar } from "@/components/messages/messages-sidebar";
import { RealtimeChat } from "@/components/realtime-chat";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { IconText } from "@/components/ui/icon-text";
import { getUserProfile, requireUser } from "@/lib/auth";
import { isClient } from "@/lib/user-helpers";
import type { ChatMessage } from "@/types/chat";

export const dynamic = "force-dynamic"; // Ensure fresh data on each request

type MessagesPageProps = {
  params: Promise<{
    roomId: string;
  }>;
};

/**
 * Renders the chat interface for a specific messaging room, including the sidebar, header, and real-time chat area.
 *
 * Fetches chat room details, messages, and the user's chat room list based on the provided room ID. Ensures the user is authenticated before displaying the chat UI. If the room or messages cannot be loaded, displays an error message.
 *
 * @param params - Contains the `roomId` identifying the chat room to display.
 * @returns The chat page UI for the specified room, or an error message if loading fails.
 */
export default async function MessagesPage({ params }: MessagesPageProps) {
  const { roomId } = await params;
  const user = await requireUser();
  const userProfile = await getUserProfile(user.id);

  const [
    { room, error: roomError },
    { messages, error: messagesError },
    { chatList: messageList },
  ] = (await Promise.all([
    getChatRoomDetails(roomId),
    getChatMessages(roomId),
    getUserMessageList(),
  ])) as [
    { room: MessageRoom | null; error: string | null },
    { messages: ChatMessage[] | null; error: string | null },
    { chatList: SimpleMessageRoomListItem[] },
  ];

  if (roomError || !room || messagesError) {
    return (
      <div className="p-4 text-red-500">
        Error loading chat room:{" "}
        {roomError || messagesError || "Room not found."}
      </div>
    );
  }

  const otherParticipant = getOtherParticipant(room, user.id);
  const isUserClient = user.id === room.client_id;

  const workshopTitle = room.workshop.name;

  return (
    <>
      <div className="hidden border-r md:block md:w-80">
        <MessagesSidebar chatRooms={messageList} isProvider={!isUserClient} />
      </div>

      {/* Main chat area - full width on mobile, 3/4 on desktop */}
      <div className="flex min-h-0 flex-1 flex-col bg-muted/40">
        <header className="sticky top-0 z-10 border-b bg-background p-4">
          <div className="flex items-center gap-3">
            <a href="/enquiries" className="md:hidden">
              <Button variant="ghost" size="icon" className="mr-1">
                <IconText
                  icon={ChevronLeft}
                  text={<span className="sr-only">Back to messages list</span>}
                  iconSize="lg"
                  spacing="tight"
                />
              </Button>
            </a>
            {otherParticipant.avatar && (
              <Avatar className="h-10 w-10 border">
                <AvatarImage
                  src={otherParticipant.avatar}
                  alt={otherParticipant.name}
                  width={40}
                  height={40}
                />
                <AvatarFallback>
                  {otherParticipant.name.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
            )}
            <div>
              <h1 className="text-xl font-semibold">{otherParticipant.name}</h1>
              <p className="text-sm text-muted-foreground">{workshopTitle}</p>
            </div>
          </div>
        </header>

        <Suspense fallback={<div className="p-4">Loading chat...</div>}>
          <RealtimeChat
            roomId={roomId}
            workshopId={room.workshop.id}
            workshopName={room.workshop.name}
            workshopCurrency={room.workshop.currency}
            organizationName={room.provider_organization?.name || "Provider"}
            currentUserId={userProfile!.id}
            messages={messages || []}
            isClient={isClient(userProfile!)}
          />
        </Suspense>
      </div>
    </>
  );
}

/**
 * Returns the name and avatar of the other participant in a chat room relative to the current user.
 *
 * If the current user is the client, returns the provider organization's name and avatar. Otherwise, returns the client's full name and an empty avatar string.
 *
 * @param room - The chat room object containing participant details
 * @param currentUserId - The ID of the current user
 * @returns An object with `name` and `avatar` properties for the other participant
 */
function getOtherParticipant(room: MessageRoom, currentUserId: string) {
  const isUserClient = currentUserId === room.client_id;

  if (isUserClient) {
    return {
      name: room.provider_organization?.name ?? "Provider",
      avatar: room.provider_organization?.profile_photo_url ?? "",
    };
  } else {
    return {
      name: room.client?.full_name ?? "Client",
      avatar: "", // Clients don't have avatars
    };
  }
}
