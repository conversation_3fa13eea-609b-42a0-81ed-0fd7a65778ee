"use server";

import {
  createEmbeddedCheckoutSession,
  retrieveCheckoutSession,
} from "@/lib/stripe-client";
import { createAdminClient, createClient } from "@/utils/supabase/server";

/**
 * Creates a Stripe Checkout Session for a quote
 */
export async function createPaymentIntent(quoteId: string) {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { success: false, error: "Authentication required" };
  }

  // Get the quote with workshop details
  const { data: quote, error: quoteError } = await supabase
    .from("quotes")
    .select(
      `
      *,
      workshops (
        name,
        provider_id
      )
    `,
    )
    .eq("id", quoteId)
    .eq("client_id", user.id)
    .single();

  if (quoteError || !quote) {
    return { success: false, error: "Quote not found" };
  }

  // Check if quote already has a payment intent
  if (quote.status === "payment_intent_created" && quote.payment_intent_id) {
    // Retrieve existing Stripe session
    try {
      const session = await retrieveCheckoutSession(quote.payment_intent_id);
      if (session.client_secret) {
        return {
          success: true,
          clientSecret: session.client_secret,
        };
      }
    } catch (error) {
      console.error("Error retrieving existing session:", error);
      // Fall through to create new session
    }
  }

  // Verify the quote is in a state that can accept payment
  if (quote.status !== "pending" && quote.status !== "payment_intent_created") {
    return {
      success: false,
      error: `Quote cannot be paid because it is ${quote.status}`,
    };
  }

  try {
    // Create Stripe Checkout Session
    const session = await createEmbeddedCheckoutSession({
      amount: quote.price,
      currency: quote.currency,
      quoteId: quote.id,
      workshopName: quote.workshops?.name || "Workshop",
      returnUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/enquiries/payment-success?session_id={CHECKOUT_SESSION_ID}`,
      customerEmail: user.email || undefined,
    });

    // Update quote with payment intent ID and status
    const { error: updateError } = await supabase.rpc(
      "create_payment_intent_for_quote",
      {
        p_quote_id: quoteId,
        p_payment_intent_id: session.id,
      },
    );

    if (updateError) {
      console.error("Error updating quote:", updateError);
      return { success: false, error: "Failed to update quote status" };
    }

    return {
      success: true,
      clientSecret: session.client_secret,
    };
  } catch (error) {
    console.error("Error creating payment intent:", error);
    return { success: false, error: "Failed to create payment session" };
  }
}

/**
 * Submits payment for a quote (updates status to payment_processing)
 */
export async function submitPayment(quoteId: string) {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return { success: false, error: "Authentication required" };
  }

  try {
    // Update quote status to payment_processing
    const { error: updateError } = await supabase.rpc(
      "submit_payment_for_quote",
      {
        p_quote_id: quoteId,
      },
    );

    if (updateError) {
      console.error("Error submitting payment:", updateError);
      return { success: false, error: "Failed to submit payment" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error submitting payment:", error);
    return { success: false, error: "Failed to submit payment" };
  }
}

/**
 * Handles successful payment completion from Stripe return URL
 */
export async function handlePaymentSuccess(sessionId: string) {
  const supabase = await createAdminClient();

  try {
    // Retrieve the Stripe session
    const session = await retrieveCheckoutSession(sessionId);

    if (session.payment_status !== "paid") {
      return { success: false, error: "Payment not completed" };
    }

    const quoteId = session.metadata?.quote_id;

    if (!quoteId) {
      return { success: false, error: "Quote ID not found in session" };
    }

    // Get the quote with workshop details to get provider_id
    const { data: quote, error: quoteError } = await supabase
      .from("quotes")
      .select(
        `
        *,
        workshops (
          provider_id
        )
      `,
      )
      .eq("id", quoteId)
      .single();

    if (quoteError || !quote) {
      return { success: false, error: "Quote not found" };
    }

    // Calculate payment breakdown
    const serviceFee = parseFloat((quote.price * 0.2).toFixed(2)); // 20% platform fee
    const providerEarnings = quote.price - serviceFee;

    // Update quote to paid status
    const { error: updateError } = await supabase
      .from("quotes")
      .update({
        status: "paid",
        paid_at: new Date().toISOString(),
        provider_earnings: providerEarnings,
        fee: serviceFee,
      })
      .eq("id", quoteId);

    if (updateError) {
      console.error("Error updating quote to paid:", updateError);
      return { success: false, error: "Failed to update quote status" };
    }

    // Create ledger entries for provider earnings and platform fee
    const providerId = quote.workshops?.provider_id;
    if (providerId) {
      const { error: ledgerError } = await supabase
        .from("provider_ledger")
        .insert([
          {
            provider_id: providerId,
            transaction_type: "payment",
            amount: providerEarnings,
            currency: quote.currency,
            quote_id: quoteId,
            description: "Payment received",
          },
          {
            provider_id: providerId,
            transaction_type: "fee",
            amount: -serviceFee,
            currency: quote.currency,
            quote_id: quoteId,
            description: "Platform fee",
          },
        ]);

      if (ledgerError) {
        console.error("Error creating ledger entries:", ledgerError);
        // Don't fail the payment for ledger errors, just log them
      }
    }

    // Add a system message to the chat room
    const { error: messageError } = await supabase
      .from("chat_messages")
      .insert({
        room_id: quote.chat_room_id,
        sender_id: null, // NULL sender_id indicates a system message
        content: "Payment completed successfully.",
      });

    if (messageError) {
      console.error("Error adding system message:", messageError);
      // Don't fail for message errors
    }

    return { success: true, quote };
  } catch (error) {
    console.error("Error handling payment success:", error);
    return { success: false, error: "Failed to process payment completion" };
  }
}
