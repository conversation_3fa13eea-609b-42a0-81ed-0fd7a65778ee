// app/enquiries/actions.ts
"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { getUserProfile, requireUser } from "@/lib/auth";
import {
  sendChatNotificationAsync,
  sendQuoteNotificationAsync,
} from "@/lib/message-notifications";
import { isProvider } from "@/lib/user-helpers";
import { type ChatMessage } from "@/types/chat";
import type { ChatRoomWithParticipants } from "@/types/chat-room";
import type { QuoteWithWorkshopDetails } from "@/types/quote";
import { createClient } from "@/utils/supabase/server";

// Re-export for backward compatibility
export type MessageRoom = ChatRoomWithParticipants;

// This is a simplified version used in the message list view
export type SimpleMessageRoomListItem = {
  id: string;
  updated_at: string;
  workshop_title: string | null;
  other_participant_name: string | null;
  other_participant_avatar: string | null;
  last_message_preview?: string | null; // Optional: Add later if needed
  has_unread: boolean;
};

// Function to get or create a chat room for a specific workshop
export async function getOrCreateChatRoom(
  workshopId: string,
): Promise<{ roomId: string | null; error?: string }> {
  const supabase = await createClient(); // Use the utility function
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { roomId: null, error: "User not authenticated" };
  }

  try {
    // 1. Fetch workshop details to get the provider organization ID
    const { data: workshop, error: workshopError } = await supabase
      .from("workshops")
      .select("provider_id") // Correct column name in workshops table
      .eq("id", workshopId)
      .single();

    if (workshopError || !workshop) {
      console.error("Error fetching workshop:", workshopError);
      return {
        roomId: null,
        error: "Workshop not found or error fetching details.",
      };
    }

    const providerOrganizationId = workshop.provider_id;
    const clientId = user.id;

    // Check if client is part of the provider org (though RLS prevents self-chat implicitly)
    // This check might be complex depending on your structure, potentially omit if RLS is sufficient

    // 2. Check if a chat room already exists
    const { data: existingRoom, error: existingRoomError } = await supabase
      .from("chat_rooms")
      .select("id")
      .eq("workshop_id", workshopId)
      .eq("client_id", clientId)
      .maybeSingle(); // Use maybeSingle as it might not exist

    if (existingRoomError && existingRoomError.code !== "PGRST116") {
      // Ignore 'PGRST116' (No rows found)
      console.error(
        "Error checking for existing chat room:",
        existingRoomError,
      );
      return {
        roomId: null,
        error: "Error checking for existing chat room.",
      };
    }

    if (existingRoom) {
      return { roomId: existingRoom.id }; // Return existing room ID
    }

    // 3. Create a new chat room if it doesn't exist
    const { data: newRoom, error: insertError } = await supabase
      .from("chat_rooms")
      .insert({
        workshop_id: workshopId,
        client_id: clientId,
        provider_organization_id: providerOrganizationId,
      })
      .select("id")
      .single();

    if (insertError || !newRoom) {
      console.error("Error creating chat room:", insertError);
      return { roomId: null, error: "Failed to create chat room." };
    }

    return { roomId: newRoom.id };
  } catch (error) {
    console.error("Unexpected error in getOrCreateChatRoom:", error);
    return { roomId: null, error: "An unexpected error occurred." };
  }
}

// Function to fetch messages for a specific chat room
export async function getChatMessages(
  roomId: string,
): Promise<{ messages: ChatMessage[] | null; error?: string }> {
  const supabase = await createClient();

  try {
    // 1. Fetch base chat messages
    const { data: baseMessages, error: baseMessagesError } = await supabase
      .from("chat_messages")
      .select("id, room_id, sender_id, content, created_at, quote_id")
      .eq("room_id", roomId)
      .order("created_at", { ascending: true });

    if (baseMessagesError) {
      console.error("Error fetching base messages:", baseMessagesError);
      return { messages: null, error: "Failed to fetch base messages." };
    }

    if (!baseMessages || baseMessages.length === 0) {
      return { messages: [] };
    }

    // 2. Collect unique IDs
    const senderIds = [
      ...new Set(baseMessages.map((msg) => msg.sender_id).filter(Boolean)),
    ] as string[];
    const quoteIds = [
      ...new Set(baseMessages.map((msg) => msg.quote_id).filter(Boolean)),
    ] as string[];

    // 3. Fetch related data
    const profilesMap = new Map<
      string,
      { id: string; full_name: string | null }
    >();
    if (senderIds.length > 0) {
      const { data: profilesData, error: profilesError } = await supabase
        .from("profiles")
        .select("id, full_name")
        .in("id", senderIds);
      if (profilesError) {
        console.warn("Error fetching profiles:", profilesError);
      } else {
        profilesData?.forEach((p) => profilesMap.set(p.id, p));
      }
    }

    const quotesMap = new Map<string, QuoteWithWorkshopDetails>();
    if (quoteIds.length > 0) {
      const { data: quotesData, error: quotesError } = await supabase
        .from("quotes")
        .select(
          `
          id, workshop_id, chat_room_id, provider_organization_id, client_id,
          proposed_datetime, location, price, currency, notes, status,
          created_at, updated_at,
          fee, paid_at, payment_intent_id, provider_earnings,
          workshops ( name ),
          provider_organizations!provider_organization_id ( name )
        `,
        )
        .in("id", quoteIds);

      if (quotesError) {
        console.warn("Error fetching quotes:", quotesError);
      } else if (quotesData) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        quotesData.forEach((q_raw: any) => {
          // Treat as any initially
          const workshopName = Array.isArray(q_raw.workshops)
            ? q_raw.workshops[0]?.name
            : q_raw.workshops?.name;

          const organizationName = Array.isArray(q_raw.provider_organizations)
            ? q_raw.provider_organizations[0]?.name
            : q_raw.provider_organizations?.name;

          const quoteTyped: QuoteWithWorkshopDetails = {
            id: q_raw.id,
            workshop_id: q_raw.workshop_id,
            chat_room_id: q_raw.chat_room_id,
            provider_organization_id: q_raw.provider_organization_id,
            client_id: q_raw.client_id,
            proposed_datetime: q_raw.proposed_datetime,
            location: q_raw.location,
            price: q_raw.price,
            currency: q_raw.currency,
            notes: q_raw.notes,
            status: q_raw.status,
            created_at: q_raw.created_at,
            updated_at: q_raw.updated_at,
            expires_at: q_raw.expires_at,
            fee: q_raw.fee,
            paid_at: q_raw.paid_at,
            payment_intent_id: q_raw.payment_intent_id,
            provider_earnings: q_raw.provider_earnings,
            workshops: workshopName ? { name: workshopName as string } : null, // Ensure name is string if workshopName exists
            provider_organizations: {
              name: organizationName || "Unknown Provider",
            },
          };
          quotesMap.set(quoteTyped.id, quoteTyped);
        });
      }
    }

    // 4. Combine data
    const messages: ChatMessage[] = baseMessages.map((msg) => {
      const profile = msg.sender_id ? profilesMap.get(msg.sender_id) : null;
      const quote = msg.quote_id ? quotesMap.get(msg.quote_id) : null;

      return {
        id: msg.id.toString(),
        content: msg.content,
        user: {
          id: profile?.id || msg.sender_id || "system",
          name:
            profile?.full_name ?? (msg.sender_id ? "Unknown User" : "System"),
        },
        createdAt: msg.created_at,
        roomId: msg.room_id,
        quotes: quote || null,
        // quote_id will be part of the quotes object if needed, not directly on ChatMessage
      };
    });

    return { messages };
  } catch (error) {
    console.error("Unexpected error in getChatMessages:", error);
    // Ensure the return type matches Promise<{ messages: ChatMessage[] | null; error?: string }>
    return { messages: null, error: "An unexpected error occurred." };
  }
}

// Function to store a new message
export async function storeChatMessage(
  roomId: string,
  content: string,
): Promise<{ message: ChatMessage | null; error?: string }> {
  const supabase = await createClient();
  const user = await requireUser();

  if (!content.trim()) {
    return { message: null, error: "Message content cannot be empty." };
  }

  try {
    // Fetch sender's full name for the ChatMessage object
    const profile = await getUserProfile(user.id);
    const senderName = profile?.full_name ?? "Unknown User";

    const { data: newMessageData, error: insertError } = await supabase
      .from("chat_messages")
      .insert({
        room_id: roomId,
        sender_id: user.id,
        content: content.trim(),
      })
      .select("id, created_at") // Select ID and created_at of the new message
      .single();

    if (insertError || !newMessageData) {
      console.error("Error inserting message:", insertError);
      return { message: null, error: "Failed to store message." };
    }

    // Mark the room as read for the sender (they just sent a message, so they've "read" it)
    await supabase.from("chat_room_read_status").upsert(
      {
        room_id: roomId,
        user_id: user.id,
        last_read_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      { onConflict: "room_id,user_id" },
    );

    // Construct the ChatMessage object to return
    const message: ChatMessage = {
      id: newMessageData.id.toString(),
      content: content.trim(),
      user: {
        id: user.id, // Populate user.id
        name: senderName,
      },
      createdAt: newMessageData.created_at,
      // userId is now part of user object, so remove top-level userId
      roomId: roomId,
      // quote_id will be set if a quote is being created with this message
      // For a plain message, quote_id will be null/undefined
    };
    revalidatePath(`/enquiries/${roomId}`);

    // Send email notification asynchronously (don't await to avoid blocking)
    sendChatNotificationAsync(roomId, user.id, senderName, content.trim());

    return { message };
  } catch (error) {
    console.error("Unexpected error in storeChatMessage:", error);
    return { message: null, error: "An unexpected error occurred." };
  }
}

/**
 * Retrieves the chat room list for the current user, selecting either provider or client chats based on user role.
 *
 * Returns an empty list with an error message if the user profile cannot be fetched or if an error occurs.
 *
 * @returns An object containing the chat list and an optional error message.
 */
export async function getUserMessageList(): Promise<{
  chatList: SimpleMessageRoomListItem[];
  error?: string;
}> {
  const user = await requireUser();
  try {
    const profile = await getUserProfile(user.id);
    if (!profile) {
      return { chatList: [], error: "Failed to fetch user profile." };
    }
    return await (isProvider(profile)
      ? getProviderMessageList(user.id)
      : getClientMessageList(user.id));
  } catch (error) {
    console.error("Error processing chat list based on user role:", error);
    const errorMessage =
      error instanceof Error
        ? error.message
        : "Failed to retrieve chat list due to an unexpected error.";
    return { chatList: [], error: errorMessage };
  }
}

// Function to get client chats
async function getClientMessageList(userId: string): Promise<{
  chatList: SimpleMessageRoomListItem[];
  error?: string;
}> {
  const supabase = await createClient();

  try {
    // Use the efficient function that includes unread status
    const { data: clientChatsData, error: clientChatsError } =
      await supabase.rpc("chat_room_details_with_unread", {
        p_user_id: userId,
      });

    if (clientChatsError) {
      console.error("Error fetching client chat rooms:", clientChatsError);
      return { chatList: [], error: "Failed to fetch client chat list." };
    }

    // If no client chats, return empty array
    if (!clientChatsData) {
      return { chatList: [] };
    }

    // Filter to only client rooms and format the data
    const chatList: SimpleMessageRoomListItem[] = clientChatsData
      .filter((room) => room.client_id === userId)
      .sort(
        (a, b) =>
          new Date(b.room_updated_at).getTime() -
          new Date(a.room_updated_at).getTime(),
      )
      .map((room) => ({
        id: room.room_id,
        updated_at: room.room_updated_at,
        workshop_title: room.workshop_name ?? "Unknown Workshop",
        other_participant_name: room.provider_organization_name ?? "Provider",
        other_participant_avatar: room.provider_organization_profile_photo_url,
        has_unread: room.has_unread,
      }));

    return { chatList };
  } catch (error) {
    console.error("Unexpected error in getClientMessageList:", error);
    return { chatList: [], error: "An unexpected error occurred." };
  }
}

async function getProviderMessageList(userId: string): Promise<{
  chatList: SimpleMessageRoomListItem[];
  error?: string;
}> {
  const supabase = await createClient();

  try {
    // Use the efficient function that includes unread status
    const { data: providerChatsData, error: providerChatsError } =
      await supabase.rpc("chat_room_details_with_unread", {
        p_user_id: userId,
      });

    if (providerChatsError) {
      console.error("Error fetching provider chat rooms:", providerChatsError);
      return { chatList: [], error: "Could not fetch provider details." };
    }

    // If no provider chats, return empty array
    if (!providerChatsData) {
      return { chatList: [] };
    }

    // Filter to only provider rooms and format the data
    const chatList: SimpleMessageRoomListItem[] = providerChatsData
      .filter((room) => room.client_id !== userId) // Provider rooms are where user is NOT the client
      .sort(
        (a, b) =>
          new Date(b.room_updated_at).getTime() -
          new Date(a.room_updated_at).getTime(),
      )
      .map((room) => ({
        id: room.room_id,
        updated_at: room.room_updated_at,
        workshop_title: room.workshop_name ?? "Unknown Workshop",
        other_participant_name: room.client_full_name ?? "Client",
        other_participant_avatar: null,
        has_unread: room.has_unread,
      }));

    return { chatList };
  } catch (error) {
    console.error("Unexpected error in getProviderMessageList:", error);
    return { chatList: [], error: "An unexpected error occurred." };
  }
}

// Function to fetch details needed for a specific chat room page
/**
 * Retrieves detailed information about a chat room, including workshop, client, and provider organization data.
 *
 * @param roomId - The unique identifier of the chat room
 * @returns An object containing the chat room details as `room`, or `null` and an error message if not found or access is denied
 */
export async function getChatRoomDetails(
  roomId: string,
): Promise<{ room: MessageRoom | null; error?: string }> {
  const supabase = await createClient(); // Use the utility function
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { room: null, error: "User not authenticated" };
  }

  try {
    // Fetch the specific chat room from the view
    const { data: roomData, error } = await supabase
      .from("chat_room_details_view") // Query the view
      .select(
        `
                room_id, room_created_at, room_updated_at,
                workshop_id, workshop_name, workshop_currency,
                client_id, client_full_name,
                provider_organization_id, provider_organization_name,
                provider_organization_profile_photo_url
            `,
      ) // Select columns from the view
      .eq("room_id", roomId) // Use room_id as the view's id column is named room_id
      .maybeSingle();

    if (error) {
      console.error(`Error fetching chat room details for ${roomId}:`, error);
      return { room: null, error: "Failed to fetch chat room details." };
    }

    if (!roomData) {
      return { room: null, error: "Chat room not found or access denied." };
    }

    // Validate required fields from the view
    if (
      !roomData.room_id ||
      !roomData.room_created_at ||
      !roomData.room_updated_at ||
      !roomData.workshop_id ||
      !roomData.workshop_name ||
      !roomData.workshop_currency ||
      !roomData.client_id ||
      !roomData.provider_organization_id
    ) {
      return { room: null, error: "Invalid room data from database view." };
    }

    // Cast to ChatRoom type - The view structure will differ from the original table query
    // Adjust ChatRoom type or mapping here as needed.
    // For now, structure the data to fit the existing ChatRoom type as best as possible.
    const formattedRoomData = {
      id: roomData.room_id,
      created_at: roomData.room_created_at,
      updated_at: roomData.room_updated_at,
      workshop_id: roomData.workshop_id,
      client_id: roomData.client_id,
      provider_organization_id: roomData.provider_organization_id,
      // Nested objects based on your ChatRoom type definition:
      workshop: {
        id: roomData.workshop_id,
        name: roomData.workshop_name,
        currency: roomData.workshop_currency,
      },
      client: roomData.client_id
        ? {
            id: roomData.client_id,
            full_name: roomData.client_full_name,
          }
        : null,
      provider_organization:
        roomData.provider_organization_id && roomData.provider_organization_name
          ? {
              id: roomData.provider_organization_id,
              name: roomData.provider_organization_name,
              profile_photo_url:
                roomData.provider_organization_profile_photo_url,
            }
          : null,
      // Ensure all required fields for ChatRoom are present
    } satisfies MessageRoom;

    return { room: formattedRoomData as MessageRoom };
  } catch (error) {
    console.error(
      `Unexpected error in getChatRoomDetails for ${roomId}:`,
      error,
    );
    return { room: null, error: "An unexpected error occurred." };
  }
}

const CreateQuoteSchema = z.object({
  chatRoomId: z.string().uuid({ message: "Invalid chat room ID" }),
  workshopId: z.string().uuid({ message: "Invalid workshop ID" }),
  proposedDatetime: z.string().datetime({ offset: true }),
  location: z.string().min(1, { message: "Location cannot be empty" }),
  price: z.number().positive({ message: "Price must be a positive number" }),
  currency: z
    .string()
    .length(3, { message: "Currency must be a 3-letter code" })
    .toUpperCase(),
  notes: z.string().optional(),
});

export type CreateQuoteInput = z.infer<typeof CreateQuoteSchema>;

/**
 * Creates a new quote for a workshop within a chat room, ensuring data integrity and provider authorization.
 *
 * Validates the input, verifies the user is an authenticated provider, and checks that the chat room and workshop belong to the same provider organization. Marks any existing pending quotes in the chat room as superceded, then inserts a new pending quote and an associated chat message. Updates relevant cache paths for UI consistency.
 *
 * @param input - The quote creation details, including chat room, workshop, proposed date/time, location, price, currency, and optional notes.
 * @returns An object indicating success or failure, and on success, the IDs of the created quote and chat message.
 */
export async function createQuote(input: CreateQuoteInput): Promise<{
  success: boolean;
  quoteId?: string;
  messageId?: string;
  error?: string;
}> {
  const supabase = await createClient();
  const user = await requireUser();
  const senderId = user.id;

  const validatedFields = CreateQuoteSchema.safeParse(input);
  if (!validatedFields.success) {
    return {
      success: false,
      error: validatedFields.error.errors
        .map((e) => `${e.path.join(".")}: ${e.message}`)
        .join("; "),
    };
  }
  const {
    chatRoomId,
    workshopId,
    proposedDatetime,
    location,
    price,
    currency,
    notes,
  } = validatedFields.data;

  const userProfile = await getUserProfile(user.id);

  if (!userProfile) {
    return { success: false, error: "User profile not found." };
  }
  if (!isProvider(userProfile)) {
    return { success: false, error: "User is not a valid provider." };
  }

  const { data: provider } = await supabase
    .from("providers")
    .select("*, provider_organizations(name)")
    .eq("id", user.id)
    .single();

  if (!provider || !provider.organization_id) {
    return {
      success: false,
      error: "Provider details or organization ID is missing.",
    };
  }

  const { data: workshopData, error: workshopError } = await supabase
    .from("workshops")
    .select("provider_id, name")
    .eq("id", workshopId)
    .single();

  if (workshopError || !workshopData) {
    console.error("Error fetching workshop for quote:", workshopError);
    return {
      success: false,
      error: "Workshop not found or error fetching details.",
    };
  }
  const workshopOrgId = workshopData.provider_id;

  // Fetch chat room to get client_id and verify its provider_organization_id against workshop's
  const { data: chatRoom, error: chatRoomError } = await supabase
    .from("chat_rooms")
    .select("client_id, provider_organization_id")
    .eq("id", chatRoomId)
    .single();

  if (chatRoomError || !chatRoom) {
    console.error("Error fetching chat room for quote:", chatRoomError);
    return {
      success: false,
      error: "Chat room not found or error fetching details.",
    };
  }

  // This check is important for data integrity: ensures the chat room is for the same provider org as the workshop.
  if (chatRoom.provider_organization_id !== workshopOrgId) {
    return {
      success: false,
      error: "Chat room organization does not match workshop organization.",
    };
  }
  const clientId = chatRoom.client_id;

  // Mark all other pending quotes in this chat room as superceded
  const { error: updateError } = await supabase
    .from("quotes")
    .update({ status: "superceded" })
    .eq("chat_room_id", chatRoomId)
    .eq("status", "pending");

  if (updateError) {
    console.error("Error updating existing pending quotes:", updateError);
    // Continue with creating the new quote even if updating fails
  }

  // 1. Insert into quotes table
  const { data: newQuote, error: quoteError } = await supabase
    .from("quotes")
    .insert({
      workshop_id: workshopId,
      chat_room_id: chatRoomId,
      provider_organization_id: workshopOrgId,
      client_id: clientId,
      proposed_datetime: proposedDatetime,
      location,
      price,
      currency,
      notes,
      status: "pending",
    })
    .select("id")
    .single();

  if (quoteError || !newQuote) {
    console.error("Error creating quote:", quoteError);
    return {
      success: false,
      error: `Failed to create quote: ${quoteError?.message || "Unknown error"}`,
    };
  }

  // 2. Insert into chat_messages table
  const { data: newMessage, error: messageError } = await supabase
    .from("chat_messages")
    .insert({
      room_id: chatRoomId,
      sender_id: senderId,
      content: "",
      quote_id: newQuote.id,
    })
    .select("id")
    .single();

  if (messageError || !newMessage) {
    console.error("Error creating chat message for quote:", messageError);
    return {
      success: false,
      error: `Failed to create chat message. Quote ${newQuote.id} created but not sent. Error: ${messageError?.message || "Unknown error"}`,
    };
  }

  // Mark the room as read for the sender (they just sent a quote, so they've "read" it)
  await supabase.from("chat_room_read_status").upsert(
    {
      room_id: chatRoomId,
      user_id: senderId,
      last_read_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    { onConflict: "room_id,user_id" },
  );

  // Get sender's name for the email notification
  const senderProfile = await getUserProfile(senderId);
  const senderName = senderProfile?.full_name ?? "Provider";

  const workshopName = workshopData.name || "Workshop";
  const providerOrgName = Array.isArray(provider.provider_organizations)
    ? provider.provider_organizations[0]?.name || "Provider"
    : provider.provider_organizations?.name || "Provider";

  // Send email notification asynchronously (don't await to avoid blocking)
  sendQuoteNotificationAsync(chatRoomId, senderId, senderName, {
    proposed_datetime: proposedDatetime,
    location,
    price,
    currency,
    notes: notes || null,
    workshops: { name: workshopName },
    provider_organizations: { name: providerOrgName },
  });

  revalidatePath(`/enquiries/${chatRoomId}`);
  revalidatePath("/enquiries");

  return { success: true, quoteId: newQuote.id, messageId: newMessage.id };
}

/**
 * Accept a quote and update its status to 'paid'
 */
/**
 * Mark a chat room as read for the current user
 */
export async function markRoomAsRead(roomId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const user = await requireUser();
    const supabase = await createClient();

    // Upsert read status for this user and room
    const { error } = await supabase.from("chat_room_read_status").upsert(
      {
        room_id: roomId,
        user_id: user.id,
        last_read_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      { onConflict: "room_id,user_id" },
    );

    if (error) {
      console.error("Error marking room as read:", error);
      return { success: false, error: "Failed to mark room as read" };
    }

    // Revalidate the enquiries list to update unread counts
    revalidatePath("/enquiries");

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in markRoomAsRead:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

export async function acceptQuote(quoteId: string): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const user = await requireUser();
    const supabase = await createClient();

    // Get the quote to verify it belongs to this user
    const { data: quote, error: quoteError } = await supabase
      .from("quotes")
      .select("id, client_id, chat_room_id, status")
      .eq("id", quoteId)
      .single();

    if (quoteError) {
      console.error("Error fetching quote:", quoteError);
      return { success: false, error: "Quote not found" };
    }

    // Verify the user is the client for this quote
    if (quote.client_id !== user.id) {
      return {
        success: false,
        error: "You are not authorized to accept this quote",
      };
    }

    // Verify the quote is in a state that can be accepted
    if (quote.status !== "pending") {
      return {
        success: false,
        error: `Quote cannot be accepted because it is ${quote.status}`,
      };
    }

    // Update the quote status
    const { error: updateError } = await supabase
      .from("quotes")
      .update({ status: "paid" })
      .eq("id", quoteId);

    if (updateError) {
      console.error("Error updating quote:", updateError);
      return { success: false, error: "Failed to update quote status" };
    }

    // Add a system message to the chat room (using NULL sender_id to denote system message)
    const { error: messageError } = await supabase
      .from("chat_messages")
      .insert({
        room_id: quote.chat_room_id,
        sender_id: null, // NULL sender_id indicates a system message
        content: "Quote accepted.",
      });

    if (messageError) {
      console.error("Error adding system message:", messageError);
      // Don't fail the whole operation if just the message fails
    }

    // Revalidate paths to update UI
    revalidatePath(`/enquiries/${quote.chat_room_id}`);
    revalidatePath("/enquiries");

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in acceptQuote:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}
