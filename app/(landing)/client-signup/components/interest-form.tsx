"use client";

import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { submitInterestForm } from "../actions";

export function InterestForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  async function handleSubmit(formData: FormData) {
    setIsSubmitting(true);
    try {
      await submitInterestForm(formData);
      setIsSubmitted(true);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isSubmitted) {
    return (
      <div className="text-center">
        <h4 className="mb-2 text-lg font-semibold text-green-700">
          Thank you for your interest!
        </h4>
        <p className="text-gray-600">
          We&apos;ll notify you as soon as client signups are available.
        </p>
      </div>
    );
  }

  return (
    <form action={handleSubmit} className="space-y-4">
      <Input
        name="name"
        placeholder="Your Name"
        required
        className="h-12 rounded-lg border-gray-300"
      />
      <Input
        name="email"
        type="email"
        placeholder="Work Email"
        required
        className="h-12 rounded-lg border-gray-300"
      />
      <Button
        type="submit"
        size="lg"
        className="w-full"
        disabled={isSubmitting}
      >
        {isSubmitting ? "Registering..." : "Register Interest"}
      </Button>
    </form>
  );
}
