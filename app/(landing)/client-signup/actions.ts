"use server";

import nodemailer from "nodemailer";

import { sanitizeEmail, sanitizeInput } from "@/lib/input-sanitization";

// Email transporter configuration (using existing pattern from lib/message-notifications.ts)
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || "smtp.forwardemail.net",
  port: Number(process.env.EMAIL_PORT) || 465,
  secure: true,
  auth: {
    user: process.env.EMAIL_USERNAME || "",
    pass: process.env.EMAIL_PASSWORD || "",
  },
});

export async function submitInterestForm(formData: FormData) {
  const name = formData.get("name") as string;
  const email = formData.get("email") as string;

  if (!name || !email) {
    throw new Error("Required fields are missing");
  }

  // Sanitize inputs before using them
  const sanitizedName = sanitizeInput(name);
  const sanitizedEmail = sanitizeEmail(email);

  const emailContent = `
    New Client Signup Interest Registration

    Name: ${sanitizedName}
    Work Email: ${sanitizedEmail}

    Submitted at: ${new Date().toLocaleString()}
  `;

  try {
    await transporter.sendMail({
      from: process.env.EMAIL_USERNAME || "<EMAIL>",
      to: "<EMAIL>",
      subject: `New Client Interest Registration from ${sanitizedName}`,
      text: emailContent,
      html: emailContent.replace(/\n/g, "<br>"),
    });

    console.log("Interest form submitted successfully");
  } catch (error) {
    console.error("Error sending email:", error);
    throw new Error("Failed to submit form");
  }
}
