import type { Metadata } from "next";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { InterestForm } from "./components/interest-form";

export const metadata: Metadata = {
  title: "Pulse Space – Launching Soon",
  description:
    "Join as an early bird to claim a surprise bonus discount and be the first to know when we launch",
};

export default function ClientSignupInterestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 px-6 py-16">
      <div className="mx-auto max-w-2xl">
        <div className="mb-8 text-center">
          <h1 className="mb-4 text-4xl font-bold text-gray-900">
            Launching soon
          </h1>
          <p className="text-xl text-gray-600">
            Join as an early bird to claim a surprise bonus discount and be the
            first to know when we launch.
          </p>
        </div>

        <Card className="mx-auto w-full max-w-md">
          <CardHeader>
            <CardTitle>Register Your Interest</CardTitle>
            <CardDescription>
              The leading place for corporate health and wellness events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <InterestForm />
          </CardContent>
        </Card>

        <div className="mt-8 text-center">
          <p className="text-gray-500">
            Already have an account?{" "}
            <a
              href="/login?type=client"
              className="text-primary hover:underline"
            >
              Log in
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
