import type { Metadata } from "next";

import { getRandomWorkshops } from "./actions";
import { HeroSection } from "./components/hero-section";
import { HowItWorksSection } from "./components/how-it-works-section";
import { LeadGenerationSection } from "./components/lead-generation-section";
import { WhyChooseSection } from "./components/why-choose-section";

export const metadata: Metadata = {
  title: "Pulse Space - Transform Your Workplace Wellness Events",
  description:
    "The all-in-one platform for companies to discover, book and manage unique health & wellness events that boost employee engagement and wellbeing.",
  keywords: [
    "corporate wellness events",
    "employee wellness programs",
    "workplace wellness workshops",
    "health and wellness events",
    "corporate health programs",
    "employee engagement activities",
    "workplace wellbeing solutions",
    "corporate wellness platform",
  ],
  openGraph: {
    title: "Pulse Space - Transform Your Workplace Wellness Events",
    description:
      "Discover and book unique wellness events for your team. Get instant quotes from vetted providers.",
  },
};

export default async function LandingClientPage() {
  const workshops = await getRandomWorkshops(8);

  return (
    <div className="min-h-screen">
      <HeroSection workshops={workshops} />
      <WhyChooseSection />
      <HowItWorksSection />
      <LeadGenerationSection />
    </div>
  );
}
