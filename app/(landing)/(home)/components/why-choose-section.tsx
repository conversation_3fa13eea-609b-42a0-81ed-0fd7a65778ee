import { CardGrid } from "@/components/ui/card-grid";
import { FeatureCard } from "@/components/ui/feature-card";

export function WhyChooseSection() {
  return (
    <section className="bg-white px-6 py-20 sm:px-6 sm:py-24 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="mb-16 text-center">
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl lg:text-5xl">
            Why HR Teams Choose Pulse Space
          </h2>
          <p className="mx-auto max-w-2xl text-xl font-light text-gray-600">
            Stop juggling multiple vendors and streamline your wellness
          </p>
        </div>

        <CardGrid>
          <FeatureCard
            title="Instant Quotes"
            description="Get pricing from multiple vetted providers instantly. Manage comparison and quotes all in one place."
            icon="/landing-client/instant-quotes-icon.svg"
            bgColor="bg-[#FFEAE2]"
          />
          <FeatureCard
            title="Unique Event Ideas"
            description="Discover creative wellness activities that keep employees engaged from art to sound bath to talks and more."
            icon="/landing-client/unique-events-icon.svg"
            bgColor="bg-[#F9FFEF]"
          />
          <FeatureCard
            title="Vetted Providers"
            description="Every provider is thoroughly screened for quality. Book with confidence."
            icon="/landing-client/vetted-providers-icon.svg"
            bgColor="bg-[#FFF9EE]"
          />
          <FeatureCard
            title="All-in-One Platform"
            description="From booking to coordinating and feedback collection. Save hours of work through our streamlined process."
            icon="/landing-client/all-in-one-icon.svg"
            bgColor="bg-[#FFEAE2]"
          />
        </CardGrid>
      </div>
    </section>
  );
}
