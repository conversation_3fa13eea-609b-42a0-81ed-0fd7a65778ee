import Image from "next/image";

import { CardGrid } from "@/components/ui/card-grid";

const steps = [
  {
    number: "01",
    title: "Browse events",
    description:
      "Explore our curated selection of wellness workshops and activities tailored for your workplace.",
    icon: "/landing-client/step-1-icon.svg",
    bgColor: "bg-primary",
  },
  {
    number: "02",
    title: "Get quote and book",
    description:
      "Request quotes from multiple providers and book your preferred session with just a few clicks.",
    icon: "/landing-client/step-2-icon.svg",
    bgColor: "bg-primary",
  },
  {
    number: "03",
    title: "Have the session",
    description:
      "Enjoy your wellness event while we handle all the coordination and logistics for you.",
    icon: "/landing-client/step-3-icon.svg",
    bgColor: "bg-primary",
  },
  {
    number: "04",
    title: "Collect feedback",
    description:
      "Gather valuable insights from participants to measure impact and plan future events.",
    icon: "/landing-client/step-4-icon.svg",
    bgColor: "bg-primary",
  },
];

export function HowItWorksSection() {
  return (
    <section className="bg-[#F9FAFB] px-6 py-20 sm:px-6 sm:py-24 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="mb-16 text-center">
          <h2 className="mb-4 text-pretty text-3xl font-bold text-gray-900 sm:text-4xl lg:text-5xl">
            How It Works
          </h2>
          <p className="mx-auto max-w-3xl text-pretty text-xl leading-relaxed text-gray-600">
            From bookings to feedback, we&apos;ve streamlined the entire process
            to save you time and deliver better results.
          </p>
        </div>

        <CardGrid>
          {steps.map((step, index) => (
            <div
              key={index}
              className="relative rounded-xl border border-gray-100 bg-white p-8 shadow-sm"
            >
              <div className="flex items-start gap-4">
                {/* Number circle - left aligned */}
                <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-gradient-to-br from-orange-400 to-orange-500 shadow-md">
                  <span className="text-lg font-bold text-white">
                    {step.number}
                  </span>
                </div>

                {/* Icon - left aligned next to number */}
                <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-orange-50">
                  <Image
                    src={step.icon}
                    alt=""
                    width={24}
                    height={24}
                    className="h-6 w-6"
                  />
                </div>
              </div>

              <div className="mt-6">
                <h3 className="mb-4 text-pretty text-xl font-bold text-gray-900">
                  {step.title}
                </h3>
                <p className="text-pretty leading-relaxed text-gray-600">
                  {step.description}
                </p>
              </div>
            </div>
          ))}
        </CardGrid>
      </div>
    </section>
  );
}
