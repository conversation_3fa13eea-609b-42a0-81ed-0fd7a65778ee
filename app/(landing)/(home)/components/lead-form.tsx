"use client";

import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { submitLeadForm } from "../actions";

export function LeadForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  async function handleSubmit(formData: FormData) {
    setIsSubmitting(true);
    try {
      await submitLeadForm(formData);
      setIsSubmitted(true);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isSubmitted) {
    return (
      <div className="text-center">
        <h4 className="mb-2 text-lg font-semibold text-green-700">
          Thank you!
        </h4>
        <p className="text-gray-600">
          We&apos;ll send your free wellness event calendar soon.
        </p>
      </div>
    );
  }

  return (
    <form action={handleSubmit} className="space-y-4">
      <Input
        name="name"
        placeholder="Name"
        required
        className="h-12 rounded-lg border-gray-300"
      />
      <Input
        name="email"
        type="email"
        placeholder="Work Email"
        required
        className="h-12 rounded-lg border-gray-300"
      />
      <Input
        name="company"
        placeholder="Company name"
        required
        className="h-12 rounded-lg border-gray-300"
      />
      <Input
        name="wellnessTopics"
        placeholder="Wellness topic focus areas"
        className="h-12 rounded-lg border-gray-300"
      />
      <Button
        type="submit"
        size="lg"
        className="w-full"
        disabled={isSubmitting}
      >
        {isSubmitting ? "Sending..." : "Get My Free Calendar"}
      </Button>
    </form>
  );
}
