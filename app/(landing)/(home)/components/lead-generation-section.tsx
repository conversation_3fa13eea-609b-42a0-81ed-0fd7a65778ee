import Image from "next/image";

import { LeadForm } from "./lead-form";

const benefits = [
  "Monthly themed wellness events",
  "Topic focused recommendations",
  "Seasonal activity suggestions",
];

export function LeadGenerationSection() {
  return (
    <section className="bg-gradient-to-r from-[#F0FDF4] to-[#EFF6FF] py-16 sm:px-6 sm:py-20 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="overflow-hidden rounded-2xl bg-gradient-to-r from-[#F0FDF4] to-[#EFF6FF] p-6 sm:p-8 lg:p-12">
          <div className="grid gap-12 lg:grid-cols-2">
            {/* Left Column - Content */}
            <div>
              <h2 className="mb-6 text-2xl font-bold text-gray-900 sm:text-3xl">
                <span className="block text-primary">Free Event Calendar</span>
                <span className="block">Personalised To Your Workplace</span>
              </h2>
              <p className="mb-8 text-lg text-gray-600">
                Join our mailing list and receive a free 12-month wellness event
                calendar designed specifically for your organisation, budget,
                and wellness goals.
              </p>
              <ul className="space-y-3">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <Image
                      src="/landing-client/check-icon.svg"
                      alt=""
                      width={20}
                      height={20}
                      className="h-5 w-5 flex-shrink-0"
                    />
                    <span className="text-gray-600">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Right Column - Form */}
            <div className="rounded-xl bg-white p-6 shadow-lg sm:p-8">
              <h3 className="mb-6 text-xl font-bold text-gray-900">
                Get Your Free Wellness Event Calendar
              </h3>
              <LeadForm />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
