"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { WorkshopCard } from "@/components/workshops/workshop-card";
import type { WorkshopWithProvider } from "@/types/workshop";

interface WorkshopCarouselProps {
  workshops: WorkshopWithProvider[];
}

export function WorkshopCarousel({ workshops }: WorkshopCarouselProps) {
  if (!workshops.length) {
    return (
      <div className="flex items-center justify-center py-16 text-gray-500">
        No workshops available at the moment.
      </div>
    );
  }

  return (
    <Carousel
      opts={{
        align: "start",
        loop: false,
      }}
      className="w-full"
    >
      <CarouselContent className="-ml-2 md:-ml-4">
        {workshops.map((workshop) => (
          <CarouselItem
            key={workshop.id}
            className="basis-2/3 pl-2 sm:basis-1/2 md:pl-4 lg:basis-1/3 xl:basis-1/4"
          >
            <WorkshopCard
              workshop={workshop}
              href={`/workshops/${workshop.id}`}
            />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious className="left-0" />
      <CarouselNext className="right-0" />
    </Carousel>
  );
}
