import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import type { WorkshopWithProvider } from "@/types/workshop";

import { WorkshopCarousel } from "./workshop-carousel";

interface HeroSectionProps {
  workshops: WorkshopWithProvider[];
}

export function HeroSection({ workshops }: HeroSectionProps) {
  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-[#EFF6FF] via-white to-[#F0FDF4] px-6 pb-24 pt-32 sm:px-6 sm:pb-32 sm:pt-40 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Hero Text Content - Full Width */}
        <div className="text-center">
          <h1 className="mx-auto mb-6 max-w-4xl text-balance text-4xl font-bold leading-tight text-gray-900 sm:text-5xl lg:text-6xl">
            Transform Your Next Workplace Wellness Event
          </h1>
          <p className="text-l mx-auto mb-12 max-w-4xl text-balance font-light leading-relaxed text-gray-600 sm:text-xl">
            The all-in-one platform for companies to discover, book and manage
            unique health & wellness events that boost employee engagement and
            wellbeing
          </p>
        </div>

        {/* Browse Events Section */}
        <div className="mt-20">
          <h2 className="mb-12 text-pretty text-center text-3xl font-bold text-gray-900 sm:text-4xl">
            Browse Events
          </h2>
          <div className="mx-auto max-w-6xl">
            <WorkshopCarousel workshops={workshops} />
          </div>
        </div>

        {/* CTA Button */}
        <div className="mt-12 flex justify-center">
          <Button
            size="lg"
            asChild
            className="px-8 py-3 text-lg shadow-lg shadow-black/10"
          >
            <Link href="/workshops">View all workshops</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
