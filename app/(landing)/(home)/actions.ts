"use server";

import nodemailer from "nodemailer";

import { sanitizeEmail, sanitizeInput } from "@/lib/input-sanitization";
import type { WorkshopWithProvider } from "@/types/workshop";
import { createClient } from "@/utils/supabase/server";

export async function getRandomWorkshops(
  limit: number = 8,
): Promise<WorkshopWithProvider[]> {
  const supabase = await createClient();

  const { data: workshops } = await supabase
    .from("workshops")
    .select(
      `*,
      provider_organizations!inner(
        id,
        name,
        profile_photo_url,
        city
      )`,
    )
    .eq("published", true)
    .order("created_at", { ascending: false })
    .limit(limit * 2); // Get more than needed to have variety

  if (!workshops || workshops.length === 0) {
    return [];
  }

  // <PERSON>-Yates shuffle the workshops to get random selection
  const shuffled = [...workshops];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled.slice(0, limit);
}

// Email transporter configuration (using existing pattern from lib/message-notifications.ts)
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || "smtp.forwardemail.net",
  port: Number(process.env.EMAIL_PORT) || 465,
  secure: true,
  auth: {
    user: process.env.EMAIL_USERNAME || "",
    pass: process.env.EMAIL_PASSWORD || "",
  },
});

export async function submitLeadForm(formData: FormData) {
  const name = formData.get("name") as string;
  const email = formData.get("email") as string;
  const company = formData.get("company") as string;
  const wellnessTopics = formData.get("wellnessTopics") as string;

  if (!name || !email || !company) {
    throw new Error("Required fields are missing");
  }

  // Sanitize inputs before using them
  const sanitizedName = sanitizeInput(name);
  const sanitizedEmail = sanitizeEmail(email);
  const sanitizedCompany = sanitizeInput(company);
  const sanitizedWellnessTopics = sanitizeInput(wellnessTopics || "");

  const emailContent = `
    New Wellness Event Calendar Request

    Name: ${sanitizedName}
    Email: ${sanitizedEmail}
    Company: ${sanitizedCompany}
    Wellness Topic Focus Areas: ${sanitizedWellnessTopics || "Not specified"}

    Submitted at: ${new Date().toLocaleString()}
  `;

  try {
    await transporter.sendMail({
      from: process.env.EMAIL_USERNAME || "<EMAIL>",
      to: "<EMAIL>",
      subject: `New Calendar Request from ${sanitizedCompany}`,
      text: emailContent,
      html: emailContent.replace(/\n/g, "<br>"),
    });

    console.log("Lead form submitted successfully");
  } catch (error) {
    console.error("Error sending email:", error);
    throw new Error("Failed to submit form");
  }
}
