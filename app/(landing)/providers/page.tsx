import type { <PERSON>ada<PERSON> } from "next";

import { FAQSection } from "./components/faq-section";
import { HeroSection } from "./components/hero-section";
import { HowItWorksSection } from "./components/how-it-works-section";

export const metadata: Metadata = {
  title: "Pulse Space - Grow Your Wellness Business on Auto-Pilot",
  description:
    "Join Pulse Space and get discovered by more corporates looking for wellness workshops. List your services for free and grow your business with qualified leads.",
  keywords: [
    "wellness provider platform",
    "corporate wellness marketplace",
    "wellness business growth",
    "yoga instructor platform",
    "wellness workshop provider",
    "corporate wellness services",
    "health and wellness provider",
    "wellness business leads",
  ],
  openGraph: {
    title: "Pulse Space - Grow Your Wellness Business on Auto-Pilot",
    description:
      "Get discovered for free by more corporates looking for workshops just like yours. Join our wellness provider platform today.",
  },
};

export default function LandingProviderPage() {
  return (
    <div className="min-h-screen">
      <HeroSection />
      <HowItWorksSection />
      <FAQSection />
    </div>
  );
}
