import Link from "next/link";

import { Button } from "@/components/ui/button";

export function HeroSection() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-[#EFF6FF] via-white to-[#F0FDF4] px-6 py-32 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="text-center">
          <h1 className="mx-auto mb-6 max-w-4xl text-balance text-4xl font-bold leading-tight text-gray-900 sm:text-5xl lg:text-6xl">
            Become A Corporate Partner
          </h1>
          <p className="mx-auto mb-12 max-w-4xl text-pretty text-xl font-light text-gray-600 sm:text-xl sm:leading-8">
            List your workshops for free in seconds, get bookings on auto-pilot.
            <br />
            Connect with hundreds of corporates seeking wellness events on Pulse
            Space.
          </p>
        </div>

        {/* CTA Section */}
        <div className="mt-12 flex justify-center">
          <Button
            size="lg"
            asChild
            className="bg-[#ff5918] px-8 py-3 text-lg shadow-lg shadow-black/10 hover:bg-[#ff5252]"
          >
            <Link href="/signup?type=provider">List your event</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
