import { CardGrid } from "@/components/ui/card-grid";
import { FeatureCard } from "@/components/ui/feature-card";

export function HowItWorksSection() {
  return (
    <section className="px-6 py-20 sm:px-6 sm:py-24 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="mb-16 text-center">
          <h2 className="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl lg:text-5xl">
            How It Works
          </h2>
          <p className="mx-auto max-w-3xl text-xl font-light text-gray-600">
            Get started in four simple steps and start growing your corporate
            bookings today
          </p>
        </div>

        <CardGrid>
          <FeatureCard
            title="List your workshop"
            description="Sign up and build your expert profile. Highlight your credentials, specialties, and what makes you unique."
            icon="/icons/card1-icon.svg"
            bgColor="bg-orange-100"
          />
          <FeatureCard
            title="Get Bookings"
            description="Receive enquiries to your workshops. Finalise bookings with personalised quotes. Coordinate event easily."
            icon="/icons/card2-icon.svg"
            bgColor="bg-green-100"
          />
          <FeatureCard
            title="Easy coordination"
            description="Receive enquiries to your workshops. Finalise bookings with personalised quotes. Coordinate event easily."
            icon="/icons/card3-icon.svg"
            bgColor="bg-yellow-100"
          />
          <FeatureCard
            title="Grow & Scale"
            description="Scale your impact and revenue on auto-pilot. Use our insights to optimize your offerings."
            icon="/icons/card4-icon.svg"
            bgColor="bg-orange-100"
          />
        </CardGrid>
      </div>
    </section>
  );
}
