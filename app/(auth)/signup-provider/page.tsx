import { redirect } from "next/navigation";

import { validateInviteCode } from "@/app/(auth)/validate-invite";
import { ProviderSignupForm } from "@/components/auth/provider-signup-form";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { getUser } from "@/lib/auth";

interface SignupProviderPageProps {
  searchParams: Promise<{
    code?: string;
  }>;
}

export default async function SignupProviderPage({
  searchParams,
}: SignupProviderPageProps) {
  const user = await getUser();

  if (user) {
    redirect("/dashboard");
  }

  const inviteCode = (await searchParams).code || "";

  const isValid = await validateInviteCode(inviteCode);

  if (!isValid) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Invalid Invite Code</CardTitle>
          <CardDescription>
            The invite code provided is not valid.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>Please check your invite code and try again.</p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <a href="/signup" className="underline">
            Try another code
          </a>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Create a provider account</CardTitle>
        <CardDescription>
          Sign up to start hosting wellness events on Pulse Space for free
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ProviderSignupForm />
      </CardContent>
      <CardFooter className="flex justify-center">
        <a href="/login">Already have an account? Log in</a>
      </CardFooter>
    </Card>
  );
}
