"use client";

import { useTransition } from "react";

import { signOut } from "@/app/(auth)/actions";
import { LoadingButton } from "@/components/ui/loading-button";

export function LogoutButton() {
  const [isPending, startTransition] = useTransition();

  const handleLogout = () => {
    startTransition(async () => {
      await signOut(false); // Client user, not provider
    });
  };

  return (
    <LoadingButton
      onClick={handleLogout}
      loading={isPending}
      loadingText="Logging out..."
      className="w-full"
    >
      Log Out
    </LoadingButton>
  );
}
