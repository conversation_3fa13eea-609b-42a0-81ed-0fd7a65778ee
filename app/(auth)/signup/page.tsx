import { redirect } from "next/navigation";

import { InviteCodeForm } from "@/components/auth/invite-code-form";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { getUser, getUserProfile } from "@/lib/auth";

// Inline client component for logout functionality
import { LogoutButton } from "./logout-button";

interface SignupPageProps {
  searchParams: Promise<{
    type?: string;
  }>;
}

export default async function SignupPage({ searchParams }: SignupPageProps) {
  const user = await getUser();
  const resolvedSearchParams = await searchParams;
  const signupType = resolvedSearchParams.type;

  // If user is logged in, check their user type and handle accordingly
  if (user) {
    const profile = await getUserProfile(user.id);

    // If user has a profile, check their type
    if (profile) {
      // Redirect providers and admins to their respective dashboards
      if (profile.user_type === "provider") {
        redirect("/dashboard");
      } else if (profile.user_type === "admin") {
        redirect("/admin/dashboard");
      }

      // For clients trying to access provider signup, show a message
      if (profile.user_type === "client" && signupType === "provider") {
        return (
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Already Logged In</CardTitle>
              <CardDescription>
                You&apos;re currently logged in as a client. To create a
                provider account, please log out first.
              </CardDescription>
            </CardHeader>
            <CardFooter className="flex flex-col space-y-2">
              <LogoutButton />
              <a
                href="/profile"
                className="text-sm text-muted-foreground hover:underline"
              >
                Go to your profile
              </a>
            </CardFooter>
          </Card>
        );
      }

      // For other cases (client not accessing provider signup), redirect to their appropriate page
      if (profile.user_type === "client") {
        redirect("/profile");
      } else {
        redirect("/dashboard");
      }
    } else {
      // User exists but no profile, redirect to profile creation
      redirect("/profile/edit");
    }
  }

  // For non-logged-in users, show the normal signup form
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Create a provider account</CardTitle>
        <CardDescription>
          Enter your invite code to start hosting wellness events on Pulse Space
        </CardDescription>
      </CardHeader>
      <CardContent>
        <InviteCodeForm />
      </CardContent>
      <CardFooter className="flex justify-center">
        <a href="/login">Already have an account? Log in</a>
      </CardFooter>
    </Card>
  );
}
