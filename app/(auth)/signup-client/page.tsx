import { redirect } from "next/navigation";

import { ClientSignupForm } from "@/components/auth/client-signup-form";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { getUser } from "@/lib/auth";

export default async function ClientSignupPage() {
  const user = await getUser();

  if (user) {
    redirect("/dashboard");
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Create a client account</CardTitle>
        <CardDescription>
          Sign up to book wellness events on Pulse Space
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ClientSignupForm />
      </CardContent>
      <CardFooter className="flex justify-center">
        <a href="/login">Already have an account? Log in</a>
      </CardFooter>
    </Card>
  );
}
