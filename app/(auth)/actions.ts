"use server";

import { redirect } from "next/navigation";

import {
  adminSignupSchema,
  clientSignupSchema,
  providerSignupSchema,
} from "@/lib/schemas";
import { createAdminClient, createClient } from "@/utils/supabase/server";

export async function login(
  email: string,
  password: string,
  redirectTo?: string,
): Promise<{ error?: string }> {
  // Create Supabase server client
  const supabase = await createClient();

  // Sign in with email and password
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    return {
      error: error.message,
    };
  }

  // If a specific redirect path is provided, use it
  if (redirectTo && redirectTo !== "/profile") {
    redirect(redirectTo);
  }

  // Otherwise, determine redirect based on user role
  if (!data.user) {
    redirect("/");
    return {};
  }

  // Get user profile to determine role
  const { data: profile } = await supabase
    .from("profiles")
    .select("user_type")
    .eq("id", data.user.id)
    .single();

  // Redirect based on user type
  if (profile?.user_type === "provider") {
    redirect("/dashboard");
  } else if (profile?.user_type === "admin") {
    redirect("/admin/dashboard");
  } else {
    redirect("/profile");
  }
}

export async function signupProvider(
  fullName: string,
  email: string,
  password: string,
  confirmPassword: string,
  organizationName: string,
  city: string = "",
  country: string = "",
  agreeToTerms: boolean,
): Promise<{
  error?: string;
  success?: boolean;
  details?: Record<string, unknown>;
}> {
  try {
    // Validate other input fields
    const validatedFields = providerSignupSchema.safeParse({
      fullName,
      email,
      password,
      confirmPassword,
      organizationName,
      city,
      country,
      agreeToTerms,
    });

    if (!validatedFields.success) {
      return {
        success: false,
        error: "Invalid signup data",
        details: validatedFields.error.format(),
      };
    }

    const supabase = await createClient();
    // Create admin client to handle the entire signup process
    const adminSupabase = await createAdminClient();

    // Sign up user with Supabase
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    if (!data?.user) {
      return {
        success: false,
        error: "Failed to create user account. Please try again.",
      };
    }

    // Create profile record
    const profileData = {
      id: data.user.id,
      full_name: fullName,
      email: email,
      user_type: "provider",
    } as const;

    const { error: profileError } = await adminSupabase
      .from("profiles")
      .insert(profileData)
      .select();

    if (profileError) {
      console.error("Failed to create profile record:", profileError);
      return {
        success: false,
        error: profileError.message,
      };
    }

    // Create organization record
    const organizationData = {
      name: organizationName,
      city: city,
      country: country,
    };

    const { data: orgData, error: orgError } = await adminSupabase
      .from("provider_organizations")
      .insert(organizationData)
      .select();

    if (orgError || !orgData || orgData.length === 0) {
      console.error("Failed to create organization record:", orgError);
      return {
        success: false,
        error: orgError
          ? orgError.message
          : "Failed to create organization record",
      };
    }

    // Create provider record linked to the organization
    const providerData = {
      id: data.user.id,
      organization_id: orgData[0].id,
      role: "admin", // Default role for the creator
    };

    const { error: providerError } = await adminSupabase
      .from("providers")
      .insert(providerData)
      .select();

    if (providerError) {
      console.error("Failed to create provider record:", providerError);
      return {
        success: false,
        error: providerError.message,
      };
    }
  } catch (err) {
    console.error("Server provider signup error:", err);
    return {
      success: false,
      error:
        err instanceof Error
          ? err.message
          : "An unexpected error occurred during signup. Please try again.",
    };
  }

  redirect("/dashboard");
  return {
    success: true,
  };
}

export async function signupClient(
  fullName: string,
  email: string,
  password: string,
  confirmPassword: string,
  companyName: string,
  location: string = "",
): Promise<{
  error?: string;
  success?: boolean;
  details?: Record<string, unknown>;
}> {
  try {
    // Validate input fields
    const validatedFields = clientSignupSchema.safeParse({
      fullName,
      email,
      password,
      confirmPassword,
      companyName,
      location,
    });

    if (!validatedFields.success) {
      return {
        success: false,
        error: "Invalid signup data",
        details: validatedFields.error.format(),
      };
    }

    const supabase = await createClient();
    // Create admin client to handle the entire signup process
    const adminSupabase = await createAdminClient();

    // Sign up user with Supabase
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    if (!data?.user) {
      return {
        success: false,
        error: "Failed to create user account. Please try again.",
      };
    }

    // Create profile record
    const profileData = {
      id: data.user.id,
      full_name: fullName,
      email: email,
      user_type: "client",
    } as const;

    const { error: profileError } = await adminSupabase
      .from("profiles")
      .insert(profileData)
      .select();

    if (profileError) {
      console.error("Failed to create profile record:", profileError);
      return {
        success: false,
        error: profileError.message,
      };
    }

    // Create client record
    const clientData = {
      id: data.user.id,
      company_name: companyName,
      location: location,
    };

    const { error: clientError } = await adminSupabase
      .from("clients")
      .insert(clientData)
      .select();

    if (clientError) {
      console.error("Failed to create client record:", clientError);
      return {
        success: false,
        error: clientError.message,
      };
    }
  } catch (err) {
    console.error("Server client signup error:", err);
    return {
      success: false,
      error:
        err instanceof Error
          ? err.message
          : "An unexpected error occurred during signup. Please try again.",
    };
  }

  redirect("/profile");
  return {
    success: true,
  };
}

export async function signupAdmin(
  fullName: string,
  email: string,
  password: string,
  confirmPassword: string,
): Promise<{
  error?: string;
  success?: boolean;
  details?: Record<string, unknown>;
}> {
  try {
    // Only allow specific admin email
    if (email !== "<EMAIL>" && email !== "<EMAIL>") {
      return {
        success: false,
        error: "Admin signup is restricted to authorized users only",
      };
    }

    // Validate input fields using schema
    const validatedFields = adminSignupSchema.safeParse({
      fullName,
      email,
      password,
      confirmPassword,
    });

    if (!validatedFields.success) {
      return {
        success: false,
        error: "Invalid signup data",
        details: validatedFields.error.format(),
      };
    }

    const supabase = await createClient();
    const adminSupabase = await createAdminClient();

    // Sign up user with Supabase
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
        emailRedirectTo: `${process.env.NEXT_PUBLIC_BASE_URL}/auth/callback`,
      },
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    if (!data?.user) {
      return {
        success: false,
        error: "Failed to create user account. Please try again.",
      };
    }

    // Create profile record
    const profileData = {
      id: data.user.id,
      full_name: fullName,
      email: email,
      user_type: "admin",
    } as const;

    const { error: profileError } = await adminSupabase
      .from("profiles")
      .insert(profileData)
      .select();

    if (profileError) {
      console.error("Failed to create profile record:", profileError);
      return {
        success: false,
        error: profileError.message,
      };
    }
  } catch (err) {
    console.error("Server admin signup error:", err);
    return {
      success: false,
      error:
        err instanceof Error
          ? err.message
          : "An unexpected error occurred during signup. Please try again.",
    };
  }

  redirect("/admin/dashboard");
  return {
    success: true,
  };
}

export async function changePassword(
  currentPassword: string,
  newPassword: string,
): Promise<{ error?: string; success?: boolean }> {
  const supabase = await createClient();

  // First verify the current password by attempting to sign in
  const { error: signInError } = await supabase.auth.signInWithPassword({
    email: (await supabase.auth.getUser()).data.user?.email || "",
    password: currentPassword,
  });

  if (signInError) {
    return {
      error: "Current password is incorrect",
    };
  }

  // If current password is correct, update to the new password
  const { error: updateError } = await supabase.auth.updateUser({
    password: newPassword,
  });

  if (updateError) {
    return {
      error: updateError.message,
    };
  }

  return { success: true };
}

export async function forgotPassword(
  email: string,
): Promise<{ error?: string; success?: boolean; message?: string }> {
  const supabase = await createClient();

  if (!email) {
    return {
      error: "Email is required",
    };
  }

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${process.env.NEXT_PUBLIC_BASE_URL}/reset-password`,
  });

  if (error) {
    console.error("Forgot password error:", error.message);
    return {
      error: "Could not send password reset instructions",
    };
  }

  return {
    success: true,
    message: "Check your email for password reset instructions",
  };
}

export async function resetPassword(
  newPassword: string,
): Promise<{ error?: string; success?: boolean }> {
  const supabase = await createClient();

  if (!newPassword) {
    return {
      error: "Password and confirm password are required",
    };
  }

  const { error } = await supabase.auth.updateUser({
    password: newPassword,
  });

  if (error) {
    return {
      error: "Password update failed: " + error.message,
    };
  }

  // Determine redirect based on user role
  const { data: user } = await supabase.auth.getUser();
  if (user.user) {
    // Get user profile to determine role
    const { data: profile } = await supabase
      .from("profiles")
      .select("user_type")
      .eq("id", user.user.id)
      .single();

    // Redirect based on user type
    if (profile?.user_type === "provider") {
      redirect("/dashboard");
    } else if (profile?.user_type === "admin") {
      redirect("/admin/dashboard");
    } else {
      redirect("/profile");
    }
  } else {
    redirect("/");
  }
}

export async function signOut(isProvider: boolean = false) {
  const supabase = await createClient();
  const { error } = await supabase.auth.signOut();

  if (error) {
    console.error("Error signing out:", error);
  }

  // Redirect providers to /providers, others to /
  const redirectPath = isProvider ? "/providers" : "/";
  return redirect(redirectPath);
}
