import type React from "react";

import { Footer } from "@/components/footer";
import { Navbar } from "@/components/navbar";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <Navbar showAuth={false} />
      <main className="flex flex-1 flex-col items-center justify-start px-2 pt-4">
        {children}
      </main>
      <Footer />
    </>
  );
}
