import type { <PERSON>ada<PERSON> } from "next";
import { redirect } from "next/navigation";

import { LoginForm } from "@/components/auth/login-form";
import { getUser } from "@/lib/auth";

export const metadata: Metadata = {
  title: "Login",
  description:
    "Sign in to your Pulse Space account to manage wellness workshops and bookings.",
  robots: {
    index: false,
    follow: false,
  },
};

export default async function LoginPage({
  searchParams,
}: {
  searchParams: Promise<{ type?: string }>;
}) {
  const user = await getUser();

  if (user) {
    redirect("/");
  }

  const resolvedSearchParams = await searchParams;
  const type = resolvedSearchParams.type;

  return <LoginForm type={type} />;
}
