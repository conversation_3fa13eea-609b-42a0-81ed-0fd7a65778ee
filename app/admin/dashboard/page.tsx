import AdminClientsTable from "@/components/admin/admin-clients-table";
import AdminFunnelAnalysis from "@/components/admin/admin-funnel-analysis";
import AdminProvidersTable from "@/components/admin/admin-providers-table";
import AdminQuotesTable from "@/components/admin/admin-quotes-table";
import AdminStatsOverview from "@/components/admin/admin-stats-overview";
import AdminWorkshopsTable from "@/components/admin/admin-workshops-table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { requireAdmin } from "@/lib/auth";
import { createClient } from "@/utils/supabase/server";

export default async function AdminDashboard() {
  await requireAdmin();
  const supabase = await createClient();

  // Get high-level stats
  const [
    { count: totalProviders },
    { count: totalClients },
    { count: totalWorkshops },
    { count: totalQuotes },
    { count: publishedWorkshops },
    { count: pendingQuotes },
  ] = await Promise.all([
    supabase.from("providers").select("*", { count: "exact", head: true }),
    supabase.from("clients").select("*", { count: "exact", head: true }),
    supabase.from("workshops").select("*", { count: "exact", head: true }),
    supabase.from("quotes").select("*", { count: "exact", head: true }),
    supabase
      .from("workshops")
      .select("*", { count: "exact", head: true })
      .eq("published", true),
    supabase
      .from("quotes")
      .select("*", { count: "exact", head: true })
      .eq("status", "pending"),
  ]);

  const stats = {
    totalProviders: totalProviders || 0,
    totalClients: totalClients || 0,
    totalWorkshops: totalWorkshops || 0,
    totalQuotes: totalQuotes || 0,
    publishedWorkshops: publishedWorkshops || 0,
    pendingQuotes: pendingQuotes || 0,
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="text-gray-600">
          Monitor platform activity and examine the user funnel
        </p>
      </div>

      <AdminStatsOverview stats={stats} />

      <Tabs defaultValue="funnel" className="mt-8">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="funnel">Funnel Analysis</TabsTrigger>
          <TabsTrigger value="providers">Providers</TabsTrigger>
          <TabsTrigger value="clients">Clients</TabsTrigger>
          <TabsTrigger value="workshops">Workshops</TabsTrigger>
          <TabsTrigger value="quotes">Quotes</TabsTrigger>
        </TabsList>

        <TabsContent value="funnel" className="mt-6">
          <AdminFunnelAnalysis />
        </TabsContent>

        <TabsContent value="providers" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Provider Organizations</CardTitle>
              <CardDescription>
                All wellness providers and their details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AdminProvidersTable />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="clients" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Client Companies</CardTitle>
              <CardDescription>
                All corporate clients and their information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AdminClientsTable />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workshops" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Workshop Catalog</CardTitle>
              <CardDescription>
                All workshops available on the platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AdminWorkshopsTable />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quotes" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Quote Activity</CardTitle>
              <CardDescription>
                All quotes and their status throughout the funnel
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AdminQuotesTable />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
