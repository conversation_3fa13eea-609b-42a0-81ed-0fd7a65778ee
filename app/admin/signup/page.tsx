import { redirect } from "next/navigation";

import AdminSignupForm from "@/components/auth/admin-signup-form";
import { createClient } from "@/utils/supabase/server";

export default async function AdminSignupPage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // If user is already logged in, check their role and redirect
  if (user) {
    const { data: profile } = await supabase
      .from("profiles")
      .select("user_type")
      .eq("id", user.id)
      .single();

    if (profile?.user_type === "admin") {
      redirect("/admin/dashboard");
    } else if (profile?.user_type === "provider") {
      redirect("/dashboard");
    } else {
      redirect("/profile");
    }
  }

  return (
    <div className="flex min-h-screen flex-col justify-center bg-gray-50 py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          Admin Account Setup
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Create an administrator account
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10">
          <AdminSignupForm />
        </div>
      </div>
    </div>
  );
}
