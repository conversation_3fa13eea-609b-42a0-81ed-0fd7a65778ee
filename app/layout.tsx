import "@/app/globals.css";

import { GoogleAnalytics } from "@next/third-parties/google";
import { NextSSRPlugin } from "@uploadthing/react/next-ssr-plugin";
import type { Metadata, Viewport } from "next";
import { Lexend, Quicksand } from "next/font/google";
import type React from "react";
import { extractRouterConfig } from "uploadthing/server";

import { ourFileRouter } from "@/app/api/uploadthing/core";
import { QueryProvider } from "@/components/query-provider";
import { ThemeProvider } from "@/components/theme-provider";
import { SupabaseAuthProvider } from "@/contexts/supabase-auth-provider";

const lexend = Lexend({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-lexend",
});

const quicksand = Quicksand({
  subsets: ["latin"],
  variable: "--font-quicksand",
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: "#ffffff",
};

export const metadata: Metadata = {
  title: {
    default: "Pulse Space - Discover & Book Wellness Workshops",
    template: "%s | Pulse Space",
  },
  description:
    "Connect with certified wellness providers and book transformative workshops for your organization. From yoga and meditation to nutrition and stress management.",
  keywords: [
    "wellness workshops",
    "corporate wellness",
    "workplace wellbeing",
    "meditation classes",
    "yoga workshops",
    "stress management",
    "team building wellness",
    "employee wellness programs",
    "mental health workshops",
    "nutrition workshops",
  ],
  metadataBase: new URL("https://pulsespace.co"),
  openGraph: {
    siteName: "Pulse Space",
    type: "website",
    locale: "en_US",
    title: "Pulse Space - Discover & Book Wellness Workshops",
    description:
      "Connect with certified wellness providers and book transformative workshops for your organization.",
  },
  twitter: {
    card: "summary_large_image",
    title: "Pulse Space - Discover & Book Wellness Workshops",
    description:
      "Connect with certified wellness providers and book transformative workshops for your organization.",
  },
  robots: {
    index: true,
    follow: true,
    "max-image-preview": "large",
    "max-snippet": -1,
    "max-video-preview": -1,
    googleBot: "index, follow",
  },
  alternates: {
    canonical: "https://pulsespace.co",
  },
  applicationName: "Pulse Space",
  appleWebApp: {
    title: "Pulse Space",
    statusBarStyle: "default",
    capable: true,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <GoogleAnalytics gaId="G-E2W0GHHSJJ" />
      <body
        className={`${lexend.variable} ${quicksand.variable} ${quicksand.className}`}
      >
        <NextSSRPlugin
          /**
           * The `extractRouterConfig` will extract only the route configs
           * from the router to prevent additional information from being
           * leaked to the client.
           */
          routerConfig={extractRouterConfig(ourFileRouter)}
        />
        <QueryProvider>
          <SupabaseAuthProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="light"
              enableSystem={false}
              disableTransitionOnChange
              forcedTheme="light"
            >
              <div className="flex min-h-screen flex-col">{children}</div>
            </ThemeProvider>
          </SupabaseAuthProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
