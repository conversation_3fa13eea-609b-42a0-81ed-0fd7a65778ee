import type { User } from "@supabase/supabase-js";
import { redirect } from "next/navigation";

import { type TypedProfile } from "@/types/user";
import { createClient as createServerClient } from "@/utils/supabase/server";

export async function getUser(): Promise<User | null> {
  const supabase = await createServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  return user;
}

export async function requireUser() {
  const user = await getUser();
  if (!user) {
    redirect("/login");
  }
  return user;
}

export async function getUserProfile(
  userId: string,
): Promise<TypedProfile | null> {
  const supabase = await createServerClient();
  const { data } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", userId)
    .single();

  return data as TypedProfile | null;
}

export async function requireProvider() {
  const user = await requireUser();
  const profile = await getUserProfile(user.id);

  if (!profile) {
    redirect("/profile/edit");
  }

  if (profile.user_type !== "provider") {
    redirect("/profile");
  }

  return { user, profile };
}

export async function requireProviderWithDetails() {
  const { user, profile } = await requireProvider();
  const supabase = await createServerClient();
  const { data: provider } = await supabase
    .from("providers")
    .select("*")
    .eq("id", user.id)
    .single();

  if (!provider) {
    redirect("/");
  }

  return { user, profile, provider };
}

export async function requireAdmin() {
  const user = await requireUser();
  const profile = await getUserProfile(user.id);

  if (!profile) {
    redirect("/");
  }

  if (profile.user_type !== "admin") {
    redirect("/");
  }

  return { user, profile };
}
