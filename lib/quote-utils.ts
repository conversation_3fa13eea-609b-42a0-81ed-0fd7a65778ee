import { Temporal } from "temporal-polyfill";

/**
 * Converts a UTC datetime string to a local datetime string formatted for an HTML `datetime-local` input, adjusting to the next valid occurrence if the proposed time is in the past.
 *
 * If the converted local datetime is earlier than the current local time, returns the same time on the next occurrence of that weekday (at least one week ahead if the time has already passed today).
 *
 * @param utcDatetimeString - The UTC datetime string to convert and adjust
 * @returns A local datetime string in `YYYY-MM-DDTHH:mm` format suitable for `datetime-local` inputs
 */
export function adjustProposedDateTime(utcDatetimeString: string): string {
  // Convert UTC datetime to local datetime for the form
  const utcInstant = Temporal.Instant.from(utcDatetimeString);
  const timeZone = Temporal.Now.timeZoneId();
  const zonedDateTime = utcInstant.toZonedDateTimeISO(timeZone);
  let proposedDateTime = zonedDateTime.toPlainDateTime();

  // Smart date adjustment - if date is in the past, find next occurrence of that day of week
  const now = Temporal.Now.plainDateTimeISO(timeZone);
  if (Temporal.PlainDateTime.compare(proposedDateTime, now) < 0) {
    const daysToAdd = (7 + proposedDateTime.dayOfWeek - now.dayOfWeek) % 7;
    const nextOccurrence = now.toPlainDate().add({ days: daysToAdd || 7 });
    proposedDateTime = nextOccurrence.toPlainDateTime(
      proposedDateTime.toPlainTime(),
    );
  }

  // Convert to the format expected by datetime-local input
  return proposedDateTime.toString().slice(0, 16); // YYYY-MM-DDTHH:mm
}

/**
 * Converts a local datetime string to its equivalent UTC datetime string.
 *
 * Parses the input as a local datetime, applies the system's current timezone, and returns the corresponding UTC datetime in ISO format.
 *
 * @param localDateTime - The local datetime string to convert, in the format "YYYY-MM-DDTHH:mm" or similar
 * @returns The UTC datetime string in ISO 8601 format
 */
export function convertLocalToUTCDateTime(localDateTime: string): string {
  const plainDateTime = Temporal.PlainDateTime.from(localDateTime);
  const timeZone = Temporal.Now.timeZoneId();
  const zonedDateTime = plainDateTime.toZonedDateTime(timeZone);
  return zonedDateTime.toInstant().toString();
}
