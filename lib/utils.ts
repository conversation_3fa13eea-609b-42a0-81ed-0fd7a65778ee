import { type ClassValue, clsx } from "clsx";
import { ReadonlyURLSearchParams } from "next/navigation";
import { twMerge } from "tailwind-merge";
import type { z } from "zod";

import type {
  PricingModel,
  TimeSlot,
  WorkshopDailyAvailability,
} from "@/types/types";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Parse URL search parameters in a type-safe way using Zod schemas
 */
export function parseSearchParams<T extends z.ZodTypeAny>(
  searchParams:
    | ReadonlyURLSearchParams
    | { [key: string]: string | string[] | undefined }
    | null
    | undefined,
  schema: T,
): z.infer<T> {
  // Convert searchParams to a regular object if it's ReadonlyURLSearchParams
  const paramsObj =
    searchParams instanceof URLSearchParams ||
    searchParams instanceof ReadonlyURLSearchParams
      ? Object.fromEntries(searchParams.entries())
      : searchParams || {};

  // Parse the params with the schema, providing a fallback for parse errors
  try {
    return schema.parse(paramsObj);
  } catch {
    // If validation fails, return an empty object that matches the schema shape
    // We use schema.safeParse() to get the default values
    const defaultResult = schema.safeParse({});
    if (defaultResult.success) {
      return defaultResult.data;
    }

    // Fallback to a simple empty object with type casting
    return {} as z.infer<T>;
  }
}

function formatTime(time: string): string {
  const [hours, minutes] = time.split(":").map(Number);
  const period = hours >= 12 ? "pm" : "am";
  const displayHours = hours % 12 || 12; // Convert 0 to 12 for 12am

  // Omit minutes if they're zero
  return minutes === 0
    ? `${displayHours}${period}`
    : `${displayHours}:${minutes.toString().padStart(2, "0")}${period}`;
}

// Abbreviations for days of the week
const DAY_ABBREVIATIONS: Record<string, string> = {
  monday: "Mon",
  tuesday: "Tue",
  wednesday: "Wed",
  thursday: "Thu",
  friday: "Fri",
  saturday: "Sat",
  sunday: "Sun",
};

export function formatTimeSlots(
  availability: WorkshopDailyAvailability | null | undefined,
): string {
  if (!availability) {
    return "Flexible scheduling";
  }

  // Filter out null slots and prepare data
  const slotsByTime: Record<string, string[]> = {};

  Object.entries(availability)
    .filter(
      ([, slot]) =>
        slot !== null && slot?.start_time !== null && slot?.end_time !== null,
    )
    .forEach(([day, slot]) => {
      const timeSlot = slot as TimeSlot;
      const timeKey = `${formatTime(timeSlot.start_time!)}-${formatTime(timeSlot.end_time!)}`;
      if (!slotsByTime[timeKey]) {
        slotsByTime[timeKey] = [];
      }
      slotsByTime[timeKey].push(day);
    });

  if (Object.keys(slotsByTime).length === 0) {
    return "Flexible scheduling";
  }

  // Format the output with grouped days
  return Object.entries(slotsByTime)
    .map(([timeRange, days]) => {
      // Sort days to ensure they're in order of the week
      const sortedDays = days.sort((a, b) => {
        const dayOrder = [
          "monday",
          "tuesday",
          "wednesday",
          "thursday",
          "friday",
          "saturday",
          "sunday",
        ];
        return dayOrder.indexOf(a) - dayOrder.indexOf(b);
      });

      // Group consecutive days
      const dayGroups: string[][] = [];
      let currentGroup: string[] = [sortedDays[0]];

      for (let i = 1; i < sortedDays.length; i++) {
        const currentDayIndex = [
          "monday",
          "tuesday",
          "wednesday",
          "thursday",
          "friday",
          "saturday",
          "sunday",
        ].indexOf(sortedDays[i - 1]);
        const nextDayIndex = [
          "monday",
          "tuesday",
          "wednesday",
          "thursday",
          "friday",
          "saturday",
          "sunday",
        ].indexOf(sortedDays[i]);

        if (nextDayIndex - currentDayIndex === 1) {
          // Consecutive days
          currentGroup.push(sortedDays[i]);
        } else {
          // Non-consecutive, start a new group
          dayGroups.push([...currentGroup]);
          currentGroup = [sortedDays[i]];
        }
      }

      // Add the last group
      dayGroups.push(currentGroup);

      // Format each group
      const formattedDayGroups = dayGroups.map((group) => {
        if (group.length === 1) {
          return DAY_ABBREVIATIONS[group[0]];
        } else {
          return `${DAY_ABBREVIATIONS[group[0]]} - ${DAY_ABBREVIATIONS[group[group.length - 1]]}`;
        }
      });

      // Format the time range
      const [startTime, endTime] = timeRange.split("-");

      return `${formattedDayGroups.join(", ")}: ${startTime} - ${endTime}`;
    })
    .join(" | ");
}

/**
 * Format a snake_case string into a human-readable format
 * e.g. "in_person" -> "In Person"
 */
export function formatSnakeCase(text: string | null | undefined): string {
  if (!text) return "";

  return text
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

/**
 * Format pricing model enum values into human-readable text
 * @param pricingModel The pricing model enum value ("per_person" | "total")
 * @returns Human-readable pricing model text
 */
export function formatPricingModel(
  pricingModel: PricingModel | null | undefined,
): string {
  if (!pricingModel) return "";

  switch (pricingModel) {
    case "per_person":
      return "Per Person";
    case "total":
      return "Total Fee";
    default:
      return formatSnakeCase(pricingModel);
  }
}

/**
 * Maps venue types to human-readable text
 * @param venueType The venue type from the database
 * @returns Human-readable venue type description
 */
export function getVenueTypeDisplay(
  venueType: string | null | undefined,
): string {
  if (!venueType) return "Location";

  switch (venueType) {
    case "provider_location":
      return "Provider's Location";
    case "client_location":
      return "Your Office";
    case "provider_or_client_location":
      return "Flexible Location";
    case "online":
      return "Virtual Workshop";
    default:
      return "Location";
  }
}
