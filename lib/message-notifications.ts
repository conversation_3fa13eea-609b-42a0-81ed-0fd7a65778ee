import nodemailer from "nodemailer";

import { formatCurrency } from "@/lib/format";
import { formatDate } from "@/lib/temporal-utils";
import type { SimpleQuote } from "@/types/quote";
import { createClient } from "@/utils/supabase/server";

// Email transporter configuration
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || "smtp.forwardemail.net",
  port: Number(process.env.EMAIL_PORT) || 465,
  secure: true,
  auth: {
    user: process.env.EMAIL_USERNAME || "",
    pass: process.env.EMAIL_PASSWORD || "",
  },
});

/**
 * Get all recipients for a chat message (excluding the sender)
 *
 * @param roomId The chat room ID
 * @param senderId The ID of the message sender to exclude
 * @returns Array of recipient information objects
 */
async function getChatRecipients(
  roomId: string,
  senderId: string,
): Promise<Array<ContactInfo>> {
  const supabase = await createClient();

  try {
    // Get the chat room details
    const { data: room, error: roomError } = await supabase
      .from("chat_rooms")
      .select(
        `
        client_id,
        provider_organization_id
        `,
      )
      .eq("id", roomId)
      .single();

    if (roomError || !room) {
      console.error("Error fetching chat room:", roomError);
      return [];
    }

    const recipients = [];

    // If sender is not the client, add client as recipient
    if (senderId !== room.client_id) {
      const clientInfo = await getRecipientInfo(room.client_id);
      if (clientInfo) {
        recipients.push(clientInfo);
      }
    }

    // If sender is not from the provider organization, add provider contacts
    if (senderId === room.client_id) {
      const providerContacts = await getProviderContacts(
        room.provider_organization_id,
      );
      recipients.push(...providerContacts);
    }

    return recipients;
  } catch (error) {
    console.error("Error in getChatRecipients:", error);
    return [];
  }
}

/**
 * Get recipient information by user ID
 *
 * @param userId The user ID
 * @returns Recipient information or null if not found
 */
async function getRecipientInfo(userId: string): Promise<{
  recipientId: string;
  recipientName: string | null;
  recipientEmail: string | null;
} | null> {
  const supabase = await createClient();

  try {
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("full_name, email")
      .eq("id", userId)
      .single();

    if (profileError || !profile) {
      console.error("Error fetching recipient profile:", profileError);
      return null;
    }

    if (!profile.email) {
      console.error("No email found for user:", userId);
      return null;
    }

    return {
      recipientId: userId,
      recipientName: profile.full_name,
      recipientEmail: profile.email,
    };
  } catch (error) {
    console.error("Error in getRecipientInfo:", error);
    return null;
  }
}

interface ProfileData {
  id: string;
  full_name: string | null;
  email: string | null;
}

type ProviderData = {
  id: string;
  profiles: ProfileData | ProfileData[];
};

type ContactInfo = {
  recipientId: string;
  recipientName: string | null;
  recipientEmail: string | null;
};

/**
 * Get all provider contacts for an organization
 *
 * @param organizationId The provider organization ID
 * @returns Array of recipient information objects
 */
async function getProviderContacts(organizationId: string): Promise<
  Array<{
    recipientId: string;
    recipientName: string | null;
    recipientEmail: string | null;
  }>
> {
  try {
    const supabase = await createClient();

    // Use a standard join query to get provider profiles
    const { data, error } = await supabase
      .from("providers")
      .select(
        `
        id,
        profiles!inner(id, full_name, email)
      `,
      )
      .eq("organization_id", organizationId)
      .order("created_at", { ascending: true });

    if (error) {
      console.error("Error in getProviderContacts:", error);
      return [];
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Process the data with proper typing
    return data
      .filter((item: ProviderData) => {
        // Get the profile data from the join result
        const profiles = item.profiles;
        if (!profiles) return false;

        // Handle both array and single object responses
        const profile = Array.isArray(profiles) ? profiles[0] : profiles;
        if (!profile) return false;

        // Only include providers with valid emails
        return profile.email;
      })
      .map((item: ProviderData): ContactInfo => {
        // Handle both array and single object responses
        const profile = Array.isArray(item.profiles)
          ? item.profiles[0]
          : item.profiles;

        return {
          recipientId: item.id,
          recipientName: profile.full_name,
          recipientEmail: profile.email,
        };
      });
  } catch (error) {
    console.error("Error in getProviderContacts:", error);
    return [];
  }
}

/**
 * Generate HTML content for quote messages
 *
 * @param quote The quote object
 * @returns Formatted HTML string for the quote
 */
async function generateQuoteHtmlContent(quote: SimpleQuote): Promise<string> {
  // Format date and time using temporal
  const { Temporal } = await import("temporal-polyfill");
  const instant = Temporal.Instant.from(quote.proposed_datetime);
  const formattedDate = formatDate(instant);
  const zdt = instant.toZonedDateTimeISO(Temporal.Now.timeZoneId());
  const formattedTime = zdt.toLocaleString("en-GB", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });

  const workshopName = quote.workshops?.name || "Workshop";
  const providerOrgName = quote.provider_organizations.name;

  return `<div style="border: 1px solid #e5e7eb; background-color: #ffffff; padding: 24px; border-radius: 12px; margin: 20px 0; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);">
    <div style="font-size: 20px; font-weight: 600; color: #111827; margin-bottom: 16px; line-height: 1.2;">${workshopName} · ${providerOrgName}</div>
    <div style="display: flex; align-items: center; margin-bottom: 12px; font-size: 16px; color: #374151;">
      <span style="margin-right: 8px;">📍</span>
      <span>${quote.location}</span>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 12px; font-size: 16px; color: #374151;">
      <span style="margin-right: 8px;">📅</span>
      <span>${formattedDate}  ⏰ ${formattedTime}</span>
    </div>
    <div style="display: flex; align-items: center; margin-bottom: 12px; font-size: 16px; color: #374151;">
      <span style="margin-right: 8px;">💲</span>
      <span>${formatCurrency(quote.price, quote.currency)}</span>
    </div>
    ${quote.notes ? `<div style="font-size: 14px; color: #6b7280; line-height: 1.5; white-space: pre-line">${quote.notes}</div>` : ""}
  </div>`;
}

/**
 * Generate HTML content for regular messages
 *
 * @param messageContent The message content
 * @returns Formatted HTML string for the message
 */
function generateMessageHtmlContent(messageContent: string): string {
  return `<div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0; white-space: pre-wrap; font-family: Arial, sans-serif;">${messageContent}</div>`;
}

/**
 * Send an email notification
 *
 * @param params Email parameters
 * @returns Success status and any error
 */
type EmailParams = {
  senderName: string;
  recipientEmail: string;
  roomId: string;
  recipientName: string;
} & (
  | {
      messageType: "message";
      messageContent: string;
    }
  | {
      messageType: "quote";
      quote: SimpleQuote;
    }
);

async function sendEmail(params: EmailParams): Promise<void> {
  const { senderName, recipientEmail, roomId, recipientName, messageType } =
    params;
  const subject = `New message from ${senderName} on PulseSpace`;

  const actionText =
    messageType === "quote"
      ? "has sent you a new quote"
      : "has sent you a new message";

  const textContent =
    messageType === "quote"
      ? (() => {
          const quote = params.quote;
          const workshopName = quote.workshops?.name || "Workshop";
          const providerOrgName = quote.provider_organizations.name;
          return `${workshopName} · ${providerOrgName}\n\n📍 ${quote.location}\n📅 ${quote.proposed_datetime}\n💲 ${formatCurrency(quote.price, quote.currency)}\n\n${quote.notes || ""}`;
        })()
      : params.messageContent;

  await transporter.sendMail({
    from: `"PulseSpace" <${process.env.EMAIL_USERNAME}>`,
    to: recipientEmail,
    subject,
    text: `Hello ${recipientName},\n\n${senderName} ${actionText} on PulseSpace:\n\n${textContent}\n\nView the conversation: ${process.env.NEXT_PUBLIC_BASE_URL}/messages/${roomId}\n\nBest regards,\nThe PulseSpace Team`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px;">
        <p>Hello ${recipientName},</p>
        <p><strong>${senderName}</strong> ${actionText}:</p>
        ${
          messageType === "quote"
            ? await generateQuoteHtmlContent(params.quote)
            : generateMessageHtmlContent(params.messageContent)
        }
        <p>
          <a href="${process.env.NEXT_PUBLIC_BASE_URL}/messages/${roomId}" style="background-color: #ff6b6b; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; display: inline-block;">
            View Conversation
          </a>
        </p>
        <p style="color: #777; font-size: 0.9em; margin-top: 30px;">
          Best regards,<br>
          The PulseSpace Team
        </p>
      </div>
    `,
  });
}

/**
 * Send chat message notifications to all recipients
 *
 * This function sends notifications without returning any value.
 * Errors are logged but not propagated to the caller.
 *
 * @param roomId The chat room ID
 * @param senderId The ID of the message sender
 * @param senderName The name of the message sender
 * @param messageContent The content of the message
 */
export function sendChatNotificationAsync(
  roomId: string,
  senderId: string,
  senderName: string,
  messageContent: string,
): void {
  // Fire and forget - don't await the promise and don't propagate errors
  sendNotifications(roomId, senderId, senderName, {
    messageType: "message",
    messageContent,
  }).catch((error) => {
    console.error("Error sending chat notifications:", error);
  });
}

/**
 * Send quote notifications to all recipients
 *
 * This function sends notifications without returning any value.
 * Errors are logged but not propagated to the caller.
 *
 * @param roomId The chat room ID
 * @param senderId The ID of the quote sender
 * @param senderName The name of the quote sender
 * @param quote The structured quote data
 */
export function sendQuoteNotificationAsync(
  roomId: string,
  senderId: string,
  senderName: string,
  quote: SimpleQuote,
): void {
  // Fire and forget - don't await the promise and don't propagate errors
  sendNotifications(roomId, senderId, senderName, {
    messageType: "quote",
    quote,
  }).catch((error) => {
    console.error("Error sending quote notifications:", error);
  });
}

/**
 * Send email notifications to all recipients in a chat room
 *
 * @param roomId The chat room ID
 * @param senderId The ID of the sender
 * @param senderName The name of the sender
 * @param emailParams The email content parameters
 */
async function sendNotifications(
  roomId: string,
  senderId: string,
  senderName: string,
  emailParams:
    | { messageType: "message"; messageContent: string }
    | { messageType: "quote"; quote: SimpleQuote },
): Promise<void> {
  try {
    // Get all recipients for this notification
    const recipients = await getChatRecipients(roomId, senderId);

    if (recipients.length === 0) {
      console.log(
        `No valid recipients found for ${emailParams.messageType} notification`,
      );
      return;
    }

    // Send email to each recipient
    const emailPromises = recipients.map((recipient) => {
      if (!recipient.recipientEmail || !recipient.recipientName)
        return Promise.resolve();

      // Wrap each email send in a catch to prevent one failure from stopping all emails
      return sendEmail({
        senderName,
        recipientEmail: recipient.recipientEmail,
        roomId,
        recipientName: recipient.recipientName,
        ...emailParams,
      } as EmailParams).catch((error) => {
        console.error(
          `Failed to send ${emailParams.messageType} email to ${recipient.recipientEmail}:`,
          error,
        );
      });
    });

    // Wait for all emails to be sent
    await Promise.all(emailPromises);
  } catch (error) {
    console.error(
      `Error in sendNotifications for ${emailParams.messageType}:`,
      error,
    );
    // Error is caught and logged but not propagated
  }
}
