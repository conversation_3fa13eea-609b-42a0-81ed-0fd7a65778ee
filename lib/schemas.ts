import { z } from "zod";

/**
 * Schema for workshop search parameters
 * Used in both server and client components for consistent validation
 */
export const workshopSearchSchema = z.object({
  // Workshop format: array of types
  type: z
    .union([
      z.array(z.enum(["in_person", "online", "hybrid"])),
      z.enum(["in_person", "online", "hybrid"]),
    ])
    .optional(),

  // Category filter
  category: z.union([z.array(z.string()), z.string()]).optional(),

  // Date filter: today, week, month
  date: z.enum(["today", "week", "month"]).optional(),

  // Page number for pagination
  page: z.coerce.number().int().positive().default(1),
});

/**
 * Common fields for all user signups
 */
const commonUserFields = {
  fullName: z
    .string()
    .min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z
    .string()
    .min(6, { message: "Password must be at least 6 characters" }),
  confirmPassword: z
    .string()
    .min(1, { message: "Please confirm your password" }),
};

/**
 * Schema for provider signup
 */
export const providerSignupSchema = z
  .object({
    ...commonUserFields,
    organizationName: z
      .string()
      .min(2, { message: "Organisation name must be at least 2 characters" }),
    city: z.string().min(1),
    country: z.string().min(1, { message: "Please select a city and country" }),
    agreeToTerms: z.boolean().refine((val) => val === true, {
      message: "You must agree to the terms and conditions and privacy policy",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export type ProviderSignupParams = z.infer<typeof providerSignupSchema>;

/**
 * Schema for client signup
 */
export const clientSignupSchema = z
  .object({
    ...commonUserFields,
    companyName: z.string().min(2, { message: "Company name is required" }),
    location: z.string().min(1, { message: "Location is required" }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export type ClientSignupParams = z.infer<typeof clientSignupSchema>;

/**
 * Schema for admin signup
 */
export const adminSignupSchema = z
  .object({
    ...commonUserFields,
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });
