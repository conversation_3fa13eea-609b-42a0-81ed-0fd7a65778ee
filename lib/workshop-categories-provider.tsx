import type { SupabaseClient } from "@supabase/supabase-js";

import type { Database } from "@/types/database.types";
import { createClient } from "@/utils/supabase/server";

type MySupabaseClient = SupabaseClient<Database>;

type CategoryGroup = Database["public"]["Tables"]["category_groups"]["Row"];
type Category = Database["public"]["Tables"]["categories"]["Row"];
type WorkshopCategory =
  Database["public"]["Tables"]["workshop_categories"]["Row"];

/** Fetches all category data needed for workshop forms */
export async function getWorkshopCategories(workshopId?: string): Promise<{
  categoryGroups: CategoryGroup[];
  categories: Category[];
  workshopCategories: WorkshopCategory[];
}> {
  const supabase = await createClient();

  const [categoryGroups, categories, workshopCategories] = await Promise.all([
    fetchCategoryGroups(supabase),
    fetchCategories(supabase),
    workshopId
      ? fetchWorkshopCategories(supabase, workshopId)
      : Promise.resolve([]),
  ]);

  return {
    categoryGroups,
    categories,
    workshopCategories,
  };
}

/** Fetches all category groups from the database */
async function fetchCategoryGroups(
  supabase: MySupabaseClient,
): Promise<CategoryGroup[]> {
  const { data, error } = await supabase
    .from("category_groups")
    .select("*")
    .order("name");

  if (error) {
    console.error("Error fetching categoryGroups:", error);
    return [];
  }

  return data || [];
}

/** Fetches all categories from the database */
async function fetchCategories(
  supabase: MySupabaseClient,
): Promise<Category[]> {
  const { data, error } = await supabase
    .from("categories")
    .select("*")
    .order("name");

  if (error) {
    console.error("Error fetching categories:", error);
    return [];
  }

  return data || [];
}

/** Fetches workshop categories for a specific workshop */
async function fetchWorkshopCategories(
  supabase: MySupabaseClient,
  workshopId: string,
): Promise<WorkshopCategory[]> {
  const { data, error } = await supabase
    .from("workshop_categories")
    .select("*")
    .eq("workshop_id", workshopId);

  if (error) {
    console.error("Error fetching workshopCategories:", error);
    return [];
  }

  return data || [];
}
