/**
 * Payment calculation utilities for service fees and provider earnings
 */

export const SERVICE_FEE_RATE = 0.2; // 20%

/**
 * Calculates the service fee as a percentage of the total amount.
 *
 * @param totalAmount - The total payment amount
 * @returns The service fee, rounded to two decimal places
 */
export function calculateServiceFee(totalAmount: number): number {
  return parseFloat((totalAmount * SERVICE_FEE_RATE).toFixed(2));
}

/**
 * Returns the service fee and provider earnings for a given total amount.
 *
 * @param totalAmount - The total payment amount to be split
 * @returns An object containing the calculated service fee and provider earnings, both rounded to two decimal places
 */
export function calculatePaymentBreakdown(totalAmount: number): {
  serviceFee: number;
  providerEarnings: number;
} {
  const serviceFee = calculateServiceFee(totalAmount);
  return {
    serviceFee,
    providerEarnings: totalAmount - serviceFee,
  };
}
