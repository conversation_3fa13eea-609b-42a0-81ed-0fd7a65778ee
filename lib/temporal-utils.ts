import { Temporal } from "temporal-polyfill";

// Formatting utilities
export function formatDate(
  instant: Temporal.Instant,
  timeZone: string = Temporal.Now.timeZoneId(),
): string {
  const zdt = instant.toZonedDateTimeISO(timeZone);
  return zdt.toLocaleString("en-GB", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

export function formatTime(
  instant: Temporal.Instant,
  timeZone: string = Temporal.Now.timeZoneId(),
): string {
  const zdt = instant.toZonedDateTimeISO(timeZone);
  return zdt.toLocaleString("en-GB", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
}
