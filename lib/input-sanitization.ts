/**
 * Sanitize user input to prevent injection attacks and ensure data integrity
 *
 * This function removes potentially dangerous characters and normalizes input
 * to prevent various types of injection attacks including:
 * - HTML/Script injection
 * - Email header injection
 * - Data overflow attacks
 *
 * @param input - The user input string to sanitize
 * @param maxLength - Maximum allowed length (default: 500)
 * @returns Sanitized string safe for interpolation
 */
export function sanitizeInput(input: string, maxLength: number = 500): string {
  if (!input) return "";

  return input
    .replace(/[<>]/g, "") // Remove angle brackets to prevent HTML/script injection
    .replace(/[\r\n]/g, " ") // Replace line breaks with spaces to prevent email header injection
    .replace(/\0/g, "") // Remove null bytes
    .trim() // Remove leading/trailing whitespace
    .slice(0, maxLength); // Limit length to prevent overflow
}

/**
 * Sanitize email address specifically
 * More restrictive sanitization for email addresses
 *
 * @param email - The email address to sanitize
 * @returns Sanitized email address
 */
export function sanitizeEmail(email: string): string {
  if (!email) return "";

  return email
    .replace(/[<>\r\n\0]/g, "") // Remove dangerous characters
    .replace(/\s+/g, "") // Remove all whitespace
    .trim()
    .slice(0, 254); // RFC 5321 limit for email addresses
}
