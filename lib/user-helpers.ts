import type {
  ClientProfile,
  ProviderProfile,
  TypedProfile,
} from "@/types/user";

// Helper function to check if a user is a provider
export function isProvider(profile: TypedProfile): profile is ProviderProfile {
  return profile.user_type === "provider";
}

// Helper function to check if a user is a client
export function isClient(profile: TypedProfile): profile is ClientProfile {
  return profile.user_type === "client";
}
