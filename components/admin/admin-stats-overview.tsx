import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { CardSectionTitle } from "@/components/ui/card-section-title";

interface AdminStatsOverviewProps {
  stats: {
    totalProviders: number;
    totalClients: number;
    totalWorkshops: number;
    totalQuotes: number;
    publishedWorkshops: number;
    pendingQuotes: number;
  };
}

export default function AdminStatsOverview({ stats }: AdminStatsOverviewProps) {
  const workshopPublishRate =
    stats.totalWorkshops > 0
      ? Math.round((stats.publishedWorkshops / stats.totalWorkshops) * 100)
      : 0;

  const quotePendingRate =
    stats.totalQuotes > 0
      ? Math.round((stats.pendingQuotes / stats.totalQuotes) * 100)
      : 0;

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      <Card>
        <CardHeader>
          <CardSectionTitle as="h3">Total Providers</CardSectionTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalProviders}</div>
          <p className="text-xs text-muted-foreground">
            Organizations registered
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardSectionTitle as="h3">Total Clients</CardSectionTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalClients}</div>
          <p className="text-xs text-muted-foreground">Companies registered</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardSectionTitle as="h3">Total Workshops</CardSectionTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalWorkshops}</div>
          <p className="text-xs text-muted-foreground">Created by providers</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardSectionTitle as="h3">Published Workshops</CardSectionTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.publishedWorkshops}</div>
          <div className="flex items-center gap-2">
            <Badge
              variant={workshopPublishRate >= 50 ? "default" : "secondary"}
            >
              {workshopPublishRate}%
            </Badge>
            <p className="text-xs text-muted-foreground">publish rate</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardSectionTitle as="h3">Total Quotes</CardSectionTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalQuotes}</div>
          <p className="text-xs text-muted-foreground">Generated</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardSectionTitle as="h3">Pending Quotes</CardSectionTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.pendingQuotes}</div>
          <div className="flex items-center gap-2">
            <Badge variant={quotePendingRate <= 30 ? "default" : "destructive"}>
              {quotePendingRate}%
            </Badge>
            <p className="text-xs text-muted-foreground">pending</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
