import { formatDistanceToNow } from "date-fns";
import { Temporal } from "temporal-polyfill";

import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate, formatTime } from "@/lib/temporal-utils";
import { createClient } from "@/utils/supabase/server";

interface QuoteData {
  id: string;
  status: string;
  price: number;
  currency: string;
  proposed_datetime: string;
  location: string;
  notes: string | null;
  expires_at: string | null;
  created_at: string;
  workshop: {
    id: string;
    name: string;
  };
  provider: {
    id: string;
    name: string;
  };
  client: {
    id: string;
    company_name: string;
    profile: {
      full_name: string | null;
      email: string;
    };
  };
  chat_room: {
    id: string;
    created_at: string;
  };
}

export default async function AdminQuotesTable() {
  const supabase = await createClient();

  const { data: quotes, error } = await supabase
    .from("quotes")
    .select(
      `
      id,
      status,
      price,
      currency,
      proposed_datetime,
      location,
      notes,
      expires_at,
      created_at,
      client_id,
      workshops!workshop_id (
        id,
        name
      ),
      provider_organizations!provider_organization_id (
        id,
        name
      ),
      chat_rooms!chat_room_id (
        id,
        created_at
      )
    `,
    )
    .order("created_at", { ascending: false });

  // Fetch client details separately
  const clientIds = [...new Set(quotes?.map((q) => q.client_id) || [])];
  const { data: clientsData } = await supabase
    .from("clients")
    .select(
      `
      id,
      company_name,
      profiles!id (
        full_name,
        email
      )
    `,
    )
    .in("id", clientIds);

  if (error) {
    console.error("Error fetching quotes:", error);
    return <div>Error loading quotes data</div>;
  }

  // Transform the data to flatten nested info
  const transformedQuotes: QuoteData[] = (quotes || []).map((quote) => {
    const client = clientsData?.find((c) => c.id === quote.client_id);
    return {
      ...quote,
      workshop: {
        id: quote.workshops?.id || "",
        name: quote.workshops?.name || "Unknown Workshop",
      },
      provider: {
        id: quote.provider_organizations?.id || "",
        name: quote.provider_organizations?.name || "Unknown Provider",
      },
      client: {
        id: client?.id || quote.client_id,
        company_name: client?.company_name || "Unknown Company",
        profile: {
          full_name: client?.profiles?.full_name || null,
          email: client?.profiles?.email || "",
        },
      },
      chat_room: {
        id: quote.chat_rooms?.id || "",
        created_at: quote.chat_rooms?.created_at || "",
      },
    };
  });

  const getPriceDisplay = (quote: QuoteData) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: quote.currency || "USD",
    }).format(quote.price);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "paid":
        return "default";
      case "pending":
        return "secondary";
      case "rejected":
        return "destructive";
      case "expired":
        return "secondary";
      case "cancelled":
        return "destructive";
      case "superceded":
        return "secondary";
      default:
        return "secondary";
    }
  };

  const isExpired = (quote: QuoteData) => {
    if (!quote.expires_at) return false;
    return new Date(quote.expires_at) < new Date();
  };

  const getTimeToQuote = (quote: QuoteData) => {
    if (!quote.chat_room.created_at) return "Unknown";
    const chatStart = new Date(quote.chat_room.created_at);
    const quoteCreated = new Date(quote.created_at);
    const diffMs = quoteCreated.getTime() - chatStart.getTime();
    const diffHours = Math.round(diffMs / (1000 * 60 * 60));

    if (diffHours < 1) return "<1h";
    if (diffHours < 24) return `${diffHours}h`;
    return `${Math.round(diffHours / 24)}d`;
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        {transformedQuotes.length} quotes found
      </div>

      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[150px]">Quote</TableHead>
              <TableHead className="w-[150px]">Client</TableHead>
              <TableHead className="w-[150px]">Workshop</TableHead>
              <TableHead className="w-[120px]">Provider</TableHead>
              <TableHead className="w-[100px]">Price</TableHead>
              <TableHead className="w-[100px]">Status</TableHead>
              <TableHead className="w-[120px]">Proposed Date</TableHead>
              <TableHead className="w-[100px]">Response Time</TableHead>
              <TableHead className="w-[120px]">Created</TableHead>
              <TableHead className="w-[80px]">Issues</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transformedQuotes.map((quote) => {
              const issues = [];

              if (isExpired(quote) && quote.status === "pending")
                issues.push("Expired");
              if (!quote.notes) issues.push("No notes");
              if (
                quote.status === "pending" &&
                new Date(quote.created_at) <
                  new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
              ) {
                issues.push("Old pending");
              }

              return (
                <TableRow key={quote.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold text-gray-900">
                        Quote #{quote.id.slice(0, 8)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {quote.location}
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {quote.client.company_name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {quote.client.profile.full_name || "No contact name"}
                      </div>
                      <div className="text-xs text-gray-500">
                        {quote.client.profile.email}
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {quote.workshop.name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {quote.workshop.id.slice(0, 8)}...
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="text-sm">{quote.provider.name}</div>
                  </TableCell>

                  <TableCell>
                    <div className="text-sm font-medium">
                      {getPriceDisplay(quote)}
                    </div>
                  </TableCell>

                  <TableCell>
                    <Badge
                      variant={getStatusBadgeVariant(quote.status)}
                      className="text-xs"
                    >
                      {quote.status}
                    </Badge>
                    {isExpired(quote) && quote.status === "pending" && (
                      <Badge
                        variant="destructive"
                        className="mt-1 block text-xs"
                      >
                        Expired
                      </Badge>
                    )}
                  </TableCell>

                  <TableCell>
                    <div className="text-xs">
                      {formatDate(
                        Temporal.Instant.from(quote.proposed_datetime),
                      )}
                      <br />
                      {formatTime(
                        Temporal.Instant.from(quote.proposed_datetime),
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <Badge
                      variant={
                        getTimeToQuote(quote).includes("d")
                          ? "destructive"
                          : "default"
                      }
                      className="text-xs"
                    >
                      {getTimeToQuote(quote)}
                    </Badge>
                  </TableCell>

                  <TableCell>
                    <div className="text-xs text-gray-500">
                      {formatDistanceToNow(new Date(quote.created_at), {
                        addSuffix: true,
                      })}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      {issues.length === 0 ? (
                        <Badge variant="default" className="text-xs">
                          ✓ Good
                        </Badge>
                      ) : (
                        issues.map((issue, index) => (
                          <Badge
                            key={index}
                            variant="destructive"
                            className="mb-1 block text-xs"
                          >
                            {issue}
                          </Badge>
                        ))
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
