import { formatDistanceToNow } from "date-fns";

import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { createClient } from "@/utils/supabase/server";

interface WorkshopData {
  id: string;
  name: string;
  description: string | null;
  format: string;
  duration: string;
  price: number;
  currency: string;
  pricing_model: string;
  published: boolean;
  min_capacity: number | null;
  max_capacity: number | null;
  location: string | null;
  venue_type: string;
  created_at: string;
  provider: {
    id: string;
    name: string;
  };
  quotes: {
    id: string;
    status: string;
  }[];
  categories: {
    categories: {
      name: string;
    };
  }[];
}

export default async function AdminWorkshopsTable() {
  const supabase = await createClient();

  const { data: workshops, error } = await supabase
    .from("workshops")
    .select(
      `
      id,
      name,
      description,
      format,
      duration,
      price,
      currency,
      pricing_model,
      published,
      min_capacity,
      max_capacity,
      location,
      venue_type,
      created_at,
      provider_organizations!provider_id (
        id,
        name
      ),
      quotes:quotes!workshop_id (
        id,
        status
      ),
      workshop_categories (
        categories (
          name
        )
      )
    `,
    )
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching workshops:", error);
    return <div>Error loading workshops data</div>;
  }

  // Transform the data to flatten provider info
  const transformedWorkshops: WorkshopData[] = (workshops || []).map(
    (workshop) => ({
      ...workshop,
      provider: {
        id: workshop.provider_organizations?.id || "",
        name: workshop.provider_organizations?.name || "Unknown Provider",
      },
      categories: workshop.workshop_categories || [],
    }),
  );

  const getCapacityDisplay = (workshop: WorkshopData) => {
    if (workshop.min_capacity && workshop.max_capacity) {
      return `${workshop.min_capacity}-${workshop.max_capacity}`;
    }
    if (workshop.max_capacity) {
      return `Up to ${workshop.max_capacity}`;
    }
    return "Not specified";
  };

  const getPriceDisplay = (workshop: WorkshopData) => {
    const price = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: workshop.currency || "USD",
    }).format(workshop.price);

    const model = workshop.pricing_model === "per_person" ? "/person" : "total";
    return `${price} ${model}`;
  };

  const getQuoteStats = (quotes: WorkshopData["quotes"]) => {
    const total = quotes.length;
    const pending = quotes.filter((q) => q.status === "pending").length;
    const paid = quotes.filter((q) => q.status === "paid").length;
    return { total, pending, paid };
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        {transformedWorkshops.length} workshops found
      </div>

      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Workshop</TableHead>
              <TableHead className="w-[150px]">Provider</TableHead>
              <TableHead className="w-[100px]">Format</TableHead>
              <TableHead className="w-[100px]">Duration</TableHead>
              <TableHead className="w-[120px]">Pricing</TableHead>
              <TableHead className="w-[100px]">Capacity</TableHead>
              <TableHead className="w-[100px]">Status</TableHead>
              <TableHead className="w-[100px]">Quotes</TableHead>
              <TableHead className="w-[120px]">Created</TableHead>
              <TableHead className="w-[80px]">Issues</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transformedWorkshops.map((workshop) => {
              const { total, pending, paid } = getQuoteStats(workshop.quotes);
              const issues = [];

              if (!workshop.published) issues.push("Not published");
              if (!workshop.description) issues.push("No description");
              if (!workshop.min_capacity && !workshop.max_capacity)
                issues.push("No capacity");
              if (!workshop.location && workshop.venue_type !== "online")
                issues.push("No location");
              if (workshop.categories.length === 0)
                issues.push("No categories");

              return (
                <TableRow key={workshop.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold text-gray-900">
                        {workshop.name}
                      </div>
                      <div className="font-mono text-xs text-gray-500">
                        {workshop.id.slice(0, 8)}...
                      </div>
                      {workshop.categories.length > 0 && (
                        <div className="mt-1 text-xs text-gray-500">
                          {workshop.categories.map((cat, index) => (
                            <span key={index} className="mr-1 inline-block">
                              #{cat.categories.name}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="text-sm">{workshop.provider.name}</div>
                  </TableCell>

                  <TableCell>
                    <Badge
                      variant={
                        workshop.format === "online"
                          ? "default"
                          : workshop.format === "hybrid"
                            ? "secondary"
                            : "outline"
                      }
                      className="text-xs"
                    >
                      {workshop.format}
                    </Badge>
                  </TableCell>

                  <TableCell>
                    <div className="text-sm">{workshop.duration}</div>
                  </TableCell>

                  <TableCell>
                    <div className="text-sm font-medium">
                      {getPriceDisplay(workshop)}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="text-sm">
                      {getCapacityDisplay(workshop)}
                    </div>
                  </TableCell>

                  <TableCell>
                    <Badge
                      variant={workshop.published ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {workshop.published ? "Published" : "Draft"}
                    </Badge>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{total} total</div>
                      {pending > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {pending} pending
                        </Badge>
                      )}
                      {paid > 0 && (
                        <Badge variant="default" className="text-xs">
                          {paid} paid
                        </Badge>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="text-xs text-gray-500">
                      {formatDistanceToNow(new Date(workshop.created_at), {
                        addSuffix: true,
                      })}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      {issues.length === 0 ? (
                        <Badge variant="default" className="text-xs">
                          ✓ Complete
                        </Badge>
                      ) : (
                        issues.slice(0, 2).map((issue, index) => (
                          <Badge
                            key={index}
                            variant="destructive"
                            className="mb-1 block text-xs"
                          >
                            {issue}
                          </Badge>
                        ))
                      )}
                      {issues.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{issues.length - 2} more
                        </div>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
