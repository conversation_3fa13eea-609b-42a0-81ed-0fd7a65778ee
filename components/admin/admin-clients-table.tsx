import { formatDistanceToNow } from "date-fns";

import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { createClient } from "@/utils/supabase/server";

interface ClientData {
  id: string;
  company_name: string;
  location: string | null;
  created_at: string;
  profile: {
    full_name: string | null;
    email: string;
    phone_number: string | null;
  };
  quotes: {
    id: string;
    status: string;
    created_at: string;
  }[];
  chat_rooms: {
    id: string;
    created_at: string;
  }[];
}

export default async function AdminClientsTable() {
  const supabase = await createClient();

  const { data: clients, error } = await supabase
    .from("clients")
    .select(
      `
      id,
      company_name,
      location,
      created_at,
      profiles!id (
        full_name,
        email,
        phone_number
      )
    `,
    )
    .order("created_at", { ascending: false });

  // Fetch quotes separately using client_id foreign key
  const { data: quotesData } = await supabase
    .from("quotes")
    .select("client_id, id, status, created_at");

  // Fetch chat rooms separately using client_id foreign key
  const { data: chatRoomsData } = await supabase
    .from("chat_rooms")
    .select("client_id, id, created_at");

  if (error) {
    console.error("Error fetching clients:", error);
    return <div>Error loading clients data</div>;
  }

  // Transform the data to flatten profile info and add quotes/chat rooms
  const transformedClients: ClientData[] = (clients || []).map((client) => ({
    ...client,
    profile: {
      full_name: client.profiles?.full_name || null,
      email: client.profiles?.email || "",
      phone_number: client.profiles?.phone_number || null,
    },
    quotes: quotesData?.filter((q) => q.client_id === client.id) || [],
    chat_rooms: chatRoomsData?.filter((cr) => cr.client_id === client.id) || [],
  }));

  const getEngagementLevel = (client: ClientData) => {
    const quotesCount = client.quotes.length;
    const chatRoomsCount = client.chat_rooms.length;

    if (quotesCount > 0) return "High";
    if (chatRoomsCount > 0) return "Medium";
    return "Low";
  };

  const getQuoteStats = (quotes: ClientData["quotes"]) => {
    const total = quotes.length;
    const pending = quotes.filter((q) => q.status === "pending").length;
    const paid = quotes.filter((q) => q.status === "paid").length;
    return { total, pending, paid };
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        {transformedClients.length} client companies found
      </div>

      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Company</TableHead>
              <TableHead className="w-[200px]">Contact Person</TableHead>
              <TableHead className="w-[150px]">Location</TableHead>
              <TableHead className="w-[120px]">Quotes</TableHead>
              <TableHead className="w-[100px]">Chat Rooms</TableHead>
              <TableHead className="w-[100px]">Engagement</TableHead>
              <TableHead className="w-[120px]">Registered</TableHead>
              <TableHead className="w-[80px]">Issues</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transformedClients.map((client) => {
              const { total, pending, paid } = getQuoteStats(client.quotes);
              const engagementLevel = getEngagementLevel(client);
              const issues = [];

              if (!client.profile.full_name) issues.push("No contact name");
              if (!client.profile.phone_number) issues.push("No phone");
              if (!client.location) issues.push("No location");
              if (client.chat_rooms.length === 0) issues.push("No activity");

              return (
                <TableRow key={client.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold text-gray-900">
                        {client.company_name}
                      </div>
                      <div className="font-mono text-xs text-gray-500">
                        {client.id.slice(0, 8)}...
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">
                        {client.profile.full_name || (
                          <span className="italic text-gray-400">No name</span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        {client.profile.email}
                      </div>
                      {client.profile.phone_number && (
                        <div className="text-xs text-gray-500">
                          {client.profile.phone_number}
                        </div>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="text-sm">
                      {client.location || (
                        <span className="italic text-gray-400">
                          Not specified
                        </span>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{total} total</div>
                      {pending > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {pending} pending
                        </Badge>
                      )}
                      {paid > 0 && (
                        <Badge variant="default" className="text-xs">
                          {paid} paid
                        </Badge>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="text-sm font-medium">
                      {client.chat_rooms.length}
                    </div>
                  </TableCell>

                  <TableCell>
                    <Badge
                      variant={
                        engagementLevel === "High"
                          ? "default"
                          : engagementLevel === "Medium"
                            ? "secondary"
                            : "outline"
                      }
                      className="text-xs"
                    >
                      {engagementLevel}
                    </Badge>
                  </TableCell>

                  <TableCell>
                    <div className="text-xs text-gray-500">
                      {formatDistanceToNow(new Date(client.created_at), {
                        addSuffix: true,
                      })}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      {issues.length === 0 ? (
                        <Badge variant="default" className="text-xs">
                          ✓ Complete
                        </Badge>
                      ) : (
                        issues.slice(0, 2).map((issue, index) => (
                          <Badge
                            key={index}
                            variant="destructive"
                            className="mb-1 block text-xs"
                          >
                            {issue}
                          </Badge>
                        ))
                      )}
                      {issues.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{issues.length - 2} more
                        </div>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
