import { formatDistanceToNow } from "date-fns";

import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { createClient } from "@/utils/supabase/server";

interface ProviderData {
  id: string;
  name: string;
  description: string | null;
  city: string | null;
  country: string | null;
  location: string | null;
  created_at: string;
  provider_users: {
    id: string;
    full_name: string | null;
    email: string;
    role: string | null;
  }[];
  workshops: {
    id: string;
    name: string;
    published: boolean;
  }[];
}

export default async function AdminProvidersTable() {
  const supabase = await createClient();

  const { data: providers, error } = await supabase
    .from("provider_organizations")
    .select(
      `
      id,
      name,
      description,
      city,
      country,
      location,
      created_at,
      providers:providers!organization_id (
        id,
        role,
        profiles:profiles!id (
          full_name,
          email
        )
      ),
      workshops:workshops!provider_id (
        id,
        name,
        published
      )
    `,
    )
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching providers:", error);
    return <div>Error loading providers data</div>;
  }

  // Transform the data to flatten provider user info
  const transformedProviders: ProviderData[] = (providers || []).map(
    (provider) => ({
      ...provider,
      provider_users: (provider.providers ?? []).map((p) => ({
        id: p.id,
        full_name: p.profiles?.full_name || null,
        email: p.profiles?.email || "",
        role: p.role,
      })),
    }),
  );

  const getLocationDisplay = (provider: ProviderData) => {
    if (provider.location) return provider.location;
    if (provider.city && provider.country)
      return `${provider.city}, ${provider.country}`;
    if (provider.city) return provider.city;
    if (provider.country) return provider.country;
    return "Not specified";
  };

  const getWorkshopStats = (workshops: ProviderData["workshops"]) => {
    const total = workshops.length;
    const published = workshops.filter((w) => w.published).length;
    return { total, published };
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600">
        {transformedProviders.length} provider organizations found
      </div>

      <div className="rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Organization</TableHead>
              <TableHead className="w-[150px]">Location</TableHead>
              <TableHead className="w-[200px]">Users</TableHead>
              <TableHead className="w-[120px]">Workshops</TableHead>
              <TableHead className="w-[300px]">Description</TableHead>
              <TableHead className="w-[120px]">Created</TableHead>
              <TableHead className="w-[80px]">Issues</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transformedProviders.map((provider) => {
              const { total, published } = getWorkshopStats(provider.workshops);
              const issues = [];

              if (provider.provider_users.length === 0) issues.push("No users");
              if (total === 0) issues.push("No workshops");
              if (total > 0 && published === 0)
                issues.push("No published workshops");
              if (!provider.description) issues.push("No description");
              if (getLocationDisplay(provider) === "Not specified")
                issues.push("No location");

              return (
                <TableRow key={provider.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold text-gray-900">
                        {provider.name}
                      </div>
                      <div className="font-mono text-xs text-gray-500">
                        {provider.id.slice(0, 8)}...
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="text-sm">
                      {getLocationDisplay(provider)}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      {provider.provider_users.length === 0 ? (
                        <Badge variant="destructive" className="text-xs">
                          No users
                        </Badge>
                      ) : (
                        provider.provider_users.map((user) => (
                          <div key={user.id} className="text-xs">
                            <div className="font-medium">
                              {user.full_name || "No name"}
                            </div>
                            <div className="text-gray-500">
                              {user.email} ({user.role || "No role"})
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{total} total</div>
                      <div className="text-xs">
                        <Badge
                          variant={published > 0 ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {published} published
                        </Badge>
                      </div>
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="max-w-[300px] truncate text-sm text-gray-600">
                      {provider.description || (
                        <span className="italic text-gray-400">
                          No description provided
                        </span>
                      )}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="text-xs text-gray-500">
                      {formatDistanceToNow(new Date(provider.created_at), {
                        addSuffix: true,
                      })}
                    </div>
                  </TableCell>

                  <TableCell>
                    <div className="space-y-1">
                      {issues.length === 0 ? (
                        <Badge variant="default" className="text-xs">
                          ✓ Complete
                        </Badge>
                      ) : (
                        issues.map((issue, index) => (
                          <Badge
                            key={index}
                            variant="destructive"
                            className="mb-1 block text-xs"
                          >
                            {issue}
                          </Badge>
                        ))
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
