import { Badge } from "@/components/ui/badge";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Section } from "@/components/ui/section";
import { createClient } from "@/utils/supabase/server";

interface FunnelData {
  totalProviders: number;
  providersWithWorkshops: number;
  providersWithPublishedWorkshops: number;
  totalClients: number;
  clientsWithChats: number;
  clientsWithQuotes: number;
  clientsWithPaidQuotes: number;
  totalWorkshops: number;
  publishedWorkshops: number;
  workshopsWithQuotes: number;
  totalQuotes: number;
  pendingQuotes: number;
  paidQuotes: number;
  expiredQuotes: number;
}

interface MissingInfoSummary {
  providersWithoutDescription: number;
  providersWithoutLocation: number;
  providersWithoutPhone: number;
  workshopsWithoutDescription: number;
  workshopsWithoutLocation: number;
  workshopsWithoutCapacity: number;
  clientsWithoutPhone: number;
  clientsWithoutLocation: number;
  quotesWithoutNotes: number;
}

export default async function AdminFunnelAnalysis() {
  const supabase = await createClient();

  // Get funnel data
  const [
    providerStats,
    clientStats,
    workshopStats,
    quoteStats,
    missingInfoStats,
  ] = await Promise.all([
    // Provider funnel stats
    Promise.all([
      supabase.from("providers").select("*", { count: "exact", head: true }),
      supabase
        .from("provider_organizations")
        .select("id", { count: "exact", head: true })
        .in(
          "id",
          (await supabase.from("workshops").select("provider_id")).data?.map(
            (w) => w.provider_id,
          ) || [],
        ),
      supabase
        .from("provider_organizations")
        .select("id", { count: "exact", head: true })
        .in(
          "id",
          (
            await supabase
              .from("workshops")
              .select("provider_id")
              .eq("published", true)
          ).data?.map((w) => w.provider_id) || [],
        ),
    ]),

    // Client funnel stats
    Promise.all([
      supabase.from("clients").select("*", { count: "exact", head: true }),
      supabase
        .from("clients")
        .select("id", { count: "exact", head: true })
        .in(
          "id",
          (await supabase.from("chat_rooms").select("client_id")).data?.map(
            (cr) => cr.client_id,
          ) || [],
        ),
      supabase
        .from("clients")
        .select("id", { count: "exact", head: true })
        .in(
          "id",
          (await supabase.from("quotes").select("client_id")).data?.map(
            (q) => q.client_id,
          ) || [],
        ),
      supabase
        .from("clients")
        .select("id", { count: "exact", head: true })
        .in(
          "id",
          (
            await supabase
              .from("quotes")
              .select("client_id")
              .eq("status", "paid")
          ).data?.map((q) => q.client_id) || [],
        ),
    ]),

    // Workshop stats
    Promise.all([
      supabase.from("workshops").select("*", { count: "exact", head: true }),
      supabase
        .from("workshops")
        .select("*", { count: "exact", head: true })
        .eq("published", true),
      supabase
        .from("workshops")
        .select("id", { count: "exact", head: true })
        .in(
          "id",
          (await supabase.from("quotes").select("workshop_id")).data?.map(
            (q) => q.workshop_id,
          ) || [],
        ),
    ]),

    // Quote stats
    Promise.all([
      supabase.from("quotes").select("*", { count: "exact", head: true }),
      supabase
        .from("quotes")
        .select("*", { count: "exact", head: true })
        .eq("status", "pending"),
      supabase
        .from("quotes")
        .select("*", { count: "exact", head: true })
        .eq("status", "paid"),
      supabase
        .from("quotes")
        .select("*", { count: "exact", head: true })
        .eq("status", "expired"),
    ]),

    // Missing information stats
    Promise.all([
      supabase
        .from("provider_organizations")
        .select("*", { count: "exact", head: true })
        .is("description", null),
      supabase
        .from("provider_organizations")
        .select("*", { count: "exact", head: true })
        .is("city", null)
        .is("country", null)
        .is("location", null),
      supabase
        .from("profiles")
        .select("*", { count: "exact", head: true })
        .eq("user_type", "provider")
        .is("phone_number", null),
      supabase
        .from("workshops")
        .select("*", { count: "exact", head: true })
        .is("description", null),
      supabase
        .from("workshops")
        .select("*", { count: "exact", head: true })
        .is("location", null),
      supabase
        .from("workshops")
        .select("*", { count: "exact", head: true })
        .is("min_capacity", null)
        .is("max_capacity", null),
      supabase
        .from("profiles")
        .select("*", { count: "exact", head: true })
        .eq("user_type", "client")
        .is("phone_number", null),
      supabase
        .from("clients")
        .select("*", { count: "exact", head: true })
        .is("location", null),
      supabase
        .from("quotes")
        .select("*", { count: "exact", head: true })
        .is("notes", null),
    ]),
  ]);

  const funnelData: FunnelData = {
    totalProviders: providerStats[0].count || 0,
    providersWithWorkshops: providerStats[1].count || 0,
    providersWithPublishedWorkshops: providerStats[2].count || 0,
    totalClients: clientStats[0].count || 0,
    clientsWithChats: clientStats[1].count || 0,
    clientsWithQuotes: clientStats[2].count || 0,
    clientsWithPaidQuotes: clientStats[3].count || 0,
    totalWorkshops: workshopStats[0].count || 0,
    publishedWorkshops: workshopStats[1].count || 0,
    workshopsWithQuotes: workshopStats[2].count || 0,
    totalQuotes: quoteStats[0].count || 0,
    pendingQuotes: quoteStats[1].count || 0,
    paidQuotes: quoteStats[2].count || 0,
    expiredQuotes: quoteStats[3].count || 0,
  };

  const missingInfo: MissingInfoSummary = {
    providersWithoutDescription: missingInfoStats[0].count || 0,
    providersWithoutLocation: missingInfoStats[1].count || 0,
    providersWithoutPhone: missingInfoStats[2].count || 0,
    workshopsWithoutDescription: missingInfoStats[3].count || 0,
    workshopsWithoutLocation: missingInfoStats[4].count || 0,
    workshopsWithoutCapacity: missingInfoStats[5].count || 0,
    clientsWithoutPhone: missingInfoStats[6].count || 0,
    clientsWithoutLocation: missingInfoStats[7].count || 0,
    quotesWithoutNotes: missingInfoStats[8].count || 0,
  };

  const calculateRate = (numerator: number, denominator: number) => {
    if (denominator === 0) return 0;
    return Math.round((numerator / denominator) * 100);
  };

  return (
    <Section spacing="loose">
      <Card>
        <CardHeader>
          <CardTitle>Funnel Analysis</CardTitle>
          <CardDescription>
            Track user progression through the platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {/* Provider Funnel */}
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Provider Funnel</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Total Providers</span>
                  <Badge>{funnelData.totalProviders}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>→ With Workshops</span>
                  <div className="flex gap-2">
                    <Badge variant="secondary">
                      {calculateRate(
                        funnelData.providersWithWorkshops,
                        funnelData.totalProviders,
                      )}
                      %
                    </Badge>
                    <Badge>{funnelData.providersWithWorkshops}</Badge>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span>→ With Published Workshops</span>
                  <div className="flex gap-2">
                    <Badge variant="secondary">
                      {calculateRate(
                        funnelData.providersWithPublishedWorkshops,
                        funnelData.totalProviders,
                      )}
                      %
                    </Badge>
                    <Badge>{funnelData.providersWithPublishedWorkshops}</Badge>
                  </div>
                </div>
              </div>
            </div>

            {/* Client Funnel */}
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Client Funnel</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Total Clients</span>
                  <Badge>{funnelData.totalClients}</Badge>
                </div>
                <div className="flex justify-between">
                  <span>→ Started Conversations</span>
                  <div className="flex gap-2">
                    <Badge variant="secondary">
                      {calculateRate(
                        funnelData.clientsWithChats,
                        funnelData.totalClients,
                      )}
                      %
                    </Badge>
                    <Badge>{funnelData.clientsWithChats}</Badge>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span>→ Received Quotes</span>
                  <div className="flex gap-2">
                    <Badge variant="secondary">
                      {calculateRate(
                        funnelData.clientsWithQuotes,
                        funnelData.totalClients,
                      )}
                      %
                    </Badge>
                    <Badge>{funnelData.clientsWithQuotes}</Badge>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span>→ Paid for Services</span>
                  <div className="flex gap-2">
                    <Badge variant="default">
                      {calculateRate(
                        funnelData.clientsWithPaidQuotes,
                        funnelData.totalClients,
                      )}
                      %
                    </Badge>
                    <Badge>{funnelData.clientsWithPaidQuotes}</Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Missing Information Issues</CardTitle>
          <CardDescription>
            Profile completeness issues that may affect conversions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            {/* Provider Issues */}
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Provider Issues</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">No Description</span>
                  <Badge variant="destructive">
                    {missingInfo.providersWithoutDescription}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">No Location</span>
                  <Badge variant="destructive">
                    {missingInfo.providersWithoutLocation}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">No Phone</span>
                  <Badge variant="destructive">
                    {missingInfo.providersWithoutPhone}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Workshop Issues */}
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Workshop Issues</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">No Description</span>
                  <Badge variant="destructive">
                    {missingInfo.workshopsWithoutDescription}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">No Location Info</span>
                  <Badge variant="destructive">
                    {missingInfo.workshopsWithoutLocation}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">No Capacity Info</span>
                  <Badge variant="destructive">
                    {missingInfo.workshopsWithoutCapacity}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Client Issues */}
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Client Issues</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">No Phone</span>
                  <Badge variant="destructive">
                    {missingInfo.clientsWithoutPhone}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">No Location</span>
                  <Badge variant="destructive">
                    {missingInfo.clientsWithoutLocation}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Quotes No Notes</span>
                  <Badge variant="destructive">
                    {missingInfo.quotesWithoutNotes}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Quote Conversion Analysis</CardTitle>
          <CardDescription>
            Understanding where quotes are getting stuck
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{funnelData.totalQuotes}</div>
              <div className="text-sm text-gray-600">Total Quotes</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {funnelData.pendingQuotes}
              </div>
              <div className="text-sm text-gray-600">Pending</div>
              <div className="text-xs text-gray-500">
                {calculateRate(
                  funnelData.pendingQuotes,
                  funnelData.totalQuotes,
                )}
                %
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {funnelData.paidQuotes}
              </div>
              <div className="text-sm text-gray-600">Paid</div>
              <div className="text-xs text-gray-500">
                {calculateRate(funnelData.paidQuotes, funnelData.totalQuotes)}%
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {funnelData.expiredQuotes}
              </div>
              <div className="text-sm text-gray-600">Expired</div>
              <div className="text-xs text-gray-500">
                {calculateRate(
                  funnelData.expiredQuotes,
                  funnelData.totalQuotes,
                )}
                %
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Section>
  );
}
