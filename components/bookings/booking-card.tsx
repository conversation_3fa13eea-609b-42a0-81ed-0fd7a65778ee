"use client";

import {
  CalendarIcon,
  ClockIcon,
  CreditCardIcon,
  ExternalLinkIcon,
  MapPinIcon,
  UserIcon,
} from "lucide-react";
import Image from "next/image";
import { Temporal } from "temporal-polyfill";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { IconText } from "@/components/ui/icon-text";
import { formatCurrency } from "@/lib/format";
import { formatDate, formatTime } from "@/lib/temporal-utils";
import { cn } from "@/lib/utils";
import type { Quote } from "@/types/quote";

// Booking data type based on paid quotes with relations
export type BookingData = Quote & {
  workshops: {
    id: string;
    name: string;
    format: string;
    prerequisites?: string | null;
    image_url?: string | null;
    provider_organizations: {
      id: string;
      name: string;
      profile_photo_url?: string | null;
    } | null;
  } | null;
  profiles?:
    | {
        full_name: string | null;
      }
    | null
    | { full_name: string | null }[];
  clients?: {
    id: string;
    company_name: string | null;
    profiles: {
      full_name: string;
    } | null;
  } | null;
};

interface BookingCardProps {
  booking: BookingData;
  viewType: "provider" | "client";
  className?: string;
}

/**
 * BookingCard component displays a full-width booking with workshop and booking details
 * - Desktop: Two-column layout (booking details left, workshop details right)
 * - Mobile: Stacked layout (booking details top, workshop details bottom)
 */
export function BookingCard({
  booking,
  viewType,
  className,
}: BookingCardProps) {
  const workshop = booking.workshops;
  const client = booking.clients;
  const profile = booking.profiles;
  const organization = workshop?.provider_organizations;

  // Create workshop URL
  const workshopUrl = workshop ? `/workshops/${workshop.id}` : "#";

  // Format date and time
  const bookingDate = formatDate(
    Temporal.Instant.from(booking.proposed_datetime),
  );
  const bookingTime = formatTime(
    Temporal.Instant.from(booking.proposed_datetime),
  );

  return (
    <Card
      className={cn(
        "w-full border-l-4 border-l-emerald-500 shadow-sm",
        className,
      )}
    >
      <CardContent className="p-6">
        <div className="flex flex-col gap-6 sm:flex-row sm:items-start sm:justify-between">
          {/* Left side: Booking Details */}
          <div className="flex-1 space-y-4">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-xl font-semibold text-foreground">
                  {workshop?.name || "Workshop"}
                </h3>
                {viewType === "provider" && (client || profile) && (
                  <div className="mt-1 flex items-center gap-2">
                    <IconText
                      icon={UserIcon}
                      text={
                        <span className="text-muted-foreground">
                          {client?.profiles?.full_name ||
                            (Array.isArray(profile)
                              ? profile[0]?.full_name
                              : profile?.full_name) ||
                            "Client"}
                          {client?.company_name && (
                            <span className="font-medium">
                              {" "}
                              · {client.company_name}
                            </span>
                          )}
                        </span>
                      }
                      spacing="tight"
                      className="text-sm"
                    />
                  </div>
                )}
                {viewType === "client" && organization && (
                  <div className="mt-1 flex items-center gap-2">
                    <Avatar className="h-5 w-5">
                      <AvatarImage
                        src={organization.profile_photo_url || undefined}
                        alt={organization.name}
                        width={20}
                        height={20}
                      />
                      <AvatarFallback className="text-xs">
                        {organization.name[0]}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm text-muted-foreground">
                      {organization.name}
                    </span>
                  </div>
                )}
              </div>
              <Badge className="bg-emerald-100 text-emerald-800 hover:bg-emerald-100">
                Confirmed
              </Badge>
            </div>

            <div className="grid gap-3 sm:grid-cols-2">
              <IconText
                icon={CalendarIcon}
                text={bookingDate}
                className="text-sm text-muted-foreground [&>svg]:text-blue-500"
              />
              <IconText
                icon={ClockIcon}
                text={bookingTime}
                className="text-sm text-muted-foreground [&>svg]:text-amber-500"
              />
              <IconText
                icon={MapPinIcon}
                text={booking.location}
                className="text-sm text-muted-foreground [&>svg]:text-green-500"
              />
              <IconText
                icon={CreditCardIcon}
                text={
                  <span className="font-semibold text-foreground">
                    {formatCurrency(booking.price, booking.currency)}
                  </span>
                }
                className="text-sm [&>svg]:text-purple-500"
              />
            </div>

            {booking.notes && (
              <div className="mt-4 rounded-md bg-muted/50 p-3">
                <p className="whitespace-pre-line text-sm text-muted-foreground">
                  {booking.notes}
                </p>
              </div>
            )}
          </div>

          {/* Right side: Workshop Details & Actions */}
          <div className="flex-shrink-0 space-y-4 sm:w-80">
            {workshop && (
              <div className="rounded-lg border bg-muted/30 p-4">
                <div className="flex items-center justify-between gap-3">
                  <div className="flex-1">
                    <h4 className="font-medium text-foreground">
                      Workshop Details
                    </h4>
                    {organization && (
                      <div className="mt-2 flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage
                            src={organization.profile_photo_url || undefined}
                            alt={organization.name}
                            width={24}
                            height={24}
                          />
                          <AvatarFallback className="text-xs">
                            {organization.name[0]}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs text-muted-foreground">
                          by {organization.name}
                        </span>
                      </div>
                    )}
                    {workshop.prerequisites && (
                      <div className="mt-3">
                        <p className="text-xs font-medium text-muted-foreground">
                          Prerequisites:
                        </p>
                        <p className="whitespace-pre-line text-xs text-muted-foreground">
                          {workshop.prerequisites}
                        </p>
                      </div>
                    )}
                  </div>
                  {workshop.image_url && (
                    <div className="h-16 w-16 overflow-hidden rounded-md bg-muted">
                      <Image
                        src={workshop.image_url}
                        alt={workshop.name}
                        width={64}
                        height={64}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  )}
                </div>

                <div className="mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                    className="w-full"
                  >
                    <a href={workshopUrl}>
                      <IconText
                        icon={ExternalLinkIcon}
                        text="View Workshop Details"
                        spacing="tight"
                      />
                    </a>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
