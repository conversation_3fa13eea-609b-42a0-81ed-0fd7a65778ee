import { BookingCard } from "@/components/bookings/booking-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CardSectionTitle } from "@/components/ui/card-section-title";
import { Section } from "@/components/ui/section";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import type { Database } from "@/types/database.types";
import { createClient } from "@/utils/supabase/server";

type ClientBookingQuote = Database["public"]["Tables"]["quotes"]["Row"] & {
  workshops: {
    id: string;
    name: string;
    format: string;
    prerequisites: string | null;
    image_url: string | null;
    provider_organizations: {
      id: string;
      name: string;
      profile_photo_url: string | null;
    } | null;
  };
};

interface ClientBookingsSectionProps {
  userId: string;
}

export async function ClientBookingsSection({
  userId,
}: ClientBookingsSectionProps) {
  const supabase = await createClient();

  // Get paid quotes (bookings) for this client with workshop and provider details
  const { data: bookingsData, error } = await supabase
    .from("quotes")
    .select(
      `
      *,
      workshops!inner(
        id,
        name,
        format,
        prerequisites,
        image_url,
        provider_organizations(id, name, profile_photo_url)
      )
    `,
    )
    .eq("client_id", userId)
    .eq("status", "paid")
    .order("proposed_datetime", { ascending: false });

  if (error) {
    console.error("Error fetching client bookings:", error);
    return (
      <div className="py-8 text-center">
        <p className="text-muted-foreground">Unable to load your bookings</p>
      </div>
    );
  }

  const bookings = (bookingsData || []) as ClientBookingQuote[];

  // Separate upcoming and past bookings
  const now = new Date();
  const upcomingBookings = bookings.filter(
    (booking) => new Date(booking.proposed_datetime) > now,
  );
  const pastBookings = bookings.filter(
    (booking) => new Date(booking.proposed_datetime) <= now,
  );

  return (
    <Tabs defaultValue="upcoming">
      <TabsList className="mb-6">
        <TabsTrigger value="upcoming">Upcoming Workshops</TabsTrigger>
        <TabsTrigger value="past">Past Workshops</TabsTrigger>
      </TabsList>

      <TabsContent value="upcoming">
        <Section>
          <CardSectionTitle>Your Upcoming Workshops</CardSectionTitle>

          {upcomingBookings.length > 0 ? (
            <div className="space-y-4">
              {upcomingBookings.map((booking) => (
                <BookingCard
                  key={booking.id}
                  booking={booking}
                  viewType="client"
                />
              ))}
            </div>
          ) : (
            <div className="rounded-lg border border-dashed p-8 text-center">
              <CardSectionTitle size="base">
                No upcoming workshops
              </CardSectionTitle>
              <p className="mt-2 text-sm text-muted-foreground">
                You haven&apos;t registered for any upcoming workshops yet.
              </p>
              <Button asChild className="mt-4 w-full">
                <a href="/workshops">Browse Workshops</a>
              </Button>
            </div>
          )}
        </Section>
      </TabsContent>

      <TabsContent value="past">
        <Section>
          <CardSectionTitle>Your Past Workshops</CardSectionTitle>

          {pastBookings.length > 0 ? (
            <div className="space-y-4">
              {pastBookings.map((booking) => (
                <BookingCard
                  key={booking.id}
                  booking={booking}
                  viewType="client"
                />
              ))}
            </div>
          ) : (
            <div className="rounded-lg border border-dashed p-8 text-center">
              <CardSectionTitle size="base">No past events</CardSectionTitle>
              <p className="mt-2 text-sm text-muted-foreground">
                You haven&apos;t attended any events yet.
              </p>
            </div>
          )}
        </Section>
      </TabsContent>
    </Tabs>
  );
}
