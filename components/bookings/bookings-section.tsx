import {
  BookingCard,
  type BookingData,
} from "@/components/bookings/booking-card";
import { requireProviderWithDetails } from "@/lib/auth";
import { createClient } from "@/utils/supabase/server";

export async function BookingsSection() {
  const { provider } = await requireProviderWithDetails();
  const supabase = await createClient();

  // Get paid quotes (bookings) for provider's workshops with all necessary relations
  const { data: bookingsData, error } = await supabase
    .from("quotes")
    .select(
      `
      *,
      workshops!inner(
        id,
        name,
        format,
        prerequisites,
        image_url,
        provider_organizations(id, name, profile_photo_url)
      )
    `,
    )
    .eq("workshops.provider_id", provider.organization_id)
    .eq("status", "paid")
    .order("proposed_datetime", { ascending: false });

  if (error) {
    console.error("Error fetching bookings:", error);
    return (
      <div className="py-8 text-center">
        <p className="text-muted-foreground">Unable to load bookings</p>
      </div>
    );
  }

  let bookings = (bookingsData || []) as Record<string, unknown>[];

  // If we have bookings, fetch client/profile information for each
  if (bookings.length > 0) {
    const clientIds = bookings.map((booking) => booking.client_id as string);

    // Get client and profile information
    const { data: clientsData } = await supabase
      .from("clients")
      .select(
        `
        id,
        company_name,
        profiles!inner(full_name)
      `,
      )
      .in("id", clientIds);

    // Also get profiles directly for client_ids not in clients table
    const { data: profilesData } = await supabase
      .from("profiles")
      .select("id, full_name")
      .in("id", clientIds);

    // Merge client and profile data into bookings
    bookings = bookings.map((booking) => {
      const clientId = booking.client_id as string;

      // First try to find in clients table
      const clientData = clientsData?.find((c) => c.id === clientId);
      if (clientData) {
        return {
          ...booking,
          clients: clientData,
        };
      }

      // Fallback to profiles table
      const profileData = profilesData?.find((p) => p.id === clientId);
      if (profileData) {
        return {
          ...booking,
          profiles: profileData,
        };
      }

      return booking;
    });
  }

  if (bookings.length === 0) {
    return (
      <div className="py-12 text-center">
        <p className="text-muted-foreground">No confirmed bookings yet</p>
        <p className="mt-1 text-sm text-muted-foreground">
          Bookings will appear here when clients pay for quotes
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {bookings.map((booking) => (
        <BookingCard
          key={booking.id as string}
          booking={booking as BookingData}
          viewType="provider"
        />
      ))}
    </div>
  );
}
