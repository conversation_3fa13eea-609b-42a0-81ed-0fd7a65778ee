import { redirect } from "next/navigation";

import { RegistrationsTableServer } from "@/components/dashboard/registrations-table-server";
import { WorkshopCards } from "@/components/dashboard/workshops-cards";
import { OrganizationDetailsCard } from "@/components/profile/organization-details-card";
import { UserProfileCard } from "@/components/profile/user-profile-card";
import { Section } from "@/components/ui/section";
import type { Database } from "@/types/database.types";
import { createClient } from "@/utils/supabase/server";

// Define types for better type safety
type ProviderOrganization =
  Database["public"]["Tables"]["provider_organizations"]["Row"];

interface ProviderProfileProps {
  userId: string;
}

export async function ProviderProfile({ userId }: ProviderProfileProps) {
  const supabase = await createClient();

  const { data: profile } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", userId)
    .single();

  if (!profile) {
    redirect("/dashboard/edit");
  }

  const { data: provider } = await supabase
    .from("providers")
    .select("*")
    .eq("id", userId)
    .single();

  let organization: ProviderOrganization | null = null;

  if (provider?.organization_id) {
    const { data: orgData } = await supabase
      .from("provider_organizations")
      .select("*")
      .eq("id", provider.organization_id)
      .single();

    organization = orgData;
  }

  return (
    <div className="container py-10">
      <Section spacing="loose">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {/* Left side - User profile info */}
          <div className="md:col-span-1">
            <UserProfileCard profile={profile} dashboardMode />
          </div>

          {/* Right side - Provider organization details */}
          <div className="space-y-8 md:col-span-2">
            {organization && (
              <OrganizationDetailsCard organization={organization} />
            )}
          </div>
        </div>

        {/* Workshops and bookings sections */}
        <Section spacing="loose">
          {/* My Workshops Section */}
          <section>
            <h2 className="mb-4 text-2xl font-semibold tracking-tight">
              My Workshops
            </h2>
            <WorkshopCards />
          </section>

          {/* Bookings Section */}
          <section>
            <h2 className="mb-4 text-2xl font-semibold tracking-tight">
              Bookings
            </h2>
            <RegistrationsTableServer />
          </section>
        </Section>
      </Section>
    </div>
  );
}
