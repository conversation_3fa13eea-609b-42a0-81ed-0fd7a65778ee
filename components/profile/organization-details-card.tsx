import { Edit } from "lucide-react";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CardSectionTitle } from "@/components/ui/card-section-title";
import { IconText } from "@/components/ui/icon-text";
import { MissingFieldNotice } from "@/components/ui/missing-field-notice";
import { Section } from "@/components/ui/section";
import { StatusIndicator } from "@/components/ui/status-indicator";
import type { Database } from "@/types/database.types";

type ProviderOrganization =
  Database["public"]["Tables"]["provider_organizations"]["Row"];

interface OrganizationDetailsCardProps {
  organization: ProviderOrganization;
}

export function OrganizationDetailsCard({
  organization,
}: OrganizationDetailsCardProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Avatar className="h-16 w-16">
                <AvatarImage
                  src={organization.profile_photo_url}
                  alt={organization.name}
                  width={64}
                  height={64}
                />
                <AvatarFallback>
                  {organization.name.charAt(0) || "O"}
                </AvatarFallback>
              </Avatar>
              {!organization.profile_photo_url && (
                <div className="absolute bottom-1.5 right-1.5">
                  <StatusIndicator status="missing" />
                </div>
              )}
            </div>
            <div>
              <CardTitle>{organization.name}</CardTitle>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <OrganizationContent organization={organization} />
      </CardContent>
    </Card>
  );
}

function OrganizationContent({
  organization,
}: {
  organization: ProviderOrganization;
}) {
  const hasLocation = organization.location;

  return (
    <Section
      spacing="normal"
      className="sm:flex sm:items-end sm:justify-between sm:gap-4"
    >
      <div className="flex-1">
        <CardSectionTitle spacing="sm">Description</CardSectionTitle>
        {organization.description ? (
          <p className="whitespace-pre-line">{organization.description}</p>
        ) : (
          <MissingFieldNotice>Not provided</MissingFieldNotice>
        )}
        <CardSectionTitle spacing="sm">Location</CardSectionTitle>
        {hasLocation ? (
          <p className="mt-1">{organization.location}</p>
        ) : (
          <MissingFieldNotice>Address not provided</MissingFieldNotice>
        )}
        <p className="mt-1">
          {organization.city}, {organization.country}
        </p>
      </div>
      <div className="mt-4 sm:mt-0 sm:w-auto sm:flex-shrink-0">
        <Button asChild variant="outline" className="w-full sm:w-auto">
          <a href="/dashboard/organization">
            <IconText icon={Edit} text="Edit Organisation" />
          </a>
        </Button>
      </div>
    </Section>
  );
}
