"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { AlertCircle } from "lucide-react";
import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { updateProfile } from "@/app/(loggedin)/profile/actions";
import { DeleteProfileButton } from "@/components/profile/delete-profile-button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { FormActions } from "@/components/ui/form-actions";
import { Input } from "@/components/ui/input";
import { LoadingButton } from "@/components/ui/loading-button";
import { Section } from "@/components/ui/section";
import type { Database } from "@/types/database.types";

type Profile = Database["public"]["Tables"]["profiles"]["Row"];

interface ProfileFormProps {
  profile: Profile;
  dashboardMode: boolean;
}

const formSchema = z.object({
  full_name: z
    .string()
    .min(2, { message: "Name must be at least 2 characters" }),
  phone_number: z.string().optional().or(z.literal("")),
});

export function ProfileForm({ profile, dashboardMode }: ProfileFormProps) {
  const passwordUrl = dashboardMode
    ? "/dashboard/password"
    : "/profile/password";
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      full_name: profile.full_name || "",
      phone_number: profile.phone_number || "",
    },
  });

  async function handleSubmit(values: z.infer<typeof formSchema>) {
    setError(null);

    startTransition(async () => {
      // Pass individual parameters to the server action
      const result = await updateProfile(
        profile.id,
        values.full_name,
        values.phone_number,
      );

      // If we get here, it means the redirect didn't happen, which indicates an error
      if (result?.error) {
        setError(result.error);
      }
    });
  }

  return (
    <Section spacing="loose">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <FormField
            control={form.control}
            name="full_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormDescription>Your contact phone number</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="mt-4 flex flex-col space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0">
            <Button
              type="button"
              variant="outline"
              asChild
              className="w-full sm:w-auto"
            >
              <a href={passwordUrl}>Change Password</a>
            </Button>
            <DeleteProfileButton />
          </div>

          <FormActions>
            <Button type="button" variant="outline" asChild>
              <a href={dashboardMode ? "/dashboard" : "/profile"}>Cancel</a>
            </Button>
            <LoadingButton
              type="submit"
              loading={isPending}
              loadingText="Saving..."
            >
              Save Changes
            </LoadingButton>
          </FormActions>
        </form>
      </Form>
    </Section>
  );
}
