import { Edit } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CardSectionTitle } from "@/components/ui/card-section-title";
import { IconText } from "@/components/ui/icon-text";
import { MissingFieldNotice } from "@/components/ui/missing-field-notice";
import { Section } from "@/components/ui/section";
import type { Database } from "@/types/database.types";

type Profile = Database["public"]["Tables"]["profiles"]["Row"];

interface UserProfileCardProps {
  profile: Profile;
  dashboardMode: boolean;
}

export function UserProfileCard({
  profile,
  dashboardMode,
}: UserProfileCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{profile.full_name}</CardTitle>
        <CardDescription>{profile.email}</CardDescription>
      </CardHeader>
      <CardContent>
        <Section spacing="normal">
          <CardSectionTitle as="h3">Phone</CardSectionTitle>
          <div className="mt-1">
            {profile.phone_number ? (
              <p>{profile.phone_number}</p>
            ) : (
              <MissingFieldNotice>Not provided</MissingFieldNotice>
            )}
          </div>

          <Button asChild variant="outline" className="w-full">
            <a href={dashboardMode ? "/dashboard/edit" : "/profile/edit"}>
              <IconText icon={Edit} text="Edit Profile" />
            </a>
          </Button>
        </Section>
      </CardContent>
    </Card>
  );
}
