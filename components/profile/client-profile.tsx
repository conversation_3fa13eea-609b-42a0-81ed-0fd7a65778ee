import { redirect } from "next/navigation";

import { UserProfileCard } from "@/components/profile/user-profile-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CardSectionTitle } from "@/components/ui/card-section-title";
import { Section } from "@/components/ui/section";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { createClient } from "@/utils/supabase/server";

interface ClientProfileProps {
  userId: string;
}

export async function ClientProfile({ userId }: ClientProfileProps) {
  const supabase = await createClient();

  const { data: profile } = await supabase
    .from("profiles")
    .select("*")
    .eq("id", userId)
    .single();

  if (!profile) {
    redirect("/profile/edit");
  }

  const { data: bookings } = await supabase
    .from("bookings")
    .select("*, workshops(*)")
    .eq("client_id", userId)
    .eq("status", "confirmed")
    .order("created_at", { ascending: false });

  return (
    <div className="container my-10 grid grid-cols-1 gap-8 md:grid-cols-3">
      {/* Left side - User profile info */}
      <div className="md:col-span-1">
        <UserProfileCard profile={profile} dashboardMode={false} />
      </div>

      {/* Right side - Client's workshops */}
      <div className="md:col-span-2">
        <Tabs defaultValue="upcoming">
          <TabsList className="mb-6">
            <TabsTrigger value="upcoming">Upcoming Workshops</TabsTrigger>
            <TabsTrigger value="past">Past Workshops</TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming">
            <Section>
              <CardSectionTitle>Your Upcoming Workshops</CardSectionTitle>

              {bookings &&
              bookings.filter((b) => new Date(b.booking_datetime) > new Date())
                .length > 0 ? (
                <div className="grid gap-4 sm:grid-cols-2">
                  {bookings
                    .filter((b) => new Date(b.booking_datetime) > new Date())
                    .map((booking) => (
                      <Card key={booking.id}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-lg">
                            {booking.workshops.name}
                          </CardTitle>
                          <CardDescription>
                            {new Date(
                              booking.booking_datetime,
                            ).toLocaleDateString()}{" "}
                            at{" "}
                            {new Date(
                              booking.booking_datetime,
                            ).toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="text-sm">
                            <p className="font-medium">
                              {booking.workshops.location ||
                                "Location to be arranged"}
                            </p>
                            <div className="mt-2 flex justify-between">
                              <span>
                                Participants: {booking.participant_count}
                              </span>
                              <span className="font-medium">
                                ${booking.total_price.toFixed(2)}
                              </span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              ) : (
                <div className="rounded-lg border border-dashed p-8 text-center">
                  <CardSectionTitle size="base">
                    No upcoming workshops
                  </CardSectionTitle>
                  <p className="mt-2 text-sm text-muted-foreground">
                    You haven&apos;t registered for any upcoming workshops yet.
                  </p>
                  <Button asChild className="mt-4 w-full">
                    <a href="/workshops">Browse Workshops</a>
                  </Button>
                </div>
              )}
            </Section>
          </TabsContent>

          <TabsContent value="past">
            <Section>
              <CardSectionTitle>Your Past Workshops</CardSectionTitle>

              {bookings &&
              bookings.filter((b) => new Date(b.booking_datetime) <= new Date())
                .length > 0 ? (
                <div className="grid gap-4 sm:grid-cols-2">
                  {bookings
                    .filter((b) => new Date(b.booking_datetime) <= new Date())
                    .map((booking) => (
                      <Card key={booking.id}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-lg">
                            {booking.workshops.name}
                          </CardTitle>
                          <CardDescription>
                            {new Date(
                              booking.booking_datetime,
                            ).toLocaleDateString()}{" "}
                            at{" "}
                            {new Date(
                              booking.booking_datetime,
                            ).toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="text-sm">
                            <p className="font-medium">
                              {booking.workshops.location ||
                                "Location details unavailable"}
                            </p>
                            <div className="mt-2 flex justify-between">
                              <span>
                                Participants: {booking.participant_count}
                              </span>
                              <span className="font-medium">
                                ${booking.total_price.toFixed(2)}
                              </span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              ) : (
                <div className="rounded-lg border border-dashed p-8 text-center">
                  <CardSectionTitle size="base">
                    No past events
                  </CardSectionTitle>
                  <p className="mt-2 text-sm text-muted-foreground">
                    You haven&apos;t attended any events yet.
                  </p>
                </div>
              )}
            </Section>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
