"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, X } from "lucide-react";
import Image from "next/image";
import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { updateProviderOrganization } from "@/app/(loggedin)/profile/actions";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { CityCountrySelect } from "@/components/ui/city-country-select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { FormActions } from "@/components/ui/form-actions";
import { Input } from "@/components/ui/input";
import { LoadingButton } from "@/components/ui/loading-button";
import { Section } from "@/components/ui/section";
import { Textarea } from "@/components/ui/textarea";
import { UploadDropzone } from "@/components/ui/upload-thing";
import type { Database } from "@/types/database.types";

type Provider = Database["public"]["Tables"]["providers"]["Row"];
type ProviderOrganization =
  Database["public"]["Tables"]["provider_organizations"]["Row"];

interface ProviderOrganizationFormProps {
  provider: Provider;
  organization: ProviderOrganization;
}

const formSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Organisation name must be at least 2 characters" }),
  description: z.string().optional().or(z.literal("")),
  city: z.string().optional().or(z.literal("")),
  country: z.string().or(z.literal("")),
  location: z.string().optional().or(z.literal("")),
  profile_photo_url: z.string().optional().or(z.literal("")),
});

export function ProviderOrganizationForm({
  provider,
  organization,
}: ProviderOrganizationFormProps) {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: organization.name,
      description: organization.description || "",
      city: organization.city || "",
      country: organization.country || "",
      location: organization.location || "",
      profile_photo_url: organization.profile_photo_url || "",
    },
  });

  async function handleSubmit(values: z.infer<typeof formSchema>) {
    setError(null);

    startTransition(async () => {
      // Pass individual parameters to the server action
      const result = await updateProviderOrganization(
        provider.organization_id,
        values.name,
        values.description || "",
        values.city || "",
        values.country || "",
        values.location || "",
        values.profile_photo_url || "",
      );

      // If we get here, it means the redirect didn't happen, which indicates an error
      if (result?.error) {
        setError(result.error);
      }
    });
  }

  return (
    <Section spacing="loose">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleSubmit)}
          className="grid grid-cols-1 gap-4 sm:grid-cols-2"
        >
          {error && (
            <div className="sm:order-0 sm:col-span-2">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            </div>
          )}

          <div className="sm:order-2">
            <FormField
              control={form.control}
              name="profile_photo_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organisation Logo</FormLabel>
                  <FormControl>
                    <div className="space-y-4">
                      {field.value ? (
                        <div className="relative h-40 w-40 overflow-hidden rounded-md border border-border">
                          <div className="absolute right-2 top-2 z-10">
                            <Button
                              type="button"
                              variant="destructive"
                              size="icon"
                              onClick={() => field.onChange("")}
                              disabled={isPending}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                          <Image
                            src={field.value}
                            alt="Organization logo"
                            fill
                            className="object-cover"
                          />
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <UploadDropzone
                            endpoint="organizationProfilePhoto"
                            onUploadBegin={() => {
                              setUploadError(null);
                            }}
                            onClientUploadComplete={(res) => {
                              if (res && res[0]) {
                                field.onChange(res[0].url);
                              }
                            }}
                            onUploadError={(error) => {
                              // Handle specific error types
                              if (error.message.includes("FileSizeMismatch")) {
                                setUploadError(
                                  "File size exceeds the 4MB limit. Please upload a smaller image.",
                                );
                              } else {
                                setUploadError(
                                  `Upload failed: ${error.message}`,
                                );
                              }
                            }}
                            config={{ mode: "auto", appendOnPaste: true }}
                            className="h-40 w-40 text-wrap ut-button:bg-primary ut-button:text-primary-foreground ut-button:hover:bg-primary/90 ut-label:w-32"
                          />
                          {uploadError && (
                            <Alert variant="destructive" className="mb-4">
                              <AlertCircle className="h-4 w-4" />
                              <AlertDescription>{uploadError}</AlertDescription>
                            </Alert>
                          )}
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="sm:order-1">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organisation Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organisation Description</FormLabel>
                  <FormControl>
                    <Textarea className="min-h-32 resize-none" {...field} />
                  </FormControl>
                  <FormDescription>
                    Describe your organisation, mission, and services
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="city"
              render={({ field: cityField }) => (
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field: countryField }) => (
                    <CityCountrySelect
                      onCityChange={cityField.onChange}
                      onCountryChange={countryField.onChange}
                      cityValue={cityField.value}
                      countryValue={countryField.value}
                      label="Location"
                      placeholder="Select a location"
                      description="Select the location where your business operates"
                    />
                  )}
                />
              )}
            />
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organisation Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Full address (street, city, postal code, etc.)"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter your organisation&apos;s full address
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormActions className="sm:order-3 sm:col-span-2">
            <Button type="button" variant="outline" asChild>
              <a href="/profile">Cancel</a>
            </Button>
            <LoadingButton
              type="submit"
              loading={isPending}
              loadingText="Saving..."
            >
              Save Changes
            </LoadingButton>
          </FormActions>
        </form>
      </Form>
    </Section>
  );
}
