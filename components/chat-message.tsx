import { Temporal } from "temporal-polyfill";

import { formatTime } from "@/lib/temporal-utils";
import { cn } from "@/lib/utils";
import type { ChatMessage } from "@/types/chat";
import type { QuoteWithWorkshopDetails } from "@/types/quote";

import { QuoteCard } from "./quote-card";

interface ChatMessageItemProps {
  message: ChatMessage;
  isOwnMessage: boolean;
  isClient: boolean;
  isOptimistic?: boolean;
  onDuplicateQuote?: (quote: QuoteWithWorkshopDetails) => void;
}

export const ChatMessageItem = ({
  message,
  isOwnMessage,
  isClient,
  isOptimistic = false,
  onDuplicateQuote,
}: ChatMessageItemProps) => {
  const isSystemMessage = !message.user?.id;

  // For system messages, we'll use a centered style with different background
  if (isSystemMessage) {
    return (
      <div className="mt-2 flex justify-center">
        <div className="w-fit max-w-[80%] rounded-xl bg-slate-100 px-4 py-2 text-center text-xs text-slate-600">
          {message.content}
          <div className="mt-1 text-[10px] text-slate-400">
            {formatTime(Temporal.Instant.from(message.createdAt))}
          </div>
        </div>
      </div>
    );
  }

  // Regular user message
  return (
    <div className="mt-2 flex flex-col gap-1">
      <div className="flex items-center gap-2 px-3 text-xs">
        <span className="font-medium">{message.user.name}</span>
        <span className="text-xs text-foreground/50">
          {formatTime(Temporal.Instant.from(message.createdAt))}
          {isOptimistic && " (sending...)"}
        </span>
      </div>

      <div
        className={cn(
          "w-full whitespace-pre-line rounded-lg border border-border p-4 text-sm",
          isOwnMessage ? "bg-muted/50" : "bg-transparent",
          isOptimistic && "opacity-70",
        )}
      >
        {message.content}

        {message.quotes && (
          <QuoteCard
            quote={message.quotes}
            isOwnMessage={isOwnMessage}
            isClient={isClient}
            onDuplicate={onDuplicateQuote}
          />
        )}
      </div>
    </div>
  );
};
