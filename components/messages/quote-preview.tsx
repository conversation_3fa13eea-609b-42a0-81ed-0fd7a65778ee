"use client";

import { Temporal } from "temporal-polyfill";

import type { QuoteFormClientInput } from "@/components/messages/quote-form";
import { QuoteCard } from "@/components/quote-card";

interface QuotePreviewProps {
  quote: QuoteFormClientInput;
  workshopName: string;
  organizationName: string;
}

/**
 * Renders a preview of a draft quote using the provided quote details, workshop name, and organization name.
 *
 * Constructs a draft quote object from the input data, normalizing the proposed date-time to UTC, and displays it in a formatted preview card.
 *
 * @param quote - The input data for the quote preview
 * @param workshopName - The name of the workshop associated with the quote
 * @param organizationName - The name of the provider organization associated with the quote
 */
export function QuotePreview({
  quote,
  workshopName,
  organizationName,
}: QuotePreviewProps) {
  const plainDateTime = Temporal.PlainDateTime.from(quote.proposedDatetime);
  const timeZone = Temporal.Now.timeZoneId();
  const zonedDateTime = plainDateTime.toZonedDateTime(timeZone);
  const utcDatetime = zonedDateTime.toInstant().toString();
  const draftQuote = {
    id: "draft",
    workshop_id: quote.workshopId,
    chat_room_id: "",
    proposed_datetime: utcDatetime,
    location: quote.location,
    price: quote.price,
    currency: quote.currency,
    notes: quote.notes || "",
    status: "pending",
    created_at: Temporal.Now.instant().toString(),
    updated_at: Temporal.Now.instant().toString(),
    expires_at: null,
    fee: null,
    paid_at: null,
    payment_intent_id: null,
    provider_earnings: null,
    // Required fields that might not be in the data
    provider_organization_id: "",
    client_id: "",
    workshops: {
      name: workshopName,
    },
    provider_organizations: {
      name: organizationName,
    },
  } as const;

  return (
    <div className="mx-4 mb-2 mt-4 space-y-2">
      <h3 className="text-lg font-semibold">Preview Quote</h3>
      <QuoteCard
        quote={draftQuote}
        isOwnMessage={true}
        isClient={false}
        isDraft={true}
      />
    </div>
  );
}
