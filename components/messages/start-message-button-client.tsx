"use client";

import { MessageSquarePlus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useTransition } from "react";
import { toast } from "sonner";

import { getOrCreateChatRoom } from "@/app/enquiries/actions";
import { IconText } from "@/components/ui/icon-text";
import { LoadingButton } from "@/components/ui/loading-button";

interface StartMessageButtonClientProps {
  workshopId: string;
}

export function StartMessageButtonClient({
  workshopId,
}: StartMessageButtonClientProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  const handleStartChat = () => {
    setError(null);
    startTransition(async () => {
      const result = await getOrCreateChatRoom(workshopId);
      if (result.error) {
        setError(result.error);
        toast.error(`Failed to start messaging: ${result.error}`);
        console.error("Failed to start messaging:", result.error);
      } else if (result.roomId) {
        router.push(`/enquiries/${result.roomId}`);
      } else {
        setError("Could not retrieve chat room ID.");
        toast.error("An unknown error occurred while starting messaging.");
        console.error(
          "Failed to start messaging: Unknown error, no room ID returned.",
        );
      }
    });
  };

  return (
    <>
      <LoadingButton
        onClick={handleStartChat}
        loading={isPending}
        loadingText="Starting Conversation..."
        className="mt-4 w-full"
      >
        <IconText icon={MessageSquarePlus} text="Enquire to Book" />
      </LoadingButton>
      {error && <p className="mt-2 text-sm text-destructive">{error}</p>}
    </>
  );
}
