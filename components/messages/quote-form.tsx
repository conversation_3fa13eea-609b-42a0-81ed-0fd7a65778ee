"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Pencil } from "lucide-react";
import { useImperativeHandle, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Temporal } from "temporal-polyfill";
import { z } from "zod";

import { QuotePreview } from "@/components/messages/quote-preview";
import { Button } from "@/components/ui/button";
import { FieldGroup } from "@/components/ui/field-group";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { adjustProposedDateTime } from "@/lib/quote-utils";
import type { QuoteWithWorkshopDetails } from "@/types/quote";

// Client-side schema for the form
export const QuoteFormClientSchema = z.object({
  workshopId: z.string().uuid({ message: "Please select a workshop." }),
  // TODO: could this be a z.date instead?
  proposedDatetime: z.string().refine(
    (val) => {
      try {
        Temporal.PlainDateTime.from(val);
        return true;
      } catch {
        return false;
      }
    },
    {
      message:
        "Invalid proposed date and time. Please use YYYY-MM-DDTHH:mm format.",
    },
  ),
  location: z
    .string()
    .min(1, { message: "Location cannot be empty." })
    .max(255, { message: "Location is too long (max 255 characters)." }),
  price: z.coerce
    .number()
    .positive({ message: "Price must be a positive number." }),
  currency: z.string(),
  notes: z
    .string()
    .max(1000, { message: "Notes are too long (max 1000 characters)." })
    .optional(),
});

export type QuoteFormClientInput = z.infer<typeof QuoteFormClientSchema>;

interface QuoteFormProps {
  ref?: React.Ref<QuoteFormHandle>;
  workshopId: string;
  workshopName: string;
  workshopCurrency: string;
  organizationName: string;
  onQuoteSent: (data: QuoteFormClientInput) => Promise<void>;
}

type FormState = "form" | "preview";

export interface QuoteFormHandle {
  duplicateQuote: (quote: QuoteWithWorkshopDetails) => void;
}

/**
 * Renders a form for preparing, previewing, and sending a workshop quote.
 *
 * Allows users to enter quote details, preview the quote before sending, and submit the quote asynchronously. Supports duplicating an existing quote via an imperative handle. The form enforces validation and manages transitions between editing and preview states.
 *
 * @returns The quote form UI with preview and send functionality.
 */
export function QuoteForm({
  ref,
  workshopId,
  workshopName,
  workshopCurrency,
  organizationName,
  onQuoteSent,
}: QuoteFormProps) {
  const [formState, setFormState] = useState<FormState>("form");
  const [quoteData, setQuoteData] = useState<QuoteFormClientInput | null>(null);

  const form = useForm<QuoteFormClientInput>({
    resolver: zodResolver(QuoteFormClientSchema),
    defaultValues: {
      workshopId,
      proposedDatetime: "",
      location: "",
      price: 0,
      currency: workshopCurrency,
      notes: "",
    },
  });

  // Expose imperative handle for quote duplication
  useImperativeHandle(
    ref,
    () => ({
      duplicateQuote: (quote: QuoteWithWorkshopDetails) => {
        const formattedDateTime = adjustProposedDateTime(
          quote.proposed_datetime,
        );

        const formData: QuoteFormClientInput = {
          workshopId: quote.workshop_id,
          proposedDatetime: formattedDateTime,
          location: quote.location,
          price: quote.price,
          currency: quote.currency,
          notes: quote.notes || "",
        };

        const validationResult = QuoteFormClientSchema.safeParse(formData);
        if (!validationResult.success) {
          toast.error("Invalid quote data for duplication");
          return;
        }

        form.reset(formData);
        setQuoteData(formData);
        setFormState("form");
      },
    }),
    [form],
  );

  const handlePreview = (data: QuoteFormClientInput) => {
    setQuoteData(data);
    setFormState("preview");
  };

  const handleEdit = () => {
    setFormState("form");
  };

  const handleSendQuote = async () => {
    if (!quoteData) return;

    try {
      await onQuoteSent(quoteData);

      // Reset form after successful send
      form.reset({
        workshopId,
        proposedDatetime: "",
        location: "",
        price: 0,
        currency: workshopCurrency,
        notes: "",
      });
      setQuoteData(null);
      setFormState("form");
    } catch {
      toast.error("Failed to send quote");
    }
  };

  if (formState === "preview" && quoteData) {
    return (
      <div className="space-y-3">
        <QuotePreview
          quote={quoteData}
          workshopName={workshopName}
          organizationName={organizationName}
        />
        <div className="flex justify-end gap-2">
          <Button variant="ghost" size="sm" onClick={handleEdit} type="button">
            <Pencil className="mr-1 size-4" />
            Edit
          </Button>
          <Button size="sm" onClick={handleSendQuote}>
            Send Quote
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handlePreview)}>
        <FieldGroup>
          <div className="w-full">
            <h3 className="text-lg font-semibold">Prepare Quote</h3>
            <div className="grid gap-4 sm:grid-cols-[1fr,2fr]">
              <FormField
                control={form.control}
                name="proposedDatetime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date & Time</FormLabel>
                    <FormControl>
                      <Input type="datetime-local" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Office address or Virtual"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="number"
                          placeholder="500"
                          className="pr-12"
                          {...field}
                        />
                        <div className="absolute right-0 top-0 flex h-10 items-center pr-3 text-sm text-muted-foreground">
                          {workshopCurrency}
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional details or terms"
                        className="resize-none"
                        rows={2}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex justify-end">
            <Button type="submit" size="sm">
              Preview
            </Button>
          </div>
        </FieldGroup>
      </form>
    </Form>
  );
}
