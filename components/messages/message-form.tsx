"use client";

import type { KeyboardEvent } from "react";
import { useCallback, useEffect } from "react";

import { LoadingButton } from "@/components/ui/loading-button";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";

interface MessageFormProps {
  messageRef: React.RefObject<HTMLTextAreaElement | null>;
  onSendMessage: (e: React.FormEvent) => void;
  onKeyDown: (e: KeyboardEvent<HTMLTextAreaElement>) => void;
  isConnected: boolean;
  isPending: boolean;
}

/**
 * Renders a message input form with a dynamically resizing textarea and a send button.
 *
 * The textarea automatically adjusts its height to fit its content unless the browser supports the CSS `field-sizing: content` property. The send button and textarea are disabled when not connected or when a message send operation is pending.
 */
export function MessageForm({
  messageRef,
  onSendMessage,
  onKeyDown,
  isConnected,
  isPending,
}: MessageFormProps) {
  const adjustTextareaHeight = useCallback(() => {
    const textarea = messageRef.current;
    if (!textarea) return;

    const supportsFieldSizing = CSS.supports?.("field-sizing", "content");
    if (supportsFieldSizing) return;

    textarea.style.height = "auto";
    textarea.style.height = `${textarea.scrollHeight}px`;
  }, [messageRef]);

  useEffect(() => {
    const textarea = messageRef.current;
    if (!textarea) return;

    const handleInput = () => adjustTextareaHeight();
    textarea.addEventListener("input", handleInput);
    adjustTextareaHeight();

    return () => {
      textarea.removeEventListener("input", handleInput);
    };
  }, [messageRef, adjustTextareaHeight]);

  useEffect(() => {
    adjustTextareaHeight();
  }, [adjustTextareaHeight]);

  return (
    <form onSubmit={onSendMessage} className="space-y-3">
      <Textarea
        ref={messageRef}
        className={cn(
          "min-h-[2.5rem] w-full resize-none overflow-hidden rounded-xl bg-background px-4 py-2 text-sm transition-all duration-300 [field-sizing:content]",
        )}
        onKeyDown={onKeyDown}
        placeholder="Type a message..."
        disabled={!isConnected}
        rows={2}
      />
      <div className="flex justify-end">
        <LoadingButton
          type="submit"
          size="sm"
          loading={isPending}
          disabled={!isConnected}
        >
          Send
        </LoadingButton>
      </div>
    </form>
  );
}
