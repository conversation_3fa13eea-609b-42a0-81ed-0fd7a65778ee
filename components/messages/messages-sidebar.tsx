"use client";

import { formatDistance } from "date-fns";
import { usePathname } from "next/navigation";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { StatusIndicator } from "@/components/ui/status-indicator";
import { cn } from "@/lib/utils";

type MessageRoom = {
  id: string;
  updated_at: string;
  workshop_title?: string | null;
  other_participant_name?: string | null;
  other_participant_avatar?: string | null;
  has_unread: boolean;
};

interface MessagesSidebarProps {
  chatRooms: MessageRoom[];
  className?: string;
  isProvider: boolean;
}

/**
 * Displays a sidebar listing message chat rooms with participant details and recent activity.
 *
 * Renders a scrollable list of chat rooms, each showing the participant's avatar (if available), name, last updated time, and workshop title. If no chat rooms are present, displays a contextual message based on the user's provider status.
 *
 * @param chatRooms - Array of chat room objects to display in the sidebar
 * @param className - Optional additional CSS classes for the sidebar container
 * @param isProvider - Indicates whether the current user is a provider, affecting empty state messaging
 */
export function MessagesSidebar({
  chatRooms,
  className,
  isProvider,
}: MessagesSidebarProps) {
  const pathname = usePathname();

  if (!chatRooms || chatRooms.length === 0) {
    return (
      <div className={cn("w-full", className)}>
        <div className="p-4">
          <CardTitle className="mb-2 px-2 tracking-tight md:hidden">
            Enquiries
          </CardTitle>
          <div className="flex flex-col items-center justify-center text-balance py-8 text-center text-muted-foreground">
            {isProvider ? (
              <p>Enquiry threads from clients will appear here</p>
            ) : (
              <p>Enquiries to providers will appear here</p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex h-full w-full flex-col", className)}>
      <CardTitle className="mb-2 p-4 tracking-tight md:hidden">
        Enquiries
      </CardTitle>
      <ScrollArea className="flex-1 space-y-1 p-2">
        {chatRooms.map((chat) => {
          const isActive = pathname === `/enquiries/${chat.id}`;
          return (
            <a
              key={chat.id}
              href={`/enquiries/${chat.id}`}
              className={cn(
                "flex gap-3 rounded-md px-3 py-2 transition-colors hover:bg-secondary/80",
                isActive && "bg-secondary/80",
              )}
            >
              {chat.other_participant_avatar && (
                <Avatar className="h-10 w-10 shrink-0 border">
                  <AvatarImage
                    src={chat.other_participant_avatar}
                    alt={
                      chat.other_participant_name || "Chat participant avatar"
                    }
                    width={40}
                    height={40}
                  />
                  <AvatarFallback>
                    {(chat.other_participant_name || "?")
                      .substring(0, 2)
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              )}
              <div className="flex flex-col gap-1">
                <div className="flex w-full items-center justify-start gap-1">
                  <div className="flex items-center gap-2">
                    <span
                      className={cn(
                        "line-clamp-1",
                        chat.has_unread ? "font-bold" : "font-medium",
                      )}
                    >
                      {chat.other_participant_name || "Unknown"}
                    </span>
                    {chat.has_unread && (
                      <StatusIndicator status="missing" size="sm" />
                    )}
                  </div>
                </div>
                <div
                  className={cn(
                    "line-clamp-1 text-sm text-muted-foreground",
                    chat.has_unread ? "font-bold" : "font-medium",
                  )}
                >
                  {chat.workshop_title || "Workshop Messages"}
                </div>
                <div className="text-xs text-muted-foreground">
                  {formatDistance(new Date(chat.updated_at), new Date(), {
                    addSuffix: true,
                  })}
                </div>
              </div>
            </a>
          );
        })}
      </ScrollArea>
    </div>
  );
}
