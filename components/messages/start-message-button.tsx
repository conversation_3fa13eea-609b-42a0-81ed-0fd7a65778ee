import { getUserProfile } from "@/lib/auth";

import { StartMessageButtonClient } from "./start-message-button-client";

interface StartMessageButtonProps {
  workshopId: string;
  currentUserId: string | undefined;
}

export async function StartMessageButton({
  workshopId,
  currentUserId,
}: StartMessageButtonProps) {
  // Show only for logged in clients

  if (!currentUserId) {
    return null;
  }
  const profile = await getUserProfile(currentUserId);
  const userRole = profile?.user_type ?? null;

  if (userRole === "provider") {
    return null;
  }

  return <StartMessageButtonClient workshopId={workshopId} />;
}
