"use client";

import type { KeyboardEvent } from "react";
import { useImperativeHandle, useRef, useState } from "react";

import { MessageForm } from "@/components/messages/message-form";
import {
  QuoteForm,
  type QuoteFormClientInput,
  type QuoteF<PERSON><PERSON>andle,
} from "@/components/messages/quote-form";
import { Switch } from "@/components/ui/switch";
import type { QuoteWithWorkshopDetails } from "@/types/quote";

interface ChatInputAreaProps {
  ref?: React.Ref<ChatInputAreaHandle>;
  messageRef: React.RefObject<HTMLTextAreaElement | null>;
  onSendMessage: (e: React.FormEvent) => void;
  onKeyDown: (e: KeyboardEvent<HTMLTextAreaElement>) => void;
  createQuote: (
    data: QuoteFormClientInput,
  ) => Promise<{ success: boolean; error?: string }>;
  workshopId: string;
  workshopName: string;
  workshopCurrency: string;
  organizationName: string;
  isClient: boolean;
  isConnected: boolean;
  isPending: boolean;
}

export interface ChatInputAreaHandle {
  duplicateQuote: (quote: QuoteWithWorkshopDetails) => void;
}

/**
 * Renders a chat input area that allows users to switch between sending messages and creating quotes.
 *
 * Provides a toggle for switching modes (message or quote), and conditionally renders the appropriate form based on the selected mode. Exposes an imperative handle for duplicating quotes.
 */
export function ChatInputArea({
  ref,
  messageRef,
  onSendMessage,
  onKeyDown,
  createQuote,
  workshopId,
  workshopName,
  workshopCurrency,
  organizationName,
  isClient,
  isConnected,
  isPending,
}: ChatInputAreaProps) {
  const [mode, setMode] = useState<"message" | "quote">("message");
  const quoteFormRef = useRef<QuoteFormHandle>(null);

  useImperativeHandle(
    ref,
    () => ({
      duplicateQuote: (quote: QuoteWithWorkshopDetails) => {
        setMode("quote"); // Switch to quote mode when duplicating
        queueMicrotask(() => {
          quoteFormRef.current?.duplicateQuote(quote);
        });
      },
    }),
    [],
  );

  const handleQuoteSent = async (data: QuoteFormClientInput) => {
    const result = await createQuote(data);

    if (result.success) {
      if (messageRef.current && mode === "message") {
        messageRef.current.value = "";
      }
      setMode("message");
    }
  };

  return (
    <div className="relative m-4 space-y-3 rounded-lg border border-border bg-muted/50 p-4 shadow-md">
      {/* Mode Switch - positioned in bottom-left corner */}
      {!isClient && (
        <div className="absolute bottom-4 left-4 z-10 flex items-center gap-2 rounded-lg px-2 py-1 text-sm">
          <span>Message</span>
          <Switch
            checked={mode === "quote"}
            onCheckedChange={(checked: boolean) =>
              setMode(checked ? "quote" : "message")
            }
          />
          <span>Quote</span>
        </div>
      )}

      {/* Form Content */}
      {mode === "message" ? (
        <MessageForm
          messageRef={messageRef}
          onSendMessage={onSendMessage}
          onKeyDown={onKeyDown}
          isConnected={isConnected}
          isPending={isPending}
        />
      ) : (
        <QuoteForm
          ref={quoteFormRef}
          workshopId={workshopId}
          workshopName={workshopName}
          workshopCurrency={workshopCurrency}
          organizationName={organizationName}
          onQuoteSent={handleQuoteSent}
        />
      )}
    </div>
  );
}
