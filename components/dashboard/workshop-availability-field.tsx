import React from "react";

import { Checkbox } from "@/components/ui/checkbox";
import {
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import type { DayOfWeek, WorkshopDailyAvailability } from "@/types/types";

// Use the database type for consistency
export type DailyAvailability = WorkshopDailyAvailability;

// Define the type for the days of week array items
type DayOption = {
  value: DayOfWeek;
  label: string;
};

interface WorkshopAvailabilityFieldProps {
  value: DailyAvailability;
  onChange: (value: DailyAvailability) => void;
}

// Define days of week array inside the component file
const daysOfWeek: readonly DayOption[] = [
  { value: "monday", label: "Monday" },
  { value: "tuesday", label: "Tuesday" },
  { value: "wednesday", label: "Wednesday" },
  { value: "thursday", label: "Thursday" },
  { value: "friday", label: "Friday" },
  { value: "saturday", label: "Saturday" },
  { value: "sunday", label: "Sunday" },
] as const;

export function WorkshopAvailabilityField({
  value,
  onChange,
}: WorkshopAvailabilityFieldProps) {
  return (
    <FormItem>
      <FormLabel>Availability</FormLabel>
      <div className="grid grid-cols-[auto_1fr] gap-4">
        {daysOfWeek.map((day) => (
          <React.Fragment key={day.value}>
            <div className="col-start-1 col-end-1 flex items-center space-x-2">
              <Checkbox
                id={`day-${day.value}`}
                checked={value[day.value] !== null}
                onCheckedChange={(checked) => {
                  const newValue = { ...value };
                  if (checked) {
                    newValue[day.value] = {
                      start_time: "09:00",
                      end_time: "17:00",
                    };
                  } else {
                    newValue[day.value] = null;
                  }
                  onChange(newValue);
                }}
              />
              <label
                htmlFor={`day-${day.value}`}
                className="text-sm font-medium"
              >
                {day.label}
              </label>
            </div>
            <div className="flex h-7 items-center space-x-2">
              {value[day.value] && (
                <>
                  <Input
                    type="time"
                    value={value[day.value]?.start_time || ""}
                    onChange={(e) => {
                      const newValue = { ...value };
                      newValue[day.value] = {
                        ...newValue[day.value]!,
                        start_time: e.target.value,
                      };
                      onChange(newValue);
                    }}
                    className="w-24"
                  />
                  <span>to</span>
                  <Input
                    type="time"
                    value={value[day.value]?.end_time || ""}
                    onChange={(e) => {
                      const newValue = { ...value };
                      newValue[day.value] = {
                        ...newValue[day.value]!,
                        end_time: e.target.value,
                      };
                      onChange(newValue);
                    }}
                    className="w-24"
                  />
                </>
              )}
            </div>
          </React.Fragment>
        ))}
      </div>
      <FormDescription>
        Select the days and times when this workshop is available
      </FormDescription>
      <FormMessage />
    </FormItem>
  );
}
