import { Plus } from "lucide-react";

import { ProviderWorkshopCard } from "@/components/dashboard/provider-workshop-card";
import { Card, CardContent } from "@/components/ui/card";
import { requireProviderWithDetails } from "@/lib/auth";
import type {
  WorkshopWithCategories,
  WorkshopWithProvider,
} from "@/types/workshop";
import { createClient } from "@/utils/supabase/server";

export async function WorkshopCards() {
  const { provider } = await requireProviderWithDetails();
  const supabase = await createClient();

  const { data: workshopsData, error } = await supabase
    .from("workshops")
    .select(
      `
      *,
      provider_organizations!inner(
        id,
        name,
        profile_photo_url,
        city
      ),
      workshop_categories (
        category_id,
        categories (
          id,
          name
        )
      )
    `,
    )
    .eq("provider_id", provider.organization_id)
    .order("created_at", { ascending: false });

  if (error) {
    console.error("Error fetching workshops:", error);
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center text-destructive">
        <p className="mb-2 text-lg font-semibold">Error Loading Workshops</p>
        <p className="text-sm text-muted-foreground">
          There was a problem loading your workshops. Please try refreshing the
          page.
        </p>
      </div>
    );
  }

  const workshops = (workshopsData || []) as Array<
    WorkshopWithProvider & WorkshopWithCategories
  >;

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {/* Create Workshop Card */}
      <a href="/dashboard/workshops/new" className="block h-full">
        <Card className="group relative flex h-full flex-col overflow-hidden border-2 border-dashed bg-muted/10 transition-all duration-200 hover:border-primary hover:bg-muted/20 hover:shadow-md">
          {/* Mimicking the aspect ratio of workshop cards */}
          <CardContent className="flex flex-1 flex-col items-center justify-center p-6 text-center">
            <div className="rounded-full bg-primary/10 p-4 transition-transform group-hover:scale-110">
              <Plus className="h-8 w-8 text-primary" />
            </div>
            <h3 className="mt-4 text-lg font-semibold">Create Workshop</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              Add a new wellness workshop to your offerings
            </p>
          </CardContent>
        </Card>
      </a>

      {/* Workshop Cards */}
      {workshops.map((workshop) => (
        <ProviderWorkshopCard key={workshop.id} workshop={workshop} />
      ))}
    </div>
  );
}
