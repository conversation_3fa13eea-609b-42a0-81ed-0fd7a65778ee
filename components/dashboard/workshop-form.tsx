"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { AlertCircle } from "lucide-react";
import React, { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { Temporal } from "temporal-polyfill";
import { z } from "zod";

import { saveWorkshop } from "@/app/(loggedin)/profile/actions";
import {
  type DailyAvailability,
  WorkshopAvailabilityField,
} from "@/components/dashboard/workshop-availability-field";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { FormActions } from "@/components/ui/form-actions";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import { LoadingButton } from "@/components/ui/loading-button";
import { Section } from "@/components/ui/section";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { calculateServiceFee } from "@/lib/payment-utils";
import type { Database } from "@/types/database.types";
import type { ProviderOrganization } from "@/types/organization";
import type {
  Category,
  CategoryGroup,
  PricingModel,
  VenueType,
  WorkshopDailyAvailability,
  WorkshopFormat,
} from "@/types/types";
import type { Workshop } from "@/types/workshop";

type WorkshopCategory =
  Database["public"]["Tables"]["workshop_categories"]["Row"];

interface WorkshopFormProps {
  workshop?: Workshop;
  organizationId: string;
  organization: ProviderOrganization | null;
  categoryGroups: CategoryGroup[];
  categories: Category[];
  workshopCategories: WorkshopCategory[];
}

// Define schema locally since it's only used in this component
const timeSlotSchema = z
  .object({
    start_time: z
      .string()
      .nullable()
      .refine(
        (val) =>
          val === null ||
          /^([0-1][0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(val),
        {
          message:
            "Please enter a valid time in 24-hour format (HH:mm:ss or HH:mm)",
        },
      ),
    end_time: z
      .string()
      .nullable()
      .refine(
        (val) =>
          val === null ||
          /^([0-1][0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(val),
        {
          message:
            "Please enter a valid time in 24-hour format (HH:mm:ss or HH:mm)",
        },
      ),
  })
  .nullable();

const workshopFormSchema = z
  .object({
    name: z.string().min(3, { message: "Name must be at least 3 characters" }),
    description: z
      .string()
      .min(10, { message: "Description must be at least 10 characters" }),
    format: z.enum(["in_person", "online", "hybrid"] as const, {
      errorMap: () => ({ message: "Please select a valid format" }),
    }) satisfies z.ZodType<WorkshopFormat>,
    availability: z
      .object({
        monday: timeSlotSchema,
        tuesday: timeSlotSchema,
        wednesday: timeSlotSchema,
        thursday: timeSlotSchema,
        friday: timeSlotSchema,
        saturday: timeSlotSchema,
        sunday: timeSlotSchema,
      })
      .refine(
        (availability) => {
          // Check that end time is after start time for all slots
          return Object.values(availability).every((slot) => {
            if (!slot || !slot.start_time || !slot.end_time) return true;
            const start = Temporal.PlainTime.from(slot.start_time);
            const end = Temporal.PlainTime.from(slot.end_time);
            return Temporal.PlainTime.compare(end, start) > 0;
          });
        },
        { message: "End time must be after start time" },
      ),
    duration: z.string().min(1, { message: "Please specify the duration" }),
    location: z.string().optional(),
    venue_type: z.enum(
      [
        "provider_location",
        "client_location",
        "provider_or_client_location",
        "online",
      ] as const,
      {
        errorMap: () => ({
          message: "Please select a valid workshop location",
        }),
      },
    ) satisfies z.ZodType<VenueType>,
    prerequisites: z.string().optional(),
    price: z.coerce
      .number()
      .min(0, { message: "Price must be a positive number" }),
    pricing_model: z.enum(["per_person", "total"] as const, {
      errorMap: () => ({ message: "Please select a valid pricing model" }),
    }) satisfies z.ZodType<PricingModel>,
    published: z.boolean().default(true),
    client_site_travel_fee: z.coerce
      .number()
      .min(0, { message: "Travel fee must be a positive number" }),
    currency: z
      .enum(["SGD", "MYR"], {
        errorMap: () => ({ message: "Please select a valid currency" }),
      })
      .default("SGD"),
    min_capacity: z.coerce
      .number()
      .min(1, { message: "Minimum capacity must be at least 1" }),
    lead_time: z.string().optional(),
    max_capacity: z.coerce
      .number()
      .min(1, { message: "Maximum capacity must be at least 1" }),
    group_discount_available: z.boolean().default(false),
    image_url: z.string().optional(),
    categoryIds: z.array(z.string()),
  })
  .refine(
    (data) => {
      // If venue type requires provider location, then location must be provided
      if (
        data.venue_type === "provider_location" ||
        data.venue_type === "provider_or_client_location"
      ) {
        return !!data.location && data.location.trim() !== "";
      }
      return true;
    },
    {
      message: "Location is required when provider location is selected",
      path: ["location"],
    },
  );

type WorkshopFormValues = z.infer<typeof workshopFormSchema>;

/**
 * Renders a comprehensive form for creating or editing a workshop, including validation, dynamic field behavior, error handling, and real-time pricing breakdown.
 *
 * The form supports all workshop attributes such as name, description, format, availability, duration, location, venue type, prerequisites, pricing, capacity, categories, image upload, and publication status. It initializes with existing workshop or organization data if provided, and integrates with backend save functionality. Validation is enforced using Zod schemas, and the form provides immediate feedback for errors and pricing calculations.
 */
export function WorkshopForm({
  workshop,
  organizationId,
  organization,
  categoryGroups,
  categories,
  workshopCategories,
}: WorkshopFormProps) {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);

  const form = useForm({
    resolver: zodResolver(workshopFormSchema),
    defaultValues: {
      name: workshop?.name || "",
      description: workshop?.description || "",
      format: (workshop?.format as WorkshopFormat) || "in_person",
      availability: (() => {
        const defaultAvailability: DailyAvailability = {
          monday: null,
          tuesday: null,
          wednesday: null,
          thursday: null,
          friday: null,
          saturday: null,
          sunday: null,
        };

        // If workshop has availability, merge it with defaults
        if (workshop?.availability) {
          // The workshop.availability might have the database composite type structure
          // We need to ensure it matches our DailyAvailability type
          const availability = workshop.availability as DailyAvailability;
          return {
            ...defaultAvailability,
            ...availability,
          };
        }

        return defaultAvailability;
      })(),
      duration: workshop?.duration || "",
      location: workshop?.location || organization?.location || "",
      venue_type:
        workshop?.format === ("online" as WorkshopFormat)
          ? ("online" as VenueType)
          : (workshop?.venue_type as VenueType) ||
            ("provider_location" as VenueType),
      prerequisites: workshop?.prerequisites || "",
      price: workshop?.price || 0,
      pricing_model:
        workshop?.pricing_model === "per_person" ||
        workshop?.pricing_model === "total"
          ? (workshop.pricing_model as PricingModel)
          : "per_person",
      client_site_travel_fee: workshop?.client_site_travel_fee || 0,
      currency:
        workshop?.currency === "SGD" || workshop?.currency === "MYR"
          ? (workshop.currency as "SGD" | "MYR")
          : "SGD",
      min_capacity: workshop?.min_capacity || 1,
      max_capacity: workshop?.max_capacity || 20,
      group_discount_available: workshop?.group_discount_available || false,
      lead_time: workshop?.lead_time || "",
      image_url: workshop?.image_url || "",
      published: workshop?.published ?? true,
      categoryIds: workshopCategories?.map((wc) => wc.category_id) || [],
    },
  });

  async function handleSubmit(values: WorkshopFormValues) {
    setError(null);

    startTransition(async () => {
      const result = await saveWorkshop(
        workshop?.id || null,
        organizationId,
        values.name,
        values.description,
        values.format,
        transformAvailability(values.availability),
        values.duration,
        values.location,
        values.venue_type,
        values.prerequisites,
        values.price,
        values.pricing_model,
        values.client_site_travel_fee,
        values.currency,
        values.min_capacity,
        values.max_capacity,
        values.group_discount_available,
        values.lead_time,
        values.image_url,
        values.published,
        values.categoryIds,
      );

      if (result?.error) {
        setError(result.error);
      }
    });
  }

  // Watch for format changes and update venue_type accordingly
  // This will run whenever the format field changes
  form.watch((value, { name }) => {
    if (name === "format") {
      const onlineFormat: WorkshopFormat = "online";
      const onlineVenue: VenueType = "online";
      if (value.format === onlineFormat) {
        // Set venue_type to "online" when format is "online"
        form.setValue("venue_type", onlineVenue, { shouldValidate: true });
      }
    }
  });

  const formatTypes = [
    { value: "in_person", label: "In Person" },
    { value: "online", label: "Online" },
    { value: "hybrid", label: "Hybrid (Online / In Person)" },
  ];

  const venueTypes = [
    { value: "provider_location", label: "At Provider's Location" },
    { value: "client_location", label: "At Client's Office" },
    {
      value: "provider_or_client_location",
      label: "Either Provider's or Client's Location",
    },
    { value: "online", label: "Online" },
  ];

  // Transform DailyAvailability to WorkshopDailyAvailability
  // Convert null start_time/end_time to undefined for database compatibility
  const transformAvailability = (
    availability: DailyAvailability,
  ): WorkshopDailyAvailability => {
    const result: WorkshopDailyAvailability = {
      monday: availability.monday
        ? {
            start_time: availability.monday.start_time || "",
            end_time: availability.monday.end_time || "",
          }
        : null,
      tuesday: availability.tuesday
        ? {
            start_time: availability.tuesday.start_time || "",
            end_time: availability.tuesday.end_time || "",
          }
        : null,
      wednesday: availability.wednesday
        ? {
            start_time: availability.wednesday.start_time || "",
            end_time: availability.wednesday.end_time || "",
          }
        : null,
      thursday: availability.thursday
        ? {
            start_time: availability.thursday.start_time || "",
            end_time: availability.thursday.end_time || "",
          }
        : null,
      friday: availability.friday
        ? {
            start_time: availability.friday.start_time || "",
            end_time: availability.friday.end_time || "",
          }
        : null,
      saturday: availability.saturday
        ? {
            start_time: availability.saturday.start_time || "",
            end_time: availability.saturday.end_time || "",
          }
        : null,
      sunday: availability.sunday
        ? {
            start_time: availability.sunday.start_time || "",
            end_time: availability.sunday.end_time || "",
          }
        : null,
    };
    return result;
  };

  return (
    <Section spacing="loose">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <div>
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Workshop Name <Required />
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Yoga for Beginners" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="md:row-span-2">
              <FormField
                control={form.control}
                name="image_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Workshop Image</FormLabel>
                    <FormControl>
                      <ImageUpload
                        endpoint="workshopImage"
                        value={field.value || ""}
                        onChange={field.onChange}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormDescription>
                      Upload an image for your workshop
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div>
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Description <Required />
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your workshop..."
                        className="min-h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="md:col-span-2">
              <FormField
                control={form.control}
                name="categoryIds"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categories</FormLabel>
                    <div className="space-y-4">
                      {categoryGroups.map((group) => (
                        <div key={group.id} className="space-y-2">
                          <h3 className="text-sm font-medium">{group.name}</h3>
                          <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3">
                            {categories
                              .filter(
                                (cat) => cat.category_group_id === group.id,
                              )
                              .map((category) => (
                                <div
                                  key={category.id}
                                  className="flex items-center space-x-2"
                                >
                                  <Checkbox
                                    id={`category-${category.id}`}
                                    checked={field.value.includes(category.id)}
                                    onCheckedChange={(checked) => {
                                      if (checked) {
                                        field.onChange([
                                          ...field.value,
                                          category.id,
                                        ]);
                                      } else {
                                        field.onChange(
                                          field.value.filter(
                                            (id) => id !== category.id,
                                          ),
                                        );
                                      }
                                    }}
                                  />
                                  <label
                                    htmlFor={`category-${category.id}`}
                                    className="text-sm"
                                  >
                                    {category.name}
                                  </label>
                                </div>
                              ))}
                          </div>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="md:col-span-2">
              <FormField
                control={form.control}
                name="availability"
                render={({ field }) => <WorkshopAvailabilityField {...field} />}
              />
            </div>

            <div>
              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Duration <Required />
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., 2 hours" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div>
              <FormField
                control={form.control}
                name="lead_time"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Lead Time Required</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., 2 weeks" {...field} />
                    </FormControl>
                    <FormDescription>
                      How much advance notice you need before a booking
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="md:col-span-2">
              <FormField
                control={form.control}
                name="format"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Workshop Format <Required />
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select workshop format" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {formatTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {form.watch("format") !== "online" && (
              <div>
                <FormField
                  control={form.control}
                  name="venue_type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Workshop Location <Required />
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select workshop location" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {venueTypes
                            .filter((type) => type.value !== "online")
                            .map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {(form.watch("venue_type") === "provider_location" ||
              form.watch("venue_type") === "provider_or_client_location") && (
              <div>
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Provider Address <Required />
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Location details (address, city, etc.)"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Enter your venue&apos;s address where the workshop will
                        be held
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <div className="md:col-span-2">
              <FormField
                control={form.control}
                name="prerequisites"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prerequisites</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Any specific requirements for participants or venue"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Equipment, materials, or space requirements for the
                      workshop
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="min_capacity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Minimum Capacity <Required />
                  </FormLabel>
                  <FormControl>
                    <Input type="number" min="1" {...field} />
                  </FormControl>
                  <FormDescription>
                    Minimum number of participants required
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="max_capacity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Maximum Capacity <Required />
                  </FormLabel>
                  <FormControl>
                    <Input type="number" min="1" {...field} />
                  </FormControl>
                  <FormDescription>
                    Maximum number of participants allowed
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div>
            <FormField
              control={form.control}
              name="pricing_model"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Pricing Model <Required />
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select pricing model" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="per_person">Per Person</SelectItem>
                      <SelectItem value="total">Total Fee</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {form.watch("pricing_model") === "per_person"
                      ? "Price Per Person"
                      : "Total Price"}{" "}
                    <Required />
                  </FormLabel>
                  <FormControl>
                    <Input type="number" min="0" step="0.01" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="currency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Currency</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select currency" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="SGD">SGD</SelectItem>
                      <SelectItem value="MYR">MYR</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="client_site_travel_fee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client Site Travel Fee</FormLabel>
                  <FormControl>
                    <Input type="number" min="0" step="0.01" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Pricing breakdown display */}
          {(form.watch("price") > 0 ||
            form.watch("client_site_travel_fee") > 0) && (
            <div className="col-span-2 rounded-md bg-muted/30 p-4">
              <h4 className="mb-2 text-sm font-medium">Pricing Breakdown</h4>
              <div className="space-y-1 text-sm text-muted-foreground">
                {form.watch("pricing_model") === "total" && (
                  <div className="flex justify-between">
                    <span>Workshop Fee:</span>
                    <span>
                      {form.watch("currency")}{" "}
                      {Number(form.watch("price")).toFixed(2)}
                    </span>
                  </div>
                )}
                {form.watch("pricing_model") === "per_person" &&
                  form.watch("max_capacity") > 0 && (
                    <div className="flex justify-between">
                      <span>
                        Workshop Fee ({form.watch("max_capacity")} people):
                      </span>
                      <span>
                        {form.watch("currency")}{" "}
                        {(
                          Number(form.watch("price")) *
                          Number(form.watch("max_capacity"))
                        ).toFixed(2)}
                      </span>
                    </div>
                  )}
                {Number(form.watch("client_site_travel_fee")) > 0 && (
                  <div className="flex justify-between">
                    <span>Client Site Travel Fee:</span>
                    <span>
                      {form.watch("currency")}{" "}
                      {Number(form.watch("client_site_travel_fee")).toFixed(2)}
                    </span>
                  </div>
                )}
                {((form.watch("pricing_model") === "per_person" &&
                  form.watch("max_capacity") > 0) ||
                  form.watch("pricing_model") === "total" ||
                  Number(form.watch("client_site_travel_fee")) > 0) && (
                  <div className="flex justify-between border-t pt-1">
                    <span>Total Revenue:</span>
                    <span>
                      {form.watch("currency")}{" "}
                      {(() => {
                        const workshopRevenue =
                          form.watch("pricing_model") === "per_person"
                            ? Number(form.watch("price")) *
                              Number(form.watch("max_capacity") || 0)
                            : Number(form.watch("price"));
                        const travelFee = Number(
                          form.watch("client_site_travel_fee") || 0,
                        );
                        return (workshopRevenue + travelFee).toFixed(2);
                      })()}
                    </span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Service Charge (20%):</span>
                  <span>
                    {form.watch("currency")}{" "}
                    {(() => {
                      const workshopRevenue =
                        form.watch("pricing_model") === "per_person"
                          ? Number(form.watch("price")) *
                            Number(form.watch("max_capacity") || 0)
                          : Number(form.watch("price"));
                      const travelFee = Number(
                        form.watch("client_site_travel_fee") || 0,
                      );
                      return calculateServiceFee(
                        workshopRevenue + travelFee,
                      ).toFixed(2);
                    })()}
                  </span>
                </div>
                <div className="mt-1 flex justify-between border-t pt-1 font-medium text-foreground">
                  <span>Your Total Earnings:</span>
                  <span>
                    {form.watch("currency")}{" "}
                    {(() => {
                      const workshopRevenue =
                        form.watch("pricing_model") === "per_person"
                          ? Number(form.watch("price")) *
                            Number(form.watch("max_capacity") || 0)
                          : Number(form.watch("price"));
                      const travelFee = Number(
                        form.watch("client_site_travel_fee") || 0,
                      );
                      return ((workshopRevenue + travelFee) * 0.8).toFixed(2);
                    })()}
                  </span>
                </div>
              </div>
            </div>
          )}

          {error && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {Object.keys(form.formState.errors).length > 0 && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please fix the errors above before submitting the form.
              </AlertDescription>
            </Alert>
          )}

          <FormField
            control={form.control}
            name="published"
            render={({ field }) => (
              <FormItem className="mt-4 flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Publish Workshop</FormLabel>
                  <FormDescription>
                    When checked, this workshop will be visible to clients in
                    the workshops list. Unpublished workshops are only visible
                    to you.
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <FormActions className="mt-4">
            <Button type="button" variant="outline" asChild>
              <a href="/dashboard">Cancel</a>
            </Button>
            <LoadingButton
              type="submit"
              loading={isPending}
              loadingText="Saving..."
            >
              {workshop ? "Update Workshop" : "Create Workshop"}
            </LoadingButton>
          </FormActions>
        </form>
      </Form>
    </Section>
  );
}

export function Required() {
  return (
    <abbr
      title="required"
      className="text-red-500 no-underline"
      aria-label="Required field"
    >
      *
    </abbr>
  );
}
