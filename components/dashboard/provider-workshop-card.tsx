"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>V<PERSON>ical, Trash } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { deleteWorkshop } from "@/app/(loggedin)/profile/actions";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { IconText } from "@/components/ui/icon-text";
import { WorkshopCard } from "@/components/workshops/workshop-card";
import type {
  WorkshopWithCategories,
  WorkshopWithProvider,
} from "@/types/workshop";

interface ProviderWorkshopCardProps {
  workshop: WorkshopWithProvider & Partial<WorkshopWithCategories>;
}

export function ProviderWorkshopCard({ workshop }: ProviderWorkshopCardProps) {
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!deleteId) return;

    setIsDeleting(true);

    const result = await deleteWorkshop(deleteId);
    setIsDeleting(false);
    setDeleteId(null);

    if (!result.success) {
      console.error("Error deleting workshop:", result.error);
      toast("Failed to delete workshop. Please try again.");
      return;
    }
  };

  // Check for missing fields
  const missingFields: string[] = [];
  if (!workshop.image_url) {
    missingFields.push("image");
  }
  if (
    !workshop.workshop_categories ||
    workshop.workshop_categories.length === 0
  ) {
    missingFields.push("categories");
  }

  return (
    <>
      <div className="relative flex flex-col">
        {/* Actions menu */}
        <div className="absolute right-2 top-2 z-10">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 bg-white/90 backdrop-blur-sm hover:bg-white"
              >
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">Actions</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <a href={`/workshops/${workshop.id}`}>
                  <IconText icon={Eye} text="View" />
                </a>
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive focus:text-destructive"
                onClick={() => {
                  setDeleteId(workshop.id);
                }}
              >
                <IconText icon={Trash} text="Delete" />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Use the standard workshop card with custom href */}
        <WorkshopCard
          workshop={workshop}
          href={`/dashboard/workshops/${workshop.id}`}
        />

        {/* Provider notes section */}
        <div className="mt-3 space-y-2">
          {!workshop.published && (
            <Badge variant="secondary" className="bg-amber-50 text-amber-700">
              <IconText
                icon={AlertCircle}
                text="Unpublished"
                spacing="tight"
                iconSize="sm"
              />
            </Badge>
          )}

          {/* Missing fields */}
          {missingFields.length > 0 && (
            <div className="text-sm text-amber-600">
              <IconText
                icon={AlertCircle}
                text={`Missing: ${missingFields.join(", ")}`}
                iconSize="sm"
              />
            </div>
          )}
        </div>
      </div>

      <AlertDialog
        open={!!deleteId}
        onOpenChange={(open) => !open && setDeleteId(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              workshop and all associated registrations.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
