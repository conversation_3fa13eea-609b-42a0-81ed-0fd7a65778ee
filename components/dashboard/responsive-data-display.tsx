"use client";

import type { ReactNode } from "react";
import React from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";

export interface Column<T> {
  header: string;
  accessor: keyof T | ((item: T) => ReactNode);
  width?: string;
}

interface ResponsiveDataDisplayProps<T> {
  data: T[];
  columns: Column<T>[];
  onRowClick?: (item: T) => void;
  actionColumn?: (item: T) => ReactNode;
  emptyState: ReactNode;
  createButton?: {
    href: string;
    icon: ReactNode;
    label: string;
  };
  cardFields?: {
    title: keyof T | ((item: T) => ReactNode);
    content: Array<{
      label: string;
      value: keyof T | ((item: T) => ReactNode);
      layout?: "row" | "column";
    }>;
  };
}

// Helper type to constrain the generic to ensure values can be rendered as ReactNode
export function ResponsiveDataDisplay<T extends Record<string, unknown>>({
  data,
  columns,
  onRowClick,
  actionColumn,
  emptyState,
  createButton,
  cardFields,
}: ResponsiveDataDisplayProps<T>) {
  // Helper function to get value by accessor
  const getValue = (item: T, accessor: keyof T | ((item: T) => ReactNode)) => {
    if (typeof accessor === "function") {
      return accessor(item);
    }
    // Ensure the value is renderable as a React node
    const value = item[accessor as keyof T];
    if (value === null || value === undefined) {
      return value;
    }
    // Convert non-ReactNode values to string to ensure they can be rendered
    return React.isValidElement(value) ? value : String(value);
  };

  return (
    <>
      {/* Create Button (if provided) */}
      {createButton && (
        <div className="mb-4">
          <Button asChild>
            <a href={createButton.href}>
              {createButton.icon}
              {createButton.label}
            </a>
          </Button>
        </div>
      )}

      {/* Desktop view - Table */}
      <div className="hidden rounded-md border md:block">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column, index) => (
                <TableHead key={index} className={column.width}>
                  {column.header}
                </TableHead>
              ))}
              {actionColumn && <TableHead className="w-[80px]"></TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length + (actionColumn ? 1 : 0)}
                  className="py-8 text-center"
                >
                  {emptyState}
                </TableCell>
              </TableRow>
            ) : (
              data.map((item, rowIndex) => (
                <TableRow
                  key={rowIndex}
                  className={cn(
                    onRowClick && "cursor-pointer hover:bg-muted/50",
                  )}
                  onClick={() => onRowClick && onRowClick(item)}
                >
                  {columns.map((column, colIndex) => (
                    <TableCell
                      key={colIndex}
                      className={colIndex === 0 ? "font-medium" : ""}
                    >
                      {getValue(item, column.accessor) as ReactNode}
                    </TableCell>
                  ))}
                  {actionColumn && <TableCell>{actionColumn(item)}</TableCell>}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Mobile view - Cards */}
      <div className="space-y-3 md:hidden">
        {data.length === 0 ? (
          <Card>
            <CardContent className="p-4">{emptyState}</CardContent>
          </Card>
        ) : (
          data.map((item, index) => (
            <Card
              key={index}
              className={cn(
                onRowClick &&
                  "cursor-pointer transition-colors hover:bg-muted/50",
              )}
              onClick={() => onRowClick && onRowClick(item)}
            >
              <CardContent className="p-3 pt-2">
                {cardFields && (
                  <>
                    <div className="flex items-center justify-between">
                      <h3 className="text-base font-medium">
                        {typeof cardFields.title === "function"
                          ? cardFields.title(item)
                          : String(item[cardFields.title as keyof T])}
                      </h3>
                      {actionColumn && (
                        <div onClick={(e) => e.stopPropagation()}>
                          {actionColumn(item)}
                        </div>
                      )}
                    </div>
                    <div className="mt-1 space-y-1 text-sm text-muted-foreground">
                      {cardFields.content.map((field, fieldIndex) => (
                        <div
                          key={fieldIndex}
                          className={`flex ${field.layout === "column" ? "flex-col" : "justify-between"}`}
                        >
                          <span className="font-semibold text-foreground">
                            {field.label}
                          </span>
                          <span>
                            {typeof field.value === "function"
                              ? field.value(item)
                              : String(item[field.value as keyof T])}
                          </span>
                        </div>
                      ))}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </>
  );
}
