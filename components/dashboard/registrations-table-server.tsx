import { RegistrationsTable } from "@/components/dashboard/registrations-table";
import { requireProviderWithDetails } from "@/lib/auth";
import type { Database } from "@/types/database.types";
import { createClient } from "@/utils/supabase/server";

type Booking = Database["public"]["Tables"]["bookings"]["Row"] & {
  workshops: Database["public"]["Tables"]["workshops"]["Row"];
  clients: {
    id: string;
    profiles: {
      full_name: string;
    } | null;
  } | null;
};

export async function RegistrationsTableServer() {
  const { provider } = await requireProviderWithDetails();
  const supabase = await createClient();

  // First get all workshop IDs for this provider
  const { data: workshopsData } = await supabase
    .from("workshops")
    .select("id")
    .eq("provider_id", provider.organization_id);

  const workshopIds = (workshopsData || []).map((workshop) => workshop.id);

  if (workshopIds.length === 0) {
    return <RegistrationsTable bookings={[]} />;
  }

  // Get bookings for provider's workshops
  const { data: bookingsData } = await supabase
    .from("bookings")
    .select("*, workshops(*), clients(*, profiles(*))")
    .in("workshop_id", workshopIds)
    .eq("status", "confirmed");

  const bookings = (bookingsData || []) as Booking[];

  return <RegistrationsTable bookings={bookings} />;
}
