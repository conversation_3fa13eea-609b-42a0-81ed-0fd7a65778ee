"use client";

import type { Column } from "@/components/dashboard/responsive-data-display";
import { ResponsiveDataDisplay } from "@/components/dashboard/responsive-data-display";
import type { BookingWithRelations } from "@/types/types";

interface RegistrationsTableProps {
  bookings: BookingWithRelations[];
}

export function RegistrationsTable({ bookings }: RegistrationsTableProps) {
  // Empty state component
  const EmptyState = () => (
    <p className="py-8 text-center text-muted-foreground">No bookings found</p>
  );

  // Define columns for the responsive table
  const columns: Column<BookingWithRelations>[] = [
    {
      header: "Workshop",
      accessor: (booking: BookingWithRelations) => booking.workshops.name,
    },
    {
      header: "Attendee",
      accessor: (booking: BookingWithRelations) =>
        booking.clients?.profiles?.full_name || "Unknown",
    },
    {
      header: "Date",
      accessor: (booking: BookingWithRelations) =>
        new Date(booking.booking_datetime).toLocaleDateString(),
    },
    {
      header: "Tickets",
      accessor: "participant_count",
    },
    {
      header: "Total",
      accessor: (booking: BookingWithRelations) =>
        `$${booking.total_price.toFixed(2)}`,
    },
  ];

  return (
    <ResponsiveDataDisplay
      data={bookings}
      columns={columns}
      emptyState={<EmptyState />}
      cardFields={{
        title: (booking: BookingWithRelations) => booking.workshops.name,
        content: [
          {
            label: "Attendee",
            value: (booking: BookingWithRelations) =>
              booking.clients?.profiles?.full_name || "Unknown",
            layout: "row",
          },
          {
            label: "Date",
            value: (booking: BookingWithRelations) =>
              new Date(booking.booking_datetime).toLocaleDateString(),
            layout: "row",
          },
          {
            label: "Tickets",
            value: "participant_count",
            layout: "row",
          },
          {
            label: "Total",
            value: (booking: BookingWithRelations) =>
              `$${booking.total_price.toFixed(2)}`,
            layout: "row",
          },
        ],
      }}
    />
  );
}
