import Link from "next/link";

import { cn } from "@/lib/utils";

interface FooterProps {
  className?: string;
}

export function Footer({ className }: FooterProps) {
  return (
    <footer className={cn("w-full bg-background pb-3 pt-16", className)}>
      <ul className="container flex flex-row gap-3 text-xs sm:text-sm">
        <li>
          <Link
            href="/terms.html"
            target="_blank"
            className="opacity-80 transition-opacity hover:opacity-100"
          >
            Terms of Service
          </Link>
        </li>
        <li>
          <Link
            href="/privacy.html"
            target="_blank"
            className="opacity-80 transition-opacity hover:opacity-100"
          >
            Privacy Policy
          </Link>
        </li>
      </ul>
    </footer>
  );
}
