"use client";

import type { User } from "@supabase/supabase-js";
import { Calendar, LogOut, Menu } from "lucide-react";
import { usePathname } from "next/navigation";
import { useState } from "react";

import { signOut } from "@/app/(auth)/actions";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { IconText } from "@/components/ui/icon-text";
import {
  Sheet,
  SheetContent,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { StatusIndicator } from "@/components/ui/status-indicator";
import { useUnreadMessages } from "@/hooks/use-unread-messages";
import { cn } from "@/lib/utils";

import type { NavbarVariant } from "./navbar";

interface NavbarClientProps {
  user: User | null;
  organization: {
    profile_photo_url?: string;
    name?: string;
  } | null;
  className?: string;
  showAuth?: boolean;
  userType: "provider" | "client" | "admin" | null;
  variant?: NavbarVariant;
}

export function NavbarClient({
  user,
  organization,
  className,
  showAuth = true,
  userType,
  variant = "default",
}: NavbarClientProps) {
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Use React Query hook to check if user has any unread messages
  // Only fetch for providers and clients who are logged in
  const shouldFetchUnread =
    user && (userType === "provider" || userType === "client");
  const { data: hasUnreadMessages = false } = useUnreadMessages(
    shouldFetchUnread ? user.id : null,
  );

  const handleSignOut = async () => {
    await signOut(userType === "provider");
  };

  // Get all navigation items based on user role and variant
  const getNavigationConfig = () => {
    // Default navigation based on user role
    const publicItems: never[] = [
      // Not yet launched:
      // { name: "Workshops", href: "/workshops" },
    ];
    const providerItems = [
      { name: "Dashboard", href: "/dashboard" },
      { name: "Enquiries", href: "/enquiries" },
      { name: "Transactions", href: "/transactions" },
    ];
    const clientItems = [
      { name: "Workshops", href: "/workshops" },
      { name: "Profile", href: "/profile" },
      { name: "Enquiries", href: "/enquiries" },
    ];
    const adminItems = [{ name: "Admin Dashboard", href: "/admin/dashboard" }];

    let baseItems: { name: string; href: string }[];

    if (!user) {
      baseItems = publicItems;
    } else {
      switch (userType) {
        case "provider":
          baseItems = providerItems;
          break;
        case "client":
          baseItems = clientItems;
          break;
        case "admin":
          baseItems = adminItems;
          break;
        default:
          baseItems = [];
      }
    }

    // Add landing page cross-links when viewing landing pages
    if (variant === "landing-client") {
      baseItems = [
        ...baseItems,
        { name: "Become a provider", href: "/providers" },
      ];
    }

    if (variant === "providers") {
      baseItems = [...baseItems, { name: "For companies", href: "/" }];
    }

    return baseItems;
  };

  // Get navigation configuration
  const navItems = getNavigationConfig();

  return (
    <>
      <div
        className={cn(
          "hidden sm:ml-12 sm:flex sm:flex-1 sm:items-center sm:justify-between",
          className,
        )}
      >
        <nav className="flex items-center space-x-6 text-sm font-medium">
          {navItems.map((item) => (
            <a
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center gap-2 transition-colors hover:text-foreground/80",
                isActiveRoute(pathname, item.href)
                  ? "text-foreground"
                  : "text-foreground/60",
              )}
            >
              {item.name}
              {item.name === "Enquiries" && hasUnreadMessages && (
                <StatusIndicator status="missing" size="sm" />
              )}
            </a>
          ))}
        </nav>

        <div className="flex items-center space-x-4">
          {showAuth && user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative h-8 w-8 rounded-full"
                  data-testid="user-menu-button"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage
                      src={
                        organization?.profile_photo_url ||
                        user?.user_metadata?.avatar_url
                      }
                      alt={
                        organization?.name ||
                        user?.user_metadata?.full_name ||
                        user?.email ||
                        "User menu avatar"
                      }
                      width={32}
                      height={32}
                    />
                    <AvatarFallback>
                      {organization?.name?.charAt(0) ||
                        user?.user_metadata?.full_name?.charAt(0) ||
                        user?.email?.charAt(0) ||
                        "?"}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <div className="flex flex-col space-y-1 p-2">
                  <p className="text-sm font-medium leading-none">
                    {user?.user_metadata?.full_name}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.email}
                  </p>
                </div>
                {navItems.map((item) => (
                  <DropdownMenuItem key={item.href} asChild>
                    <a href={item.href}>
                      <IconText icon={Calendar} text={item.name} />
                    </a>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <IconText icon={LogOut} text="Log Out" />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : showAuth ? (
            <>
              <Button variant="ghost" asChild>
                <a
                  href={`/login${variant === "providers" ? "?type=provider" : "?type=client"}`}
                >
                  Log In
                </a>
              </Button>
              <Button variant="secondary" asChild>
                <a
                  href={
                    variant === "providers"
                      ? "/signup-provider"
                      : "/client-signup"
                  }
                >
                  Sign Up
                </a>
              </Button>
            </>
          ) : null}
        </div>
      </div>

      {showAuth && (
        <div className="flex flex-1 items-center justify-end sm:hidden">
          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="sm:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
              <div className="flex flex-col space-y-4 py-4">
                {navItems.map((item) => (
                  <a
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-2 text-lg font-medium",
                      isActiveRoute(pathname, item.href)
                        ? "text-foreground"
                        : "text-foreground/60",
                    )}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                    {item.name === "Enquiries" && hasUnreadMessages && (
                      <StatusIndicator status="missing" size="sm" />
                    )}
                  </a>
                ))}
                <div className="pt-4">
                  {showAuth && user ? (
                    <>
                      <div className="flex items-center space-x-4 pb-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage
                            src={
                              organization?.profile_photo_url ||
                              user?.user_metadata?.avatar_url
                            }
                            alt={
                              organization?.name ||
                              user?.user_metadata?.full_name ||
                              user?.email ||
                              "User menu avatar"
                            }
                            width={40}
                            height={40}
                          />
                          <AvatarFallback>
                            {organization?.name?.charAt(0) ||
                              user?.user_metadata?.full_name?.charAt(0) ||
                              user?.email?.charAt(0) ||
                              "?"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">
                            {user?.user_metadata?.full_name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {user?.email}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col space-y-2">
                        <Button variant="outline" onClick={handleSignOut}>
                          Log Out
                        </Button>
                      </div>
                    </>
                  ) : showAuth ? (
                    <div className="flex flex-col space-y-2">
                      <Button variant="outline" asChild>
                        <a
                          href={`/login${variant === "providers" ? "?type=provider" : "?type=client"}`}
                          onClick={() => setIsMenuOpen(false)}
                        >
                          Log in
                        </a>
                      </Button>
                      <Button asChild>
                        <a
                          href={
                            variant === "providers"
                              ? "/signup-provider"
                              : "/client-signup"
                          }
                          onClick={() => setIsMenuOpen(false)}
                        >
                          Sign up
                        </a>
                      </Button>
                    </div>
                  ) : null}
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      )}
    </>
  );
}

/**
 * Determines if a navigation route should be marked as active based on the current path.
 *
 * @param currentPath - The current pathname from usePathname(), may include query params and hash
 * @param itemHref - The navigation item's href path (simple path without query params)
 * @returns True if the route should be marked as active, false otherwise
 *
 * @example
 * ```typescript
 * isActiveRoute("/dashboard?tab=settings", "/dashboard") // true
 * isActiveRoute("/dashboard/settings", "/dashboard") // true
 * isActiveRoute("/profile", "/dashboard") // false
 * isActiveRoute("/", "/") // true
 * isActiveRoute("/dashboard", "/") // false
 * ```
 */
function isActiveRoute(currentPath: string, itemHref: string): boolean {
  // Clean current path by removing query params, hash fragments, and trailing slashes
  const currentClean = (() => {
    const cleaned = currentPath.split("?")[0].split("#")[0];
    return cleaned === "/" ? "/" : cleaned.replace(/\/$/, "");
  })();
  if (itemHref === "/") {
    return currentClean === "/";
  }
  if (currentClean === itemHref) {
    return true;
  }
  return currentClean.startsWith(itemHref + "/");
}
