"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { validateInviteCode } from "@/app/(auth)/validate-invite";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import FormErrorAlert from "./form-error-alert";
import { SubmitButton } from "./submit-button";

// Define the schema for the invite code form
const inviteCodeSchema = z.object({
  inviteCode: z.string().min(1, "Invite code is required"),
});

type InviteCodeFormValues = z.infer<typeof inviteCodeSchema>;

export function InviteCodeForm() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const form = useForm<InviteCodeFormValues>({
    resolver: zodResolver(inviteCodeSchema),
    defaultValues: {
      inviteCode: "",
    },
  });

  async function handleSubmit(values: InviteCodeFormValues) {
    setError(null);

    startTransition(async () => {
      const result = await validateInviteCode(values.inviteCode);

      if (!result) {
        setError("Invalid invite code");
        return;
      }

      router.push(
        `/signup-provider?code=${encodeURIComponent(values.inviteCode)}`,
      );
    });
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="inviteCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Invite Code</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormDescription>
                Pulse Space is invite-only for now. Contact us to get your
                invite code.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormErrorAlert error={error} />

        <SubmitButton
          isPending={isPending}
          pendingText="Validating..."
          text="Continue"
          className="w-full"
        />
      </form>
    </Form>
  );
}
