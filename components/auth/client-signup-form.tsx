"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";

import { signupClient } from "@/app/(auth)/actions";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { type ClientSignupParams, clientSignupSchema } from "@/lib/schemas";

import { FieldGroup } from "../ui/field-group";
import { Section } from "../ui/section";
import FormErrorAlert from "./form-error-alert";
import { SubmitButton } from "./submit-button";

export function ClientSignupForm() {
  const [error, setError] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const form = useForm<ClientSignupParams>({
    resolver: zod<PERSON><PERSON><PERSON>ver(clientSignupSchema),
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
      confirmPassword: "",
      companyName: "",
      location: "",
    },
    mode: "onChange",
  });

  async function handleSubmit(values: ClientSignupParams) {
    setError(null);

    startTransition(async () => {
      // Pass individual parameters to the server action
      const result = await signupClient(
        values.fullName,
        values.email,
        values.password,
        values.confirmPassword,
        values.companyName,
        values.location,
      );

      // If we get here, it means the redirect didn't happen, which indicates an error
      if (!result) {
        setError("An unexpected error occurred. Please try again.");
        return;
      }

      if (result.error) {
        setError(result.error || "An error occurred during signup");
      }
    });
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <Section>
          <FormErrorAlert error={error} />

          <FieldGroup>
            <h3 className="text-lg font-medium">Your Account</h3>
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input type="password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Confirm Password</FormLabel>
                  <FormControl>
                    <Input type="password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FieldGroup>

          <FieldGroup>
            <h3 className="text-lg font-medium">Company Information</h3>
            <FormField
              control={form.control}
              name="companyName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Name</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. Singapore" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </FieldGroup>

          <SubmitButton
            isPending={isPending}
            pendingText="Creating account..."
            text="Create Client Account"
          />
        </Section>
      </form>
    </Form>
  );
}
