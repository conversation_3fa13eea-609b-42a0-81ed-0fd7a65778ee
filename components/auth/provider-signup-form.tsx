"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";

import { signupProvider } from "@/app/(auth)/actions";
import { Checkbox } from "@/components/ui/checkbox";
import { CityCountrySelect } from "@/components/ui/city-country-select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { type ProviderSignupParams, providerSignupSchema } from "@/lib/schemas";

import { Button } from "../ui/button";
import { FieldGroup } from "../ui/field-group";
import { Section } from "../ui/section";
import FormErrorAlert from "./form-error-alert";
import { SubmitButton } from "./submit-button";

export function ProviderSignupForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [isPending, startTransition] = useTransition();

  const form = useForm<ProviderSignupParams>({
    resolver: zodResolver(providerSignupSchema),
    mode: "onChange",
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
      confirmPassword: "",
      organizationName: "",
      city: "",
      country: "",
      agreeToTerms: false,
    },
  });

  async function handleSubmit(values: ProviderSignupParams) {
    setError(null);

    startTransition(async () => {
      // Pass individual parameters to the server action
      const result = await signupProvider(
        values.fullName,
        values.email,
        values.password,
        values.confirmPassword,
        values.organizationName,
        values.city,
        values.country,
        values.agreeToTerms,
      );

      // If we get here, it means the redirect didn't happen, which indicates an error
      if (!result) {
        setError("An unexpected error occurred. Please try again.");
        return;
      }

      if (result.error) {
        setError(result.error || "An error occurred during signup");
      }
    });
  }

  const handleNext = async () => {
    let fieldsToValidate: (keyof ProviderSignupParams)[] = [];
    if (currentStep === 1) {
      fieldsToValidate = ["fullName", "email", "password", "confirmPassword"];
    }

    const isValid = await form.trigger(fieldsToValidate);

    if (isValid) {
      setCurrentStep(currentStep + 1);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <Section>
          {currentStep === 1 && (
            <FieldGroup>
              <h3 className="text-lg font-medium">Your Account</h3>
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </FieldGroup>
          )}

          {currentStep === 2 && (
            <FieldGroup>
              <h3 className="text-lg font-medium">Your Organisation</h3>
              <FormField
                control={form.control}
                name="organizationName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organisation / Provider Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="city"
                render={({ field: cityField }) => (
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field: countryField }) => (
                      <CityCountrySelect
                        onCityChange={cityField.onChange}
                        onCountryChange={countryField.onChange}
                        cityValue={cityField.value}
                        countryValue={countryField.value}
                        label="Location"
                        placeholder="Select a location"
                        description="Select the location where your business operates"
                      />
                    )}
                  />
                )}
              />
              <FormField
                control={form.control}
                name="agreeToTerms"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm">
                        I agree to the{" "}
                        <a
                          href="/terms.html"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="underline"
                        >
                          terms and conditions
                        </a>{" "}
                        and{" "}
                        <a
                          href="/privacy.html"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="underline"
                        >
                          privacy policy
                        </a>
                      </FormLabel>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            </FieldGroup>
          )}

          <FormErrorAlert error={error} />

          <div className="flex justify-between">
            {currentStep > 1 && (
              <Button
                type="button"
                onClick={() => setCurrentStep(currentStep - 1)}
                className="w-1/3"
                variant="outline"
              >
                Previous
              </Button>
            )}
            {currentStep < 2 && (
              <Button type="button" onClick={handleNext} className="w-1/3">
                Next
              </Button>
            )}
            {currentStep === 2 && (
              <SubmitButton
                isPending={isPending}
                pendingText="Signing up..."
                text="Sign up"
                className="w-1/2"
              />
            )}
          </div>
        </Section>
      </form>
    </Form>
  );
}
