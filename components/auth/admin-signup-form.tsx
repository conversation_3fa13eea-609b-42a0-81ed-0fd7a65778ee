"use client";

import { useState } from "react";

import { signupAdmin } from "@/app/(auth)/actions";
import FormErrorAlert from "@/components/auth/form-error-alert";
import { SubmitButton } from "@/components/auth/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Section } from "@/components/ui/section";

export default function AdminSignupForm() {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  async function handleSubmit(formData: FormData) {
    setIsLoading(true);
    setError(null);

    const fullName = formData.get("fullName") as string;
    const email = formData.get("email") as string;
    const password = formData.get("password") as string;
    const confirmPassword = formData.get("confirmPassword") as string;

    const result = await signupAdmin(
      fullName,
      email,
      password,
      confirmPassword,
    );

    if (result?.error) {
      setError(result.error);
      setIsLoading(false);
    }
  }

  return (
    <form action={handleSubmit}>
      <Section spacing="loose">
        {error && <FormErrorAlert error={error} />}

        <div>
          <Label htmlFor="fullName">Full Name</Label>
          <div className="mt-1">
            <Input
              id="fullName"
              name="fullName"
              type="text"
              autoComplete="name"
              required
              className="block w-full"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="email">Email address</Label>
          <div className="mt-1">
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              className="block w-full"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="password">Password</Label>
          <div className="mt-1">
            <Input
              id="password"
              name="password"
              type="password"
              autoComplete="new-password"
              required
              className="block w-full"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <div className="mt-1">
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              autoComplete="new-password"
              required
              className="block w-full"
            />
          </div>
        </div>

        <div>
          <SubmitButton
            isPending={isLoading}
            pendingText="Creating account..."
            text="Create Admin Account"
            className="w-full"
          />
        </div>

        <div className="text-center">
          <a
            href="/admin/login"
            className="text-sm text-blue-600 hover:text-blue-500"
          >
            Already have an admin account? Sign in
          </a>
        </div>
      </Section>
    </form>
  );
}
