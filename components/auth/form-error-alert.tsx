import { AlertCircle } from "lucide-react";

import { Alert, AlertDescription } from "@/components/ui/alert";

interface FormErrorAlertProps {
  error: string | null;
}

const FormErrorAlert: React.FC<FormErrorAlertProps> = ({ error }) => {
  if (!error) {
    return null;
  }

  return (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>{error}</AlertDescription>
    </Alert>
  );
};

export default FormErrorAlert;
