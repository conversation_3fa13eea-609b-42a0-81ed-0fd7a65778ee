import React from "react";

import { LoadingButton } from "@/components/ui/loading-button";
import { cn } from "@/lib/utils";

interface SubmitButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isPending: boolean;
  pendingText: string;
  text: string;
}

export const SubmitButton: React.FC<SubmitButtonProps> = ({
  isPending,
  pendingText,
  text,
  disabled,
  className,
  children: _children, // To ensure children isn't passed through ...rest
  ...rest
}) => {
  return (
    <LoadingButton
      type="submit" // Defaulting to type="submit" as it's a "SubmitButton"
      loading={isPending}
      loadingText={pendingText}
      disabled={disabled}
      className={cn("w-full", className)}
      {...rest}
    >
      {text}
    </LoadingButton>
  );
};
