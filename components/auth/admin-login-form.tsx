"use client";

import { useState } from "react";

import { login } from "@/app/(auth)/actions";
import FormErrorAlert from "@/components/auth/form-error-alert";
import { SubmitButton } from "@/components/auth/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Section } from "@/components/ui/section";

export default function AdminLoginForm() {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  async function handleSubmit(formData: FormData) {
    setIsLoading(true);
    setError(null);

    const email = formData.get("email") as string;
    const password = formData.get("password") as string;

    const result = await login(email, password, "/admin/dashboard");

    if (result?.error) {
      setError(result.error);
    }

    setIsLoading(false);
  }

  return (
    <form action={handleSubmit}>
      <Section spacing="loose">
        {error && <FormErrorAlert error={error} />}

        <div>
          <Label htmlFor="email">Email address</Label>
          <div className="mt-1">
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              className="block w-full"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="password">Password</Label>
          <div className="mt-1">
            <Input
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              className="block w-full"
            />
          </div>
        </div>

        <div>
          <SubmitButton
            isPending={isLoading}
            pendingText="Signing in..."
            text="Sign in to Admin Panel"
            className="w-full"
          />
        </div>

        <div className="text-center">
          <a
            href="/admin/signup"
            className="text-sm text-blue-600 hover:text-blue-500"
          >
            Need to create an admin account?
          </a>
        </div>
      </Section>
    </form>
  );
}
