"use client";

import { ChevronDown, ChevronUp, Filter } from "lucide-react";
import { useEffect, useState } from "react";

import { Button } from "@/components/ui/button";
import type { WorkshopFormat } from "@/types/types";

import { WorkshopFiltersCore } from "./workshop-filters-core";

interface WorkshopFiltersMobileProps {
  initialFilters: {
    type: WorkshopFormat[];
    category: string[];
    page?: number;
  };
  categories: { id: string; name: string }[];
  mode: "desktop" | "mobile";
}

export function WorkshopFiltersMobile({
  initialFilters,
  categories,
  mode,
}: WorkshopFiltersMobileProps) {
  // Check if any filters are active
  const hasActiveFilters =
    initialFilters.category.length > 0 || initialFilters.type.length > 0;

  // Collapse by default unless filters are active (based on initial filters)
  const [isExpanded, setIsExpanded] = useState(hasActiveFilters);

  // Update expansion state when filters change from URL
  useEffect(() => {
    const hasFilters =
      initialFilters.type.length > 0 || initialFilters.category.length > 0;
    if (hasFilters && !isExpanded) {
      setIsExpanded(true);
    }
  }, [initialFilters, isExpanded]);

  // Function to collapse filter when reset is called
  const handleReset = () => {
    setIsExpanded(false);
  };

  return (
    <div className="mb-4">
      <Button
        variant="outline"
        className="w-full justify-between"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          <span>Filters</span>
          {hasActiveFilters && (
            <span className="text-sm text-muted-foreground">
              ({initialFilters.category.length + initialFilters.type.length})
            </span>
          )}
        </div>
        {isExpanded ? (
          <ChevronUp className="h-4 w-4" />
        ) : (
          <ChevronDown className="h-4 w-4" />
        )}
      </Button>

      {isExpanded && (
        <div className="mt-4 space-y-4 rounded-lg bg-muted/50 p-4">
          <WorkshopFiltersCore
            initialFilters={initialFilters}
            categories={categories}
            mode={mode}
            onReset={handleReset}
          />
        </div>
      )}
    </div>
  );
}
