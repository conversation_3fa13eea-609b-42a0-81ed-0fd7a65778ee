"use client";

import { Clock, MapPin } from "lucide-react";
import Image from "next/image";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import { IconText } from "@/components/ui/icon-text";
import { Section } from "@/components/ui/section";
import { formatSnakeCase, getVenueTypeDisplay } from "@/lib/utils";
import type { WorkshopWithProvider } from "@/types/workshop";

interface WorkshopCardProps {
  workshop: WorkshopWithProvider;
  href: string;
}

export function WorkshopCard({ workshop, href }: WorkshopCardProps) {
  return (
    <>
      <a href={href} className="block h-full">
        <Card className="flex h-full flex-col overflow-hidden transition-all duration-200 hover:shadow-md">
          <CardHeader className="relative flex-none p-0">
            <div className="relative aspect-video w-full overflow-hidden bg-muted">
              {workshop.image_url ? (
                <Image
                  src={workshop.image_url}
                  alt={workshop.name}
                  fill
                  className="object-cover transition-transform duration-300"
                  onError={(e) => {
                    // Fallback to a placeholder if image fails to load
                    e.currentTarget.style.display = "none";
                  }}
                />
              ) : null}
              <div className="absolute bottom-3 right-3 flex items-center gap-1 rounded-full bg-black/70 px-3 py-1.5 text-sm font-semibold text-white shadow-md backdrop-blur-sm">
                <span>
                  {new Intl.NumberFormat("en-AU", {
                    style: "currency",
                    currency: workshop.currency || "AUD",
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2,
                  }).format(Number(workshop.price))}
                </span>
                {workshop.pricing_model === "per_person" && (
                  <span className="text-xs font-normal text-gray-300">pp</span>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="flex-1 px-4 py-4">
            <Section spacing="tight">
              <h3 className="text-lg font-semibold">{workshop.name}</h3>
              <IconText
                icon={MapPin}
                text={`${getVenueTypeDisplay(workshop.venue_type)} · ${formatSnakeCase(workshop.format)}`}
                className="text-sm text-muted-foreground [&>svg]:text-blue-500 dark:[&>svg]:text-blue-400"
                spacing="tight"
              />
              {workshop.duration && (
                <IconText
                  icon={Clock}
                  text={workshop.duration}
                  className="text-sm text-muted-foreground [&>svg]:text-amber-500 dark:[&>svg]:text-amber-400"
                  spacing="tight"
                />
              )}
            </Section>
          </CardContent>
          <CardFooter className="flex-none border-t bg-muted/20 px-4 py-4">
            <div className="flex w-full items-center justify-between">
              <div className="flex items-center gap-2">
                <Avatar className="h-7 w-7 border border-muted-foreground/10 shadow-sm">
                  <AvatarImage
                    src={workshop.provider_organizations?.profile_photo_url}
                    alt={
                      workshop.provider_organizations?.name || "Provider logo"
                    }
                    width={28}
                    height={28}
                  />
                  <AvatarFallback className="bg-primary/10 text-primary">
                    {workshop.provider_organizations?.name?.[0] || "P"}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium">
                  {workshop.provider_organizations?.name}
                </span>
              </div>
            </div>
          </CardFooter>
        </Card>
      </a>
    </>
  );
}
