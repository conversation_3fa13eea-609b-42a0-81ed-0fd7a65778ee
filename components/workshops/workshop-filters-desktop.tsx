"use client";

import type { WorkshopFormat } from "@/types/types";

import { WorkshopFiltersCore } from "./workshop-filters-core";

interface WorkshopFiltersDesktopProps {
  initialFilters: {
    type: WorkshopFormat[];
    category: string[];
    page?: number;
  };
  categories: { id: string; name: string }[];
  mode: "desktop" | "mobile";
}

export function WorkshopFiltersDesktop({
  initialFilters,
  categories,
  mode,
}: WorkshopFiltersDesktopProps) {
  return (
    <WorkshopFiltersCore
      initialFilters={initialFilters}
      categories={categories}
      mode={mode}
    />
  );
}
