"use client";

import type { WorkshopFormat } from "@/types/types";

import { WorkshopFiltersDesktop } from "./workshop-filters-desktop";
import { WorkshopFiltersMobile } from "./workshop-filters-mobile";

interface WorkshopFiltersClientProps {
  initialFilters: {
    type: WorkshopFormat[];
    category: string[];
    page?: number;
  };
  categories: { id: string; name: string }[];
  mode: "desktop" | "mobile";
}

export function WorkshopFiltersClient({
  initialFilters,
  categories,
  mode,
}: WorkshopFiltersClientProps) {
  if (mode === "mobile") {
    return (
      <WorkshopFiltersMobile
        initialFilters={initialFilters}
        categories={categories}
        mode={mode}
      />
    );
  }

  return (
    <WorkshopFiltersDesktop
      initialFilters={initialFilters}
      categories={categories}
      mode={mode}
    />
  );
}
