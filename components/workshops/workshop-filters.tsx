import { WorkshopFiltersClient } from "@/components/workshops/workshop-filters-client";
import { workshopSearchSchema } from "@/lib/schemas";
import { parseSearchParams } from "@/lib/utils";

interface WorkshopFiltersProps {
  searchParams?: { [key: string]: string | string[] | undefined };
  categories: { id: string; name: string }[];
  mode: "desktop" | "mobile";
}

export async function WorkshopFilters({
  searchParams,
  categories,
  mode,
}: WorkshopFiltersProps) {
  // Validate search parameters on the server using Zod
  const validParams = parseSearchParams(searchParams, workshopSearchSchema);

  // Extract the validated parameters
  const initialFilters = {
    type: validParams.type
      ? Array.isArray(validParams.type)
        ? validParams.type
        : [validParams.type]
      : [],

    category: validParams.category
      ? Array.isArray(validParams.category)
        ? validParams.category
        : [validParams.category]
      : [],
    page: validParams.page,
  };

  // Pass the validated parameters to the client component
  return (
    <WorkshopFiltersClient
      initialFilters={initialFilters}
      categories={categories}
      mode={mode}
    />
  );
}
