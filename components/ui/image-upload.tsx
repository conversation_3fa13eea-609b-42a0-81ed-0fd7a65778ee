import { <PERSON><PERSON><PERSON>ir<PERSON>, X } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

import { Alert, AlertDescription } from "./alert";
import { Button } from "./button";
import { UploadDropzone } from "./upload-thing";

interface ImageUploadProps {
  value: string;
  onChange: (url: string) => void;
  disabled?: boolean;
  endpoint: "workshopImage" | "organizationProfilePhoto";
}

interface UploadResult {
  url: string;
  name: string;
  size: number;
  key: string;
  serverData: { uploadedBy: string };
}

export function ImageUpload({
  value,
  onChange,
  disabled,
  endpoint,
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  const handleRemoveImage = () => {
    onChange("");
  };

  return (
    <div className="w-full space-y-4">
      {value ? (
        <div className="relative aspect-video overflow-hidden rounded-md border border-border">
          <div className="absolute right-2 top-2 z-10">
            <Button
              type="button"
              variant="destructive"
              size="icon"
              onClick={handleRemoveImage}
              disabled={disabled}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <Image
            src={value}
            alt="Uploaded image"
            fill
            className="object-cover"
          />
        </div>
      ) : (
        <div className="space-y-2 rounded-md border-2 border-dashed border-border p-4">
          {isUploading ? (
            <div className="flex h-60 items-center justify-center">
              <p className="animate-pulse text-sm text-muted-foreground">
                Uploading...
              </p>
            </div>
          ) : (
            <UploadDropzone
              endpoint={endpoint}
              onUploadBegin={() => {
                setIsUploading(true);
                setUploadError(null);
              }}
              onClientUploadComplete={(res: UploadResult[]) => {
                setIsUploading(false);
                if (res && res[0]) {
                  onChange(res[0].url);
                }
              }}
              onUploadError={(error: Error) => {
                setIsUploading(false);

                // Handle specific error types
                if (error.message.includes("FileSizeMismatch")) {
                  setUploadError(
                    "File size exceeds the 4MB limit. Please upload a smaller image.",
                  );
                } else {
                  setUploadError(`Upload failed: ${error.message}`);
                }
              }}
              config={{ mode: "auto", appendOnPaste: true }}
              className="h-60 ut-button:bg-primary ut-button:text-primary-foreground ut-button:hover:bg-primary/90"
            />
          )}
          {uploadError && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{uploadError}</AlertDescription>
            </Alert>
          )}
        </div>
      )}
    </div>
  );
}
