import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const iconTextVariants = cva("flex", {
  variants: {
    spacing: {
      tight: "gap-1",
      default: "gap-2",
      loose: "gap-3",
    },
    iconSize: {
      sm: "[&>svg]:h-3 [&>svg]:w-3",
      default: "[&>svg]:h-4 [&>svg]:w-4",
      lg: "[&>svg]:h-5 [&>svg]:w-5",
    },
    align: {
      center: "items-center",
      start: "items-start",
    },
  },
  defaultVariants: {
    spacing: "default",
    iconSize: "default",
    align: "center",
  },
});

export interface IconTextProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof iconTextVariants> {
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  text: React.ReactNode;
}

const IconText = React.forwardRef<HTMLDivElement, IconTextProps>(
  (
    { className, spacing, iconSize, align, icon: Icon, text, ...props },
    ref,
  ) => {
    return (
      <div
        ref={ref}
        className={cn(
          iconTextVariants({ spacing, iconSize, align }),
          className,
        )}
        {...props}
      >
        <Icon className={cn("flex-shrink-0", align === "start" && "mt-0.5")} />
        {text}
      </div>
    );
  },
);
IconText.displayName = "IconText";

export { IconText, iconTextVariants };
