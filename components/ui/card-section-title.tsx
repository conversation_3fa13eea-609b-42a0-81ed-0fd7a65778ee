import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const cardSectionTitleVariants = cva("font-medium text-muted-foreground", {
  variants: {
    size: {
      xs: "text-xs",
      sm: "text-sm",
      default: "text-sm",
      base: "text-base",
    },
    spacing: {
      none: "",
      sm: "mb-1",
      default: "mb-2",
      lg: "mb-3",
    },
  },
  defaultVariants: {
    size: "default",
    spacing: "default",
  },
});

export interface CardSectionTitleProps
  extends React.HTMLAttributes<HTMLHeadingElement>,
    VariantProps<typeof cardSectionTitleVariants> {
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
}

const CardSectionTitle = React.forwardRef<
  HTMLHeadingElement,
  CardSectionTitleProps
>(({ className, size, spacing, as: Comp = "h2", ...props }, ref) => {
  return (
    <Comp
      ref={ref}
      className={cn(cardSectionTitleVariants({ size, spacing }), className)}
      {...props}
    />
  );
});
CardSectionTitle.displayName = "CardSectionTitle";

export { CardSectionTitle, cardSectionTitleVariants };
