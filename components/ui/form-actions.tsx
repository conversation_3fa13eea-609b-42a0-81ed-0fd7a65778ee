import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const formActionsVariants = cva("flex", {
  variants: {
    layout: {
      row: "flex-row justify-end space-x-4",
      column: "flex-col space-y-3",
      "column-sm": "flex-col space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0",
    },
    width: {
      auto: "",
      full: "w-full",
      "full-sm": "w-full sm:w-auto",
    },
  },
  defaultVariants: {
    layout: "row",
    width: "auto",
  },
});

export interface FormActionsProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof formActionsVariants> {}

const FormActions = React.forwardRef<HTMLDivElement, FormActionsProps>(
  ({ className, layout, width, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(formActionsVariants({ layout, width }), className)}
        {...props}
      />
    );
  },
);
FormActions.displayName = "FormActions";

export { FormActions, formActionsVariants };
