import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from "./form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./select";

interface CityCountrySelectProps {
  onCityChange: (value: string) => void;
  onCountryChange: (value: string) => void;
  cityValue: string | null | undefined;
  countryValue: string | null | undefined;
  label: string;
  placeholder: string;
  description: string;
}

interface LocationOption {
  label: string;
  city: string;
  country: string;
}

const LOCATION_OPTIONS: LocationOption[] = [
  { label: "Singapore", city: "Singapore", country: "Singapore" },
  {
    label: "Kuala Lumpur, Malaysia",
    city: "Kuala Lumpur",
    country: "Malaysia",
  },
];

export function CityCountrySelect({
  onCityChange,
  onCountryChange,
  cityValue,
  countryValue,
  label,
  placeholder,
  description,
}: CityCountrySelectProps) {
  // Find the current selected value
  const currentValue = LOCATION_OPTIONS.find(
    (option) =>
      cityValue &&
      countryValue &&
      option.city === cityValue &&
      option.country === countryValue,
  );

  const handleValueChange = (combinedValue: string) => {
    // Find the selected option
    const option = LOCATION_OPTIONS.find((opt) => opt.label === combinedValue);
    if (option) {
      onCityChange(option.city);
      onCountryChange(option.country);
    }
  };

  return (
    <FormItem>
      <FormLabel>{label}</FormLabel>
      <FormControl>
        <Select onValueChange={handleValueChange} value={currentValue?.label}>
          <SelectTrigger>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {LOCATION_OPTIONS.map((option) => (
              <SelectItem key={option.label} value={option.label}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </FormControl>
      <FormDescription>{description}</FormDescription>
      <FormMessage />
    </FormItem>
  );
}
