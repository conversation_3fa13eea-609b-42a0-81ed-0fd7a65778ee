import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const sectionVariants = cva("", {
  variants: {
    spacing: {
      tight: "space-y-2",
      normal: "space-y-4",
      loose: "space-y-6",
    },
  },
  defaultVariants: {
    spacing: "normal",
  },
});

export interface SectionProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof sectionVariants> {}

const Section = React.forwardRef<HTMLDivElement, SectionProps>(
  ({ className, spacing, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(sectionVariants({ spacing }), className)}
        {...props}
      />
    );
  },
);
Section.displayName = "Section";

export { Section, sectionVariants };
