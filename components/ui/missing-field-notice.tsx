import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { StatusIndicator } from "@/components/ui/status-indicator";
import { cn } from "@/lib/utils";

const missingFieldNoticeVariants = cva("flex items-center", {
  variants: {
    spacing: {
      tight: "gap-1",
      default: "gap-2",
      loose: "gap-3",
    },
    textStyle: {
      muted: "text-muted-foreground",
      default: "",
      warning: "text-amber-600",
      error: "text-red-600",
    },
  },
  defaultVariants: {
    spacing: "default",
    textStyle: "muted",
  },
});

export interface MissingFieldNoticeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof missingFieldNoticeVariants> {
  children: React.ReactNode;
  status?: "missing" | "warning" | "success" | "info";
}

const MissingFieldNotice = React.forwardRef<
  HTMLDivElement,
  MissingFieldNoticeProps
>(
  (
    { className, spacing, textStyle, status = "missing", children, ...props },
    ref,
  ) => {
    return (
      <div
        ref={ref}
        className={cn(
          missingFieldNoticeVariants({ spacing, textStyle }),
          className,
        )}
        {...props}
      >
        <StatusIndicator status={status} />
        <span>{children}</span>
      </div>
    );
  },
);
MissingFieldNotice.displayName = "MissingFieldNotice";

export { MissingFieldNotice, missingFieldNoticeVariants };
