import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@/lib/utils";

const statusIndicatorVariants = cva("rounded-full", {
  variants: {
    status: {
      missing: "bg-red-600",
      warning: "bg-amber-500",
      success: "bg-green-600",
      info: "bg-blue-600",
    },
    size: {
      sm: "h-1.5 w-1.5",
      default: "h-2 w-2",
      lg: "h-3 w-3",
    },
  },
  defaultVariants: {
    status: "missing",
    size: "default",
  },
});

export interface StatusIndicatorProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusIndicatorVariants> {}

const StatusIndicator = React.forwardRef<HTMLDivElement, StatusIndicatorProps>(
  ({ className, status, size, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(statusIndicatorVariants({ status, size }), className)}
        {...props}
      />
    );
  },
);
StatusIndicator.displayName = "StatusIndicator";

export { StatusIndicator, statusIndicatorVariants };
