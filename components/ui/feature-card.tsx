import Image from "next/image";

import { cn } from "@/lib/utils";

interface FeatureCardProps {
  title: string;
  description: string;
  icon: string;
  bgColor: BgColor;
  className?: string;
}

// Explicit mapping to ensure Tailwind CSS classes are not purged
const bgColorMap = {
  // Standard Tailwind colors
  "bg-orange-100": "bg-orange-100",
  "bg-green-100": "bg-green-100",
  "bg-yellow-100": "bg-yellow-100",
  "bg-blue-100": "bg-blue-100",
  "bg-purple-100": "bg-purple-100",
  "bg-pink-100": "bg-pink-100",
  "bg-red-100": "bg-red-100",
  "bg-indigo-100": "bg-indigo-100",
  // Custom hex colors
  "bg-[#FFEAE2]": "bg-[#FFEAE2]",
  "bg-[#F9FFEF]": "bg-[#F9FFEF]",
  "bg-[#FFF9EE]": "bg-[#FFF9EE]",
} as const;

type BgColor = keyof typeof bgColorMap;

export function FeatureCard({
  title,
  description,
  icon,
  bgColor,
  className,
}: FeatureCardProps) {
  const mappedBgColor = bgColorMap[bgColor];

  return (
    <div
      className={cn(
        "relative rounded-xl border border-gray-100 bg-white p-8 text-center shadow-sm",
        className,
      )}
    >
      <div
        className={cn(
          "mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full shadow-md",
          mappedBgColor,
        )}
      >
        <Image src={icon} alt="" width={40} height={40} className="h-10 w-10" />
      </div>
      <h3 className="mb-4 text-xl font-bold text-gray-900">{title}</h3>
      <p className="leading-relaxed text-gray-600">{description}</p>
    </div>
  );
}
