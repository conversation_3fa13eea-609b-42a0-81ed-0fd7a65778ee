"use client";

import {
  Calendar<PERSON><PERSON>,
  CheckCircle2,
  <PERSON>I<PERSON>,
  <PERSON>py,
  CreditCardIcon,
  MapPinIcon,
} from "lucide-react";
import { useState } from "react";
import { Temporal } from "temporal-polyfill";

import { PaymentDialog } from "@/components/messages/payment-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { IconText } from "@/components/ui/icon-text";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { formatCurrency } from "@/lib/format";
import { calculatePaymentBreakdown } from "@/lib/payment-utils";
import { formatDate, formatTime } from "@/lib/temporal-utils";
import { cn } from "@/lib/utils";
import type { QuoteWithWorkshopDetails } from "@/types/quote";
import type { QuoteStatus } from "@/types/types";

/**
 * Get a human-readable status label and color for a quote status
 */
const quoteStatusInfo: {
  [status in QuoteStatus | "default"]: {
    label: string;
    badgeClassName: string;
    borderClassName: string;
  };
} = {
  pending: {
    label: "Pending",
    badgeClassName: "bg-amber-400 text-black",
    borderClassName: "border-amber-400",
  },
  rejected: {
    label: "Rejected",
    badgeClassName: "bg-rose-600 text-white",
    borderClassName: "border-rose-600",
  },
  expired: {
    label: "Expired",
    badgeClassName: "bg-slate-500 text-white",
    borderClassName: "border-slate-500",
  },
  paid: {
    label: "Paid",
    badgeClassName: "bg-emerald-600 text-white",
    borderClassName: "border-emerald-600",
  },
  superceded: {
    label: "Replaced",
    badgeClassName: "bg-indigo-600 text-white",
    borderClassName: "border-indigo-600",
  },
  cancelled: {
    label: "Cancelled",
    badgeClassName: "bg-slate-500 text-white",
    borderClassName: "border-slate-500",
  },
  payment_intent_created: {
    label: "Ready to Pay",
    badgeClassName: "bg-blue-500 text-white",
    borderClassName: "border-blue-500",
  },
  payment_processing: {
    label: "Processing Payment",
    badgeClassName: "bg-blue-600 text-white",
    borderClassName: "border-blue-600",
  },
  payment_failed: {
    label: "Payment Failed",
    badgeClassName: "bg-red-600 text-white",
    borderClassName: "border-red-600",
  },
  default: {
    label: "Unknown",
    badgeClassName: "bg-slate-500 text-white",
    borderClassName: "border-slate-500",
  },
};

interface QuoteCardProps {
  quote: QuoteWithWorkshopDetails;
  isOwnMessage: boolean;
  isClient: boolean;
  onDuplicate?: (quote: QuoteWithWorkshopDetails) => void;
  isDraft?: boolean;
  trashButton?: React.ReactNode;
}

/**
 * Renders a detailed card view of a quote with interactive actions and payment breakdown, adapting its display for clients and providers.
 *
 * Shows workshop and organization details, location, proposed date and time, price, and optional notes. Displays a "DRAFT" watermark if applicable, an optional trash button, and a duplicate button for providers. For clients, provides an action button to accept or pay for the quote based on its status, along with a payment dialog. Providers see a breakdown of service fee and earnings below the card.
 */
export function QuoteCard({
  quote,
  isOwnMessage,
  isClient,
  onDuplicate,
  isDraft = false,
  trashButton,
}: QuoteCardProps) {
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);

  const statusInfo = quoteStatusInfo[quote.status];
  const workshopName = quote.workshops?.name || "Workshop";
  const organizationName = quote.provider_organizations?.name;

  // Determine if the current user is a client and can take action on this quote
  const canActOnQuote =
    isClient &&
    !isOwnMessage &&
    ["pending", "payment_intent_created", "payment_failed"].includes(
      quote.status,
    );

  const getQuoteAction = () => {
    if (!canActOnQuote) return null;

    switch (quote.status) {
      case "pending":
        return {
          text: "Accept & Pay",
          icon: CheckCircle2,
          className: "text-green-500 hover:bg-green-50 hover:text-green-600",
        };
      case "payment_failed":
        return {
          text: "Retry Payment",
          icon: CreditCardIcon,
          className: "text-red-500 hover:bg-red-50 hover:text-red-600",
        };
      case "payment_intent_created":
        return {
          text: "Complete Payment",
          icon: CreditCardIcon,
          className: "text-blue-500 hover:bg-blue-50 hover:text-blue-600",
        };
      default:
        return null;
    }
  };

  const quoteAction = getQuoteAction();
  const isPending = quote.status === "pending";

  // Determine if duplicate button should show - for providers on any quote
  const canDuplicate = !isClient && onDuplicate;

  const handleAcceptQuote = async () => {
    setShowPaymentDialog(true);
  };

  const handleDuplicate = () => {
    onDuplicate?.(quote);
  };

  // Calculate payment breakdown for providers
  const paymentBreakdown = !isClient
    ? calculatePaymentBreakdown(quote.price)
    : null;

  return (
    <>
      <Card
        className={cn(
          "relative mt-2 w-full max-w-md overflow-hidden border shadow",
          !isPending && "border-2",
          !isPending && statusInfo.borderClassName,
        )}
      >
        {isDraft && (
          <div className="pointer-events-none absolute inset-0 z-10 flex items-center justify-center">
            <div className="rotate-12 select-none text-5xl font-black tracking-wider text-gray-600/10">
              DRAFT
            </div>
          </div>
        )}
        {trashButton && (
          <div className="absolute right-2 top-2 z-20">{trashButton}</div>
        )}
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">
            {workshopName}{" "}
            {organizationName && (
              <span className="mt-1 text-sm text-muted-foreground">
                &middot; {organizationName}
              </span>
            )}
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-2 pb-1 text-sm">
          <IconText icon={MapPinIcon} text={quote.location} align="start" />

          <div className="flex items-center gap-2">
            <IconText
              icon={CalendarIcon}
              text={formatDate(Temporal.Instant.from(quote.proposed_datetime))}
              spacing="tight"
            />
            <IconText
              icon={ClockIcon}
              text={formatTime(Temporal.Instant.from(quote.proposed_datetime))}
              spacing="tight"
            />
          </div>

          <IconText
            icon={CreditCardIcon}
            text={
              <span className="font-medium">
                {formatCurrency(quote.price, quote.currency)}
              </span>
            }
          />

          {quote.notes && (
            <div className="mt-2 border-t pt-2 text-sm">
              <p className="whitespace-pre-line">{quote.notes}</p>
            </div>
          )}
        </CardContent>

        <CardFooter className="mb-0 mt-2 flex flex-col gap-2 text-xs text-muted-foreground">
          <div className="flex w-full items-center justify-between">
            <div className="flex-1">
              {quote.status !== "pending" && (
                <Badge className={statusInfo.badgeClassName}>
                  {statusInfo.label}
                </Badge>
              )}
            </div>

            {/* Duplicate button for providers */}
            {canDuplicate && (
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-muted-foreground hover:text-foreground"
                      onClick={handleDuplicate}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    <p>Create new quote from this one</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>

          {/* Action button for clients */}
          {quoteAction && (
            <div className="flex w-full justify-center">
              <Button
                variant="outline"
                size="sm"
                className={quoteAction.className}
                onClick={handleAcceptQuote}
              >
                <IconText
                  icon={quoteAction.icon}
                  text={quoteAction.text}
                  spacing="tight"
                />
              </Button>
            </div>
          )}
        </CardFooter>

        <PaymentDialog
          quote={quote}
          open={showPaymentDialog}
          onOpenChange={setShowPaymentDialog}
        />
      </Card>

      {/* Payment breakdown for providers */}
      {paymentBreakdown && (
        <div className="mt-2 w-full max-w-md text-xs text-muted-foreground">
          <div className="flex justify-between">
            <span>Service fee:</span>
            <span>
              {formatCurrency(paymentBreakdown.serviceFee, quote.currency)}
            </span>
          </div>
          <div className="flex justify-between font-medium">
            <span>You receive:</span>
            <span>
              {formatCurrency(
                paymentBreakdown.providerEarnings,
                quote.currency,
              )}
            </span>
          </div>
        </div>
      )}
    </>
  );
}
