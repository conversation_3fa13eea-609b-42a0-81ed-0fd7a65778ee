name: <PERSON>

on:
  workflow_dispatch:
    inputs:
      code:
        description: 'Authorization code (leave empty for step 1)'
        required: false

permissions:
  actions: write  # Required for cache management
  contents: read  # Required for basic repository access

jobs:
  auth:
    runs-on: ubuntu-latest
    steps:
      - uses: grll/claude-code-login@v1
        with:
          code: ${{ inputs.code }}
          secrets_admin_pat: ${{ secrets.SECRETS_ADMIN_PAT }}
