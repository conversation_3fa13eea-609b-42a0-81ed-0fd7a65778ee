name: <PERSON> Assistant - Authorized Users Only

on:
  issue_comment:
    types: [created]
  pull_request_review_comment:
    types: [created]
  issues:
    types: [opened, assigned]
  pull_request_review:
    types: [submitted]

jobs:
  claude-code-action:
    # Only respond to @claude mentions from <PERSON><PERSON><PERSON><PERSON><PERSON>
    if: |
      (
        (github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude')) ||
        (github.event_name == 'pull_request_review_comment' && contains(github.event.comment.body, '@claude')) ||
        (github.event_name == 'pull_request_review' && contains(github.event.review.body, '@claude')) ||
        (github.event_name == 'issues' && contains(github.event.issue.body, '@claude'))
      ) && github.actor == 'KevinWuWon'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      issues: read
      id-token: write
      actions: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Install pnpm
        run: npm install -g pnpm

      - name: <PERSON> Claude PR Action
        uses: grll/claude-code-action@beta
        with:
          use_oauth: true
          claude_access_token: ${{ secrets.CLAUDE_ACCESS_TOKEN }}
          claude_refresh_token: ${{ secrets.CLAUDE_REFRESH_TOKEN }}
          claude_expires_at: ${{ secrets.CLAUDE_EXPIRES_AT }}
          secrets_admin_pat: ${{ secrets.SECRETS_ADMIN_PAT }}
          timeout_minutes: "60"
          model: "claude-sonnet-4-20250514"
          mcp_config: |
            {
              "mcpServers": {
                "context7": {
                  "command": "npx",
                  "args": ["-y", "@upstash/context7-mcp"]
                },
                "fetch": {
                  "command": "npx",
                  "args": ["@tokenizin/mcp-npx-fetch"]
                }
              }
            }
          allowed_tools: |
            Bash(pnpm run check)
            Bash(pnpm check)
            Bash(pnpm install:*)
            Bash(pnpm add:*)
            mcp__context7__resolve-library-id
            mcp__context7__get-library-docs
            mcp__fetch__fetch_html
            mcp__fetch__fetch_json
            mcp__fetch__fetch_txt
            mcp__fetch__fetch_markdown
