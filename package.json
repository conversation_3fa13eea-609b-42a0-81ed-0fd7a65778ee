{"name": "pulse-space", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "cf:preview": "npx opennextjs-cloudflare build && npx opennextjs-cloudflare preview", "cf:deploy": "npx opennextjs-cloudflare build && npx opennextjs-cloudflare deploy", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "typecheck": "tsc --noEmit", "lint": "eslint . --fix && next lint --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md,mdx,css,scss}\"", "check": "pnpm typecheck && pnpm lint && pnpm format", "depcheck": "depcheck", "test:e2e": "playwright test --retries 1", "test:e2e:ui": "playwright test --ui --retries 1", "test:e2e:report": "playwright show-report", "cleanup:test-users": "node scripts/cleanup-test-users.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@next/third-parties": "^15.3.3", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@supabase/ssr": "0.6.1", "@tanstack/react-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.81.2", "@types/nodemailer": "^6.4.17", "@uploadthing/react": "^7.3.1", "class-variance-authority": "0.7.1", "clsx": "2.1.1", "date-fns": "4.1.0", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.511.0", "next": "15.4.0-canary.85", "next-themes": "0.4.6", "nodemailer": "^7.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "sonner": "2.0.3", "stripe": "^18.2.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "1.0.7", "temporal-polyfill": "^0.3.0", "uploadthing": "^7.7.2", "zod": "^3.25.20"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@next/eslint-plugin-next": "^15.3.3", "@opennextjs/cloudflare": "^1.0.4", "@playwright/test": "^1.52.0", "@supabase/supabase-js": "^2.49.8", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "depcheck": "1.4.7", "dotenv": "^16.5.0", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "12.1.1", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "wrangler": "^4.16.0"}}